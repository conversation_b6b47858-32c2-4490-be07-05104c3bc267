@echo off
chcp 65001 >nul
title نظام إدارة التراخيص - مؤسسة وقود المستقبل

echo.
echo ========================================
echo    نظام إدارة التراخيص - الإصدار 3.0.0
echo ========================================
echo.

echo 🔐 بدء تشغيل نظام إدارة التراخيص المتقدم...
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit /b 1
)

REM التحقق من وجود npm
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: npm غير متوفر
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
echo ✅ npm متوفر
echo.

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
)

echo 🎯 تشغيل نظام إدارة التراخيص...
echo.
echo 📋 معلومات النظام:
echo.
echo 🔑 للعملاء - طلب تفعيل:
echo    - افتح التطبيق واختر "طلب تفعيل"
echo    - أدخل بياناتك واختر نوع الترخيص
echo    - انتظر الموافقة من المدير
echo.
echo 👨‍💼 للمدير - إدارة التراخيص:
echo    - اسم المستخدم: admin
echo    - كلمة المرور: admin123
echo    - ستفتح لوحة تحكم المدير تلقائياً
echo.
echo 🌟 الميزات الجديدة:
echo    ✓ نظام تحقق متقدم من الجهاز
echo    ✓ إدارة شاملة للتراخيص
echo    ✓ لوحة تحكم المدير
echo    ✓ قاعدة بيانات محسنة ومشفرة
echo    ✓ نسخ احتياطية تلقائية
echo    ✓ دعم جميع الولايات الجزائرية (58 ولاية)
echo.
echo 🔒 أمان التراخيص:
echo    - كل ترخيص يعمل على جهاز واحد فقط
echo    - تشفير متقدم للبيانات
echo    - تتبع دقيق لاستخدام التراخيص
echo.

REM تشغيل التطبيق
npm start

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo.
    echo 🔧 حلول مقترحة:
    echo    1. تأكد من إغلاق أي نسخة أخرى من التطبيق
    echo    2. أعد تشغيل الكمبيوتر
    echo    3. احذف مجلد node_modules وأعد تثبيت التبعيات:
    echo       rmdir /s node_modules
    echo       npm install
    echo    4. تحقق من أن المنفذ 3000 غير مستخدم
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق نظام إدارة التراخيص بنجاح
echo 💾 تم حفظ جميع البيانات تلقائياً
echo.
echo 📁 مواقع الملفات المهمة:
echo    - البيانات: %USERPROFILE%\FutureFuelData\
echo    - النسخ الاحتياطية: %USERPROFILE%\FutureFuelData\backups\
echo    - السجلات: %USERPROFILE%\FutureFuelData\license-logs.json
echo.
pause
