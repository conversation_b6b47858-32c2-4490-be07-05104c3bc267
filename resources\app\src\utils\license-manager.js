// نظام إدارة التراخيص

class LicenseManager {
    constructor() {
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;
        this.machineId = null;
        this.initialized = false;
    }

    // تهيئة نظام التراخيص
    async initialize() {
        if (this.initialized) return;

        try {
            this.machineId = await this.generateMachineId();
            this.initialized = true;
            console.log('تم تهيئة نظام التراخيص بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة نظام التراخيص:', error);
            throw error;
        }
    }

    // إنشاء معرف فريد للجهاز
    async generateMachineId() {
        try {
            if (this.isElectron) {
                const os = require('os');
                const crypto = require('crypto');
                
                // جمع معلومات الجهاز
                const machineInfo = {
                    hostname: os.hostname(),
                    platform: os.platform(),
                    arch: os.arch(),
                    cpus: os.cpus().length,
                    totalmem: os.totalmem(),
                    networkInterfaces: Object.keys(os.networkInterfaces())
                };
                
                // إنشاء hash فريد
                const hash = crypto.createHash('sha256');
                hash.update(JSON.stringify(machineInfo));
                return hash.digest('hex');
            } else {
                // للمتصفح - استخدام fingerprinting بسيط
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Machine ID', 2, 2);
                
                const fingerprint = canvas.toDataURL() + 
                    navigator.userAgent + 
                    navigator.language + 
                    screen.width + 'x' + screen.height;
                
                // إنشاء hash بسيط
                let hash = 0;
                for (let i = 0; i < fingerprint.length; i++) {
                    const char = fingerprint.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                return Math.abs(hash).toString(16);
            }
        } catch (error) {
            console.error('خطأ في إنشاء معرف الجهاز:', error);
            return 'unknown-machine';
        }
    }

    // إنشاء طلب تفعيل جديد
    async createActivationRequest(requestData) {
        if (!this.initialized) await this.initialize();

        const request = {
            id: this.generateRequestId(),
            customerName: requestData.customerName,
            phoneNumber: requestData.phoneNumber,
            state: requestData.state,
            licenseType: requestData.licenseType,
            machineId: this.machineId,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        try {
            // حفظ الطلب في قاعدة البيانات
            await this.saveActivationRequest(request);
            return { success: true, requestId: request.id };
        } catch (error) {
            console.error('خطأ في إنشاء طلب التفعيل:', error);
            return { success: false, error: 'فشل في إرسال طلب التفعيل' };
        }
    }

    // حفظ طلب التفعيل
    async saveActivationRequest(request) {
        if (this.isElectron && window.dbManager) {
            await window.dbManager.addRecord('activationRequests', request);
        } else {
            // حفظ في localStorage كبديل
            const requests = this.getStoredRequests();
            requests.push(request);
            localStorage.setItem('activationRequests', JSON.stringify(requests));
        }
    }

    // الحصول على الطلبات المحفوظة
    getStoredRequests() {
        try {
            const stored = localStorage.getItem('activationRequests');
            return stored ? JSON.parse(stored) : [];
        } catch {
            return [];
        }
    }

    // إنشاء ترخيص جديد
    async generateLicense(requestData, licenseType, duration = null) {
        const licenseKey = this.generateLicenseKey();
        const now = new Date();
        let expiryDate = null;

        // تحديد تاريخ انتهاء الصلاحية
        switch (licenseType) {
            case 'trial':
                expiryDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 يوم
                break;
            case 'monthly':
                expiryDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // شهر
                break;
            case 'yearly':
                expiryDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // سنة
                break;
            case 'lifetime':
                expiryDate = new Date('2099-12-31'); // مدى الحياة
                break;
            default:
                if (duration) {
                    expiryDate = new Date(now.getTime() + duration);
                }
        }

        // الحصول على بصمة الجهاز المتقدمة
        let deviceFingerprint = requestData.machineId;
        if (window.deviceVerification) {
            try {
                deviceFingerprint = await window.deviceVerification.getCurrentFingerprint();
            } catch (error) {
                console.warn('فشل في الحصول على بصمة الجهاز المتقدمة، استخدام المعرف التقليدي');
            }
        }

        const license = {
            id: this.generateLicenseId(),
            licenseKey: licenseKey,
            customerName: requestData.customerName,
            phoneNumber: requestData.phoneNumber,
            state: requestData.state,
            licenseType: licenseType,
            machineId: requestData.machineId, // المعرف التقليدي للتوافق
            deviceFingerprint: deviceFingerprint, // البصمة المتقدمة
            status: 'active',
            issuedAt: now.toISOString(),
            expiryDate: expiryDate ? expiryDate.toISOString() : null,
            createdAt: now.toISOString(),
            updatedAt: now.toISOString()
        };

        // تسجيل الجهاز في نظام التحقق
        if (window.deviceVerification) {
            try {
                await window.deviceVerification.saveDeviceFingerprint(license.id);
            } catch (error) {
                console.warn('فشل في تسجيل بصمة الجهاز:', error);
            }
        }

        return license;
    }

    // إنشاء مفتاح ترخيص
    generateLicenseKey() {
        const segments = [];
        for (let i = 0; i < 4; i++) {
            segments.push(this.generateRandomString(4).toUpperCase());
        }
        return segments.join('-');
    }

    // إنشاء معرف ترخيص
    generateLicenseId() {
        return 'LIC-' + Date.now().toString(36) + '-' + this.generateRandomString(6).toUpperCase();
    }

    // إنشاء معرف طلب
    generateRequestId() {
        return 'REQ-' + Date.now().toString(36) + '-' + this.generateRandomString(6).toUpperCase();
    }

    // إنشاء نص عشوائي
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // التحقق من صحة الترخيص
    async validateLicense(licenseKey) {
        if (!this.initialized) await this.initialize();

        try {
            const license = await this.getLicenseByKey(licenseKey);

            if (!license) {
                return { valid: false, error: 'ترخيص غير موجود' };
            }

            // التحقق من بصمة الجهاز باستخدام نظام التحقق المتقدم
            if (window.deviceVerification) {
                const deviceCheck = await window.deviceVerification.verifyLicenseDevice(
                    license.id,
                    license.deviceFingerprint || license.machineId
                );

                if (!deviceCheck.isValid) {
                    return {
                        valid: false,
                        error: 'هذا الترخيص مخصص لجهاز آخر',
                        deviceInfo: {
                            current: deviceCheck.currentFingerprint,
                            registered: license.deviceFingerprint || license.machineId
                        }
                    };
                }
            } else {
                // التحقق التقليدي من معرف الجهاز
                if (license.machineId !== this.machineId) {
                    return { valid: false, error: 'هذا الترخيص مخصص لجهاز آخر' };
                }
            }

            // التحقق من حالة الترخيص
            if (license.status !== 'active') {
                return { valid: false, error: 'الترخيص غير نشط' };
            }

            // التحقق من تاريخ انتهاء الصلاحية
            if (license.expiryDate) {
                const now = new Date();
                const expiry = new Date(license.expiryDate);

                if (now > expiry) {
                    // تحديث حالة الترخيص إلى منتهي الصلاحية
                    await this.updateLicenseStatus(license.id, 'expired');
                    return { valid: false, error: 'انتهت صلاحية الترخيص' };
                }
            }

            return {
                valid: true,
                license: license,
                daysRemaining: this.calculateDaysRemaining(license.expiryDate)
            };
        } catch (error) {
            console.error('خطأ في التحقق من الترخيص:', error);
            return { valid: false, error: 'خطأ في التحقق من الترخيص' };
        }
    }

    // حساب الأيام المتبقية
    calculateDaysRemaining(expiryDate) {
        if (!expiryDate) return null; // مدى الحياة
        
        const now = new Date();
        const expiry = new Date(expiryDate);
        const diffTime = expiry - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return Math.max(0, diffDays);
    }

    // الحصول على ترخيص بالمفتاح
    async getLicenseByKey(licenseKey) {
        if (this.isElectron && window.dbManager) {
            const licenses = await window.dbManager.getTable('licenses');
            return licenses.find(license => license.licenseKey === licenseKey);
        } else {
            // البحث في localStorage
            const licenses = this.getStoredLicenses();
            return licenses.find(license => license.licenseKey === licenseKey);
        }
    }

    // الحصول على التراخيص المحفوظة
    getStoredLicenses() {
        try {
            const stored = localStorage.getItem('licenses');
            return stored ? JSON.parse(stored) : [];
        } catch {
            return [];
        }
    }

    // تحديث حالة الترخيص
    async updateLicenseStatus(licenseId, newStatus) {
        if (this.isElectron && window.dbManager) {
            await window.dbManager.updateRecord('licenses', licenseId, {
                status: newStatus,
                updatedAt: new Date().toISOString()
            });
        } else {
            // تحديث في localStorage
            const licenses = this.getStoredLicenses();
            const licenseIndex = licenses.findIndex(l => l.id === licenseId);
            if (licenseIndex !== -1) {
                licenses[licenseIndex].status = newStatus;
                licenses[licenseIndex].updatedAt = new Date().toISOString();
                localStorage.setItem('licenses', JSON.stringify(licenses));
            }
        }
    }

    // حفظ ترخيص جديد
    async saveLicense(license) {
        if (this.isElectron && window.dbManager) {
            await window.dbManager.addRecord('licenses', license);
        } else {
            const licenses = this.getStoredLicenses();
            licenses.push(license);
            localStorage.setItem('licenses', JSON.stringify(licenses));
        }
    }

    // الحصول على معلومات الجهاز الحالي
    async getMachineInfo() {
        if (!this.initialized) await this.initialize();
        
        return {
            machineId: this.machineId,
            platform: this.isElectron ? require('os').platform() : navigator.platform,
            timestamp: new Date().toISOString()
        };
    }

    // تشفير البيانات الحساسة
    encryptData(data, key) {
        // تشفير بسيط - يمكن تحسينه لاحقاً
        try {
            const encrypted = btoa(JSON.stringify(data) + key);
            return encrypted;
        } catch {
            return data;
        }
    }

    // فك تشفير البيانات
    decryptData(encryptedData, key) {
        try {
            const decrypted = atob(encryptedData);
            const data = decrypted.replace(key, '');
            return JSON.parse(data);
        } catch {
            return null;
        }
    }
}

// إنشاء مثيل واحد للاستخدام العام
const licenseManager = new LicenseManager();

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LicenseManager, licenseManager };
} else {
    window.LicenseManager = LicenseManager;
    window.licenseManager = licenseManager;
}
