<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم المتقدمة - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        /* سيتم إضافة CSS هنا */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f6fa;
            direction: rtl;
            overflow-x: hidden;
        }

        /* شريط التنقل العلوي */
        .top-navbar {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 1rem 2rem;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .navbar-brand i {
            font-size: 1.5rem;
            color: #f39c12;
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .notification-bell {
            position: relative;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .notification-bell:hover {
            background: rgba(255,255,255,0.1);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: background 0.3s ease;
            position: relative;
        }

        .user-menu:hover {
            background: rgba(255,255,255,0.1);
        }

        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            color: #2c3e50;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 200px;
            display: none;
            z-index: 1001;
        }

        .user-dropdown a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            text-decoration: none;
            color: #2c3e50;
            transition: background 0.3s ease;
        }

        .user-dropdown a:hover {
            background: #f8f9fa;
        }

        /* الشريط الجانبي */
        .sidebar {
            position: fixed;
            top: 70px;
            right: 0;
            width: 280px;
            height: calc(100vh - 70px);
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 999;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-header h3 {
            color: #2c3e50;
            font-size: 1.2rem;
        }

        .sidebar-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #2c3e50;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-item:hover {
            background: #f8f9fa;
            color: #3498db;
        }

        .nav-item.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #f39c12;
        }

        .nav-item i {
            font-size: 1.1rem;
            width: 20px;
        }

        .badge {
            background: #e74c3c;
            color: white;
            border-radius: 12px;
            padding: 0.2rem 0.5rem;
            font-size: 0.7rem;
            margin-right: auto;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-right: 280px;
            margin-top: 70px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }

        .section-header h1 {
            color: #2c3e50;
            font-size: 1.8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        /* الأزرار */
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* بطاقات الإحصائيات */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card.primary {
            border-right: 4px solid #3498db;
        }

        .stat-card.success {
            border-right: 4px solid #27ae60;
        }

        .stat-card.warning {
            border-right: 4px solid #f39c12;
        }

        .stat-card.info {
            border-right: 4px solid #17a2b8;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-card.primary .stat-icon {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .stat-card.success .stat-icon {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .stat-card.warning .stat-icon {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .stat-card.info .stat-icon {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .stat-content h3 {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-content p {
            color: #7f8c8d;
            font-weight: 500;
        }

        /* الرسوم البيانية */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .chart-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .chart-header h3 {
            color: #2c3e50;
            font-size: 1.1rem;
        }

        .chart-container {
            padding: 1.5rem;
            height: 300px;
        }

        /* الجداول */
        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: #f8f9fa;
            padding: 1rem;
            text-align: right;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
        }

        .data-table td {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
            color: #2c3e50;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        /* الفلاتر */
        .filters-bar {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-group label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .filter-group input,
        .filter-group select {
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
            min-width: 200px;
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        /* النوافذ المنبثقة */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            color: #2c3e50;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #7f8c8d;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        /* النماذج */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        /* أنماط إضافية */
        .no-data {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 2rem;
        }

        .license-key {
            background: #f8f9fa;
            padding: 0.3rem 0.6rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.8rem;
            color: #2c3e50;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-badge.approved {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.active {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.deactivated {
            background: #f8d7da;
            color: #721c24;
        }

        .status-badge.expired {
            background: #fff3cd;
            color: #856404;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-small {
            padding: 0.4rem 0.6rem;
            font-size: 0.7rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-small:hover {
            transform: translateY(-1px);
        }

        .btn-small.btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-small.btn-success {
            background: #28a745;
            color: white;
        }

        .btn-small.btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-small.btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-small.btn-primary {
            background: #007bff;
            color: white;
        }

        /* بطاقات العملاء */
        .customers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .customer-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .customer-card:hover {
            transform: translateY(-5px);
        }

        .customer-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .customer-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .customer-info h4 {
            margin: 0;
            color: #2c3e50;
        }

        .customer-info p {
            margin: 0.2rem 0;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .customer-state {
            background: #e9ecef;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            color: #495057;
        }

        .customer-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .customer-stats .stat {
            text-align: center;
        }

        .customer-stats .stat-value {
            display: block;
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .customer-stats .stat-label {
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .customer-actions {
            text-align: center;
        }

        /* إحصائيات العملاء */
        .customers-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .customers-stats .stat-card {
            text-align: center;
            padding: 1rem;
        }

        .customers-stats .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        /* الرسوم البيانية المفصلة */
        .detailed-charts {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .chart-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
        }

        .chart-card.large {
            grid-column: 1 / -1;
        }

        /* خيارات التقرير */
        .report-options {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .option-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .option-group label {
            font-weight: 600;
            color: #2c3e50;
        }

        .option-group input,
        .option-group select {
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
        }

        /* معاينة التقرير */
        .report-preview {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
        }

        .report-summary h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 0.8rem;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .summary-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .summary-value {
            font-weight: 600;
            color: #3498db;
        }

        /* السجلات */
        .logs-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-height: 600px;
            overflow-y: auto;
        }

        .log-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .log-item.info .log-icon {
            background: #17a2b8;
        }

        .log-item.success .log-icon {
            background: #28a745;
        }

        .log-item.warning .log-icon {
            background: #ffc107;
            color: #212529;
        }

        .log-item.error .log-icon {
            background: #dc3545;
        }

        .log-content {
            flex: 1;
        }

        .log-message {
            color: #2c3e50;
            margin-bottom: 0.3rem;
        }

        .log-time {
            color: #7f8c8d;
            font-size: 0.8rem;
        }

        /* الإعدادات */
        .settings-form {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 2rem;
        }

        .settings-group {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e9ecef;
        }

        .settings-group:last-child {
            border-bottom: none;
        }

        .settings-group h3 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .settings-group h3::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #3498db;
            border-radius: 2px;
        }

        /* الأنشطة الأخيرة */
        .recent-activities {
            margin-top: 2rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            background: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        .card-header h3 {
            margin: 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .activities-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .activity-icon.info {
            background: #17a2b8;
        }

        .activity-icon.success {
            background: #28a745;
        }

        .activity-icon.warning {
            background: #ffc107;
            color: #212529;
        }

        .activity-icon.error {
            background: #dc3545;
        }

        .activity-content {
            flex: 1;
        }

        .activity-message {
            color: #2c3e50;
            margin: 0 0 0.3rem 0;
        }

        .activity-time {
            color: #7f8c8d;
            font-size: 0.8rem;
        }

        /* الإشعارات */
        .notifications-panel {
            position: fixed;
            top: 70px;
            left: 20px;
            width: 350px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 1500;
            display: none;
            max-height: 500px;
            overflow: hidden;
        }

        .notifications-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .notifications-header h3 {
            margin: 0;
            color: #2c3e50;
        }

        .notifications-header button {
            background: none;
            border: none;
            color: #3498db;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .notifications-body {
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .notification-item:hover {
            background: #f8f9fa;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        /* إشعارات النظام */
        .notification {
            position: fixed;
            top: 100px;
            left: 20px;
            background: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 2000;
            display: flex;
            align-items: center;
            gap: 1rem;
            min-width: 300px;
            animation: slideInLeft 0.3s ease;
        }

        .notification.success {
            border-right: 4px solid #28a745;
        }

        .notification.error {
            border-right: 4px solid #dc3545;
        }

        .notification.warning {
            border-right: 4px solid #ffc107;
        }

        .notification.info {
            border-right: 4px solid #17a2b8;
        }

        .notification button {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #7f8c8d;
            margin-right: auto;
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                width: 100%;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .sidebar-toggle {
                display: block;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .filters-bar {
                flex-direction: column;
                gap: 1rem;
            }

            .filter-group input,
            .filter-group select {
                min-width: 100%;
            }

            .customers-grid {
                grid-template-columns: 1fr;
            }

            .chart-row {
                grid-template-columns: 1fr;
            }

            .report-options {
                flex-direction: column;
            }

            .summary-grid {
                grid-template-columns: 1fr;
            }

            .notifications-panel {
                left: 10px;
                right: 10px;
                width: auto;
            }

            .notification {
                left: 10px;
                right: 10px;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="top-navbar">
        <div class="navbar-content">
            <div class="navbar-brand">
                <i class="fas fa-gas-pump"></i>
                <span>مؤسسة وقود المستقبل</span>
            </div>
            
            <div class="navbar-actions">
                <div class="notification-bell" onclick="toggleNotifications()">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" id="notificationBadge">0</span>
                </div>
                
                <div class="user-menu" onclick="toggleUserMenu()">
                    <i class="fas fa-user-circle"></i>
                    <span>المدير</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                
                <div class="user-dropdown" id="userDropdown">
                    <a href="#" onclick="openSettings()">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a>
                    <a href="#" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- الشريط الجانبي -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h3>لوحة التحكم</h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="#dashboard" class="nav-item active" data-section="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                <span>الرئيسية</span>
            </a>
            <a href="#activation-requests" class="nav-item" data-section="activation-requests">
                <i class="fas fa-inbox"></i>
                <span>طلبات التفعيل</span>
                <span class="badge" id="requestsBadge">0</span>
            </a>
            <a href="#license-management" class="nav-item" data-section="license-management">
                <i class="fas fa-key"></i>
                <span>إدارة التراخيص</span>
            </a>
            <a href="#customers" class="nav-item" data-section="customers">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="#statistics" class="nav-item" data-section="statistics">
                <i class="fas fa-chart-bar"></i>
                <span>الإحصائيات</span>
            </a>
            <a href="#reports" class="nav-item" data-section="reports">
                <i class="fas fa-file-alt"></i>
                <span>التقارير</span>
            </a>
            <a href="#logs" class="nav-item" data-section="logs">
                <i class="fas fa-list"></i>
                <span>السجلات</span>
            </a>
            <a href="#settings" class="nav-item" data-section="settings">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- قسم الرئيسية -->
        <section id="dashboard-section" class="content-section active">
            <div class="section-header">
                <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم الرئيسية</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- بطاقات الإحصائيات السريعة -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalRequests">0</h3>
                        <p>طلبات التفعيل</p>
                    </div>
                </div>
                
                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="activeLicenses">0</h3>
                        <p>التراخيص النشطة</p>
                    </div>
                </div>
                
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalCustomers">0</h3>
                        <p>إجمالي العملاء</p>
                    </div>
                </div>
                
                <div class="stat-card info">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="monthlyGrowth">+15%</h3>
                        <p>النمو الشهري</p>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="charts-grid">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>إحصائيات الطلبات</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="requestsChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>توزيع التراخيص</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="licensesChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- الأنشطة الأخيرة -->
            <div class="recent-activities">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> الأنشطة الأخيرة</h3>
                    </div>
                    <div class="card-body">
                        <div id="recentActivities" class="activities-list">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم طلبات التفعيل -->
        <section id="activation-requests-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-inbox"></i> طلبات التفعيل</h1>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="approveAllPending()">
                        <i class="fas fa-check-double"></i> الموافقة على الكل
                    </button>
                    <button class="btn btn-primary" onclick="refreshRequests()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="filters-bar">
                <div class="filter-group">
                    <label>البحث:</label>
                    <input type="text" id="requestSearch" placeholder="ابحث بالاسم أو الهاتف..." onkeyup="filterRequests()">
                </div>
                <div class="filter-group">
                    <label>الحالة:</label>
                    <select id="statusFilter" onchange="filterRequests()">
                        <option value="">جميع الحالات</option>
                        <option value="pending">في الانتظار</option>
                        <option value="approved">موافق عليه</option>
                        <option value="rejected">مرفوض</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>نوع الترخيص:</label>
                    <select id="licenseTypeFilter" onchange="filterRequests()">
                        <option value="">جميع الأنواع</option>
                        <option value="trial">تجريبي</option>
                        <option value="lifetime">مدى الحياة</option>
                    </select>
                </div>
            </div>

            <!-- جدول الطلبات -->
            <div class="table-container">
                <table class="data-table" id="requestsTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllRequests" onchange="toggleSelectAll('requests')">
                            </th>
                            <th>اسم العميل</th>
                            <th>رقم الهاتف</th>
                            <th>الولاية</th>
                            <th>نوع الترخيص</th>
                            <th>تاريخ الطلب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="requestsTableBody">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- صفحات التنقل -->
            <div class="pagination" id="requestsPagination">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </section>

        <!-- قسم إدارة التراخيص -->
        <section id="license-management-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-key"></i> إدارة التراخيص</h1>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="openCreateLicenseModal()">
                        <i class="fas fa-plus"></i> إنشاء ترخيص جديد
                    </button>
                    <button class="btn btn-primary" onclick="refreshLicenses()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- فلاتر التراخيص -->
            <div class="filters-bar">
                <div class="filter-group">
                    <label>البحث:</label>
                    <input type="text" id="licenseSearch" placeholder="ابحث بمفتاح الترخيص أو اسم العميل..." onkeyup="filterLicenses()">
                </div>
                <div class="filter-group">
                    <label>الحالة:</label>
                    <select id="licenseStatusFilter" onchange="filterLicenses()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="expired">منتهي الصلاحية</option>
                        <option value="deactivated">معطل</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>النوع:</label>
                    <select id="licenseTypeFilterLicenses" onchange="filterLicenses()">
                        <option value="">جميع الأنواع</option>
                        <option value="trial">تجريبي</option>
                        <option value="lifetime">مدى الحياة</option>
                    </select>
                </div>
            </div>

            <!-- جدول التراخيص -->
            <div class="table-container">
                <table class="data-table" id="licensesTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllLicenses" onchange="toggleSelectAll('licenses')">
                            </th>
                            <th>مفتاح الترخيص</th>
                            <th>اسم العميل</th>
                            <th>رقم الهاتف</th>
                            <th>النوع</th>
                            <th>تاريخ الإنشاء</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="licensesTableBody">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- صفحات التنقل -->
            <div class="pagination" id="licensesPagination">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </section>

        <!-- قسم العملاء -->
        <section id="customers-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-users"></i> إدارة العملاء</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="refreshCustomers()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                    <button class="btn btn-info" onclick="exportCustomers()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>

            <!-- إحصائيات العملاء -->
            <div class="customers-stats">
                <div class="stat-card">
                    <h3 id="totalCustomersCount">0</h3>
                    <p>إجمالي العملاء</p>
                </div>
                <div class="stat-card">
                    <h3 id="activeCustomersCount">0</h3>
                    <p>العملاء النشطون</p>
                </div>
                <div class="stat-card">
                    <h3 id="newCustomersThisMonth">0</h3>
                    <p>عملاء جدد هذا الشهر</p>
                </div>
            </div>

            <!-- قائمة العملاء -->
            <div class="customers-grid" id="customersGrid">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </section>

        <!-- قسم الإحصائيات -->
        <section id="statistics-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-chart-bar"></i> الإحصائيات المفصلة</h1>
                <div class="header-actions">
                    <select id="statsTimeRange" onchange="updateStatistics()">
                        <option value="week">آخر أسبوع</option>
                        <option value="month" selected>آخر شهر</option>
                        <option value="quarter">آخر 3 أشهر</option>
                        <option value="year">آخر سنة</option>
                    </select>
                    <button class="btn btn-primary" onclick="updateStatistics()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- الرسوم البيانية المفصلة -->
            <div class="detailed-charts">
                <div class="chart-row">
                    <div class="chart-card large">
                        <div class="chart-header">
                            <h3>اتجاه الطلبات والتراخيص</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="trendsChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>توزيع الولايات</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="statesChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>معدل الموافقة</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="approvalRateChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- قسم التقارير -->
        <section id="reports-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-file-alt"></i> التقارير</h1>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="generateReport()">
                        <i class="fas fa-file-pdf"></i> إنشاء تقرير
                    </button>
                </div>
            </div>

            <!-- خيارات التقرير -->
            <div class="report-options">
                <div class="option-group">
                    <label>نوع التقرير:</label>
                    <select id="reportType">
                        <option value="summary">تقرير ملخص</option>
                        <option value="detailed">تقرير مفصل</option>
                        <option value="licenses">تقرير التراخيص</option>
                        <option value="customers">تقرير العملاء</option>
                    </select>
                </div>
                <div class="option-group">
                    <label>الفترة الزمنية:</label>
                    <input type="date" id="reportStartDate">
                    <span>إلى</span>
                    <input type="date" id="reportEndDate">
                </div>
            </div>

            <!-- معاينة التقرير -->
            <div class="report-preview" id="reportPreview">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </section>

        <!-- قسم السجلات -->
        <section id="logs-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-list"></i> سجلات النظام</h1>
                <div class="header-actions">
                    <button class="btn btn-warning" onclick="clearLogs()">
                        <i class="fas fa-trash"></i> مسح السجلات
                    </button>
                    <button class="btn btn-primary" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- فلاتر السجلات -->
            <div class="filters-bar">
                <div class="filter-group">
                    <label>نوع السجل:</label>
                    <select id="logTypeFilter" onchange="filterLogs()">
                        <option value="">جميع الأنواع</option>
                        <option value="info">معلومات</option>
                        <option value="success">نجح</option>
                        <option value="warning">تحذير</option>
                        <option value="error">خطأ</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>البحث:</label>
                    <input type="text" id="logSearch" placeholder="ابحث في السجلات..." onkeyup="filterLogs()">
                </div>
            </div>

            <!-- قائمة السجلات -->
            <div class="logs-container" id="logsContainer">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </section>

        <!-- قسم الإعدادات -->
        <section id="settings-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                <div class="header-actions">
                    <button class="btn btn-success" onclick="saveSettings()">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </div>

            <!-- نموذج الإعدادات -->
            <div class="settings-form">
                <div class="settings-group">
                    <h3>إعدادات التراخيص</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>مدة صلاحية الترخيص (بالأيام):</label>
                            <input type="number" id="licenseValidityDays" min="1" max="3650">
                        </div>
                        <div class="form-group">
                            <label>الحد الأقصى للأجهزة لكل ترخيص:</label>
                            <input type="number" id="maxDevicesPerLicense" min="1" max="10">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="autoApproval">
                                الموافقة التلقائية على الطلبات
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>معلومات الدعم</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>رقم الهاتف:</label>
                            <input type="tel" id="supportPhone">
                        </div>
                        <div class="form-group">
                            <label>رقم الواتساب:</label>
                            <input type="tel" id="supportWhatsapp">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>البريد الإلكتروني:</label>
                            <input type="email" id="supportEmail">
                        </div>
                    </div>
                </div>

                <div class="settings-group">
                    <h3>معلومات الشركة</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم الشركة (عربي):</label>
                            <input type="text" id="companyName">
                        </div>
                        <div class="form-group">
                            <label>اسم الشركة (إنجليزي):</label>
                            <input type="text" id="companyNameEn">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>العنوان:</label>
                            <input type="text" id="companyAddress">
                        </div>
                        <div class="form-group">
                            <label>الموقع الإلكتروني:</label>
                            <input type="url" id="companyWebsite">
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- النوافذ المنبثقة -->
    
    <!-- نافذة تفاصيل الطلب -->
    <div id="requestDetailsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل طلب التفعيل</h3>
                <button class="modal-close" onclick="closeModal('requestDetailsModal')">&times;</button>
            </div>
            <div class="modal-body" id="requestDetailsBody">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" onclick="approveRequest()">
                    <i class="fas fa-check"></i> موافقة
                </button>
                <button class="btn btn-danger" onclick="rejectRequest()">
                    <i class="fas fa-times"></i> رفض
                </button>
                <button class="btn btn-secondary" onclick="closeModal('requestDetailsModal')">
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء ترخيص جديد -->
    <div id="createLicenseModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إنشاء ترخيص جديد</h3>
                <button class="modal-close" onclick="closeModal('createLicenseModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createLicenseForm">
                    <div class="form-group">
                        <label>اسم العميل:</label>
                        <input type="text" id="newLicenseCustomerName" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف:</label>
                        <input type="tel" id="newLicensePhone" required>
                    </div>
                    <div class="form-group">
                        <label>الولاية:</label>
                        <input type="text" id="newLicenseState" required>
                    </div>
                    <div class="form-group">
                        <label>نوع الترخيص:</label>
                        <select id="newLicenseType" required>
                            <option value="trial">تجريبي</option>
                            <option value="lifetime">مدى الحياة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" onclick="createNewLicense()">
                    <i class="fas fa-plus"></i> إنشاء الترخيص
                </button>
                <button class="btn btn-secondary" onclick="closeModal('createLicenseModal')">
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة الإشعارات -->
    <div id="notificationsPanel" class="notifications-panel">
        <div class="notifications-header">
            <h3>الإشعارات</h3>
            <button onclick="markAllAsRead()">تعيين الكل كمقروء</button>
        </div>
        <div class="notifications-body" id="notificationsBody">
            <!-- سيتم ملؤها بـ JavaScript -->
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="../../utils/page-guard-fixed.js"></script>
    <script src="../../utils/advanced-data-manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="advanced-dashboard.js"></script>
</body>
</html>
