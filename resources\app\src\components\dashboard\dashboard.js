// متغيرات عامة
let currentSection = 'dashboard';
let charts = {};
let dashboardData = {
    customers: [],
    vehicles: [],
    certificates: [],
    sales: [],
    activities: []
};

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    loadUserInfo();
    loadDashboardData();
    initializeCharts();
    loadTheme();
    setupEventListeners();
});

// تهيئة لوحة التحكم
function initializeDashboard() {
    // التحقق من صحة الجلسة
    if (!checkSession()) {
        redirectToLogin();
        return;
    }
    
    // تحديث الإحصائيات
    updateStatistics();
    
    // تحديث الأنشطة الأخيرة
    updateRecentActivities();
    
    // بدء التحديث التلقائي
    startAutoRefresh();
}

// تحميل معلومات المستخدم
function loadUserInfo() {
    try {
        const session = JSON.parse(localStorage.getItem('userSession'));
        if (session) {
            document.getElementById('userName').textContent = session.username;
            document.getElementById('userRole').textContent = session.role || 'مستخدم';
        }
    } catch (error) {
        console.error('خطأ في تحميل معلومات المستخدم:', error);
    }
}

// تحميل بيانات لوحة التحكم
async function loadDashboardData() {
    try {
        // محاكاة تحميل البيانات
        dashboardData = {
            customers: generateMockCustomers(150),
            vehicles: generateMockVehicles(200),
            certificates: generateMockCertificates(180),
            sales: generateMockSales(30),
            activities: generateMockActivities(10)
        };
        
        updateStatistics();
        updateRecentActivities();
        updateCharts();
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showNotification('خطأ في تحميل البيانات', 'error');
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    const stats = calculateStatistics();
    
    document.getElementById('totalCustomers').textContent = stats.customers.toLocaleString();
    document.getElementById('totalVehicles').textContent = stats.vehicles.toLocaleString();
    document.getElementById('totalCertificates').textContent = stats.certificates.toLocaleString();
    document.getElementById('totalRevenue').textContent = stats.revenue.toLocaleString() + ' ر.س';
}

// حساب الإحصائيات
function calculateStatistics() {
    return {
        customers: dashboardData.customers.length,
        vehicles: dashboardData.vehicles.length,
        certificates: dashboardData.certificates.filter(cert => cert.status === 'active').length,
        revenue: dashboardData.sales.reduce((total, sale) => total + sale.amount, 0)
    };
}

// تهيئة الرسوم البيانية
function initializeCharts() {
    initializeSalesChart();
    initializeFuelChart();
}

// رسم بياني للمبيعات
function initializeSalesChart() {
    const ctx = document.getElementById('salesChart').getContext('2d');
    
    charts.sales = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'المبيعات',
                data: [12000, 19000, 15000, 25000, 22000, 30000],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ر.س';
                        }
                    }
                }
            }
        }
    });
}

// رسم بياني لأنواع الوقود
function initializeFuelChart() {
    const ctx = document.getElementById('fuelChart').getContext('2d');
    
    charts.fuel = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['بنزين 91', 'بنزين 95', 'ديزل', 'غاز'],
            datasets: [{
                data: [40, 30, 20, 10],
                backgroundColor: [
                    '#3498db',
                    '#2ecc71',
                    '#f39c12',
                    '#e74c3c'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// تحديث الرسوم البيانية
function updateCharts() {
    if (charts.sales) {
        // تحديث بيانات المبيعات
        const salesData = generateSalesData();
        charts.sales.data.datasets[0].data = salesData;
        charts.sales.update();
    }
    
    if (charts.fuel) {
        // تحديث بيانات الوقود
        const fuelData = generateFuelData();
        charts.fuel.data.datasets[0].data = fuelData;
        charts.fuel.update();
    }
}

// تحديث الأنشطة الأخيرة
function updateRecentActivities() {
    const activityList = document.getElementById('activityList');
    activityList.innerHTML = '';
    
    dashboardData.activities.slice(0, 5).forEach(activity => {
        const activityElement = createActivityElement(activity);
        activityList.appendChild(activityElement);
    });
}

// إنشاء عنصر نشاط
function createActivityElement(activity) {
    const div = document.createElement('div');
    div.className = 'activity-item';
    
    div.innerHTML = `
        <div class="activity-icon" style="background: ${activity.color}">
            <i class="${activity.icon}"></i>
        </div>
        <div class="activity-content">
            <div class="activity-title">${activity.title}</div>
            <div class="activity-time">${formatTime(activity.time)}</div>
        </div>
    `;
    
    return div;
}

// تنسيق الوقت
function formatTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'الآن';
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    return `منذ ${days} يوم`;
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث العام
    const searchInput = document.getElementById('globalSearch');
    searchInput.addEventListener('input', handleGlobalSearch);
    
    // تغيير فترة المبيعات
    const salesPeriod = document.getElementById('salesPeriod');
    salesPeriod.addEventListener('change', handleSalesPeriodChange);
    
    // إغلاق النوافذ المنبثقة بالضغط خارجها
    window.addEventListener('click', handleModalClose);
}

// معالج البحث العام
function handleGlobalSearch(e) {
    const query = e.target.value.toLowerCase();
    // تنفيذ البحث عبر جميع البيانات
    console.log('البحث عن:', query);
}

// معالج تغيير فترة المبيعات
function handleSalesPeriodChange(e) {
    const period = e.target.value;
    updateSalesChart(period);
}

// تحديث رسم المبيعات حسب الفترة
function updateSalesChart(period) {
    let labels, data;
    
    switch (period) {
        case 'week':
            labels = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];
            data = [5000, 7000, 6000, 8000, 7500, 9000, 8500];
            break;
        case 'year':
            labels = ['2020', '2021', '2022', '2023', '2024'];
            data = [150000, 180000, 220000, 280000, 320000];
            break;
        default: // month
            labels = ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'];
            data = [25000, 30000, 28000, 35000];
    }
    
    charts.sales.data.labels = labels;
    charts.sales.data.datasets[0].data = data;
    charts.sales.update();
}

// التحقق من صحة الجلسة
function checkSession() {
    try {
        const session = localStorage.getItem('userSession');
        if (!session) return false;
        
        const sessionData = JSON.parse(session);
        const now = new Date();
        const expiry = new Date(sessionData.expiryDate);
        
        return now < expiry && sessionData.isValid;
    } catch (error) {
        return false;
    }
}

// إعادة التوجيه لتسجيل الدخول
function redirectToLogin() {
    window.location.href = '../auth/new-login.html';
}

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

// عرض قسم معين
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // إزالة الحالة النشطة من جميع عناصر التنقل
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // إظهار القسم المطلوب
    document.getElementById(sectionName + '-section').classList.add('active');
    
    // تفعيل عنصر التنقل المناسب
    document.querySelector(`[onclick="showSection('${sectionName}')"]`).parentElement.classList.add('active');
    
    // تحديث عنوان الصفحة
    updatePageTitle(sectionName);
    
    currentSection = sectionName;
}

// تحديث عنوان الصفحة
function updatePageTitle(sectionName) {
    const titles = {
        dashboard: 'لوحة التحكم',
        customers: 'إدارة العملاء',
        vehicles: 'إدارة المركبات',
        certificates: 'إدارة الشهادات',
        inventory: 'إدارة المخزون',
        reports: 'التقارير والإحصائيات',
        settings: 'إعدادات النظام'
    };
    
    document.getElementById('pageTitle').textContent = titles[sectionName] || 'لوحة التحكم';
}

// تبديل المظهر
function toggleTheme() {
    const body = document.body;
    const themeIcon = document.getElementById('themeIcon');
    
    body.classList.toggle('dark-mode');
    
    if (body.classList.contains('dark-mode')) {
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
        localStorage.setItem('theme', 'dark');
    } else {
        themeIcon.classList.remove('fa-sun');
        themeIcon.classList.add('fa-moon');
        localStorage.setItem('theme', 'light');
    }
}

// تحميل المظهر المحفوظ
function loadTheme() {
    const savedTheme = localStorage.getItem('theme');
    const themeIcon = document.getElementById('themeIcon');
    
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
    }
}

// إظهار الإشعارات
function showNotifications() {
    const modal = document.getElementById('notificationsModal');
    const notificationsList = document.getElementById('notificationsList');
    
    // تحميل الإشعارات
    const notifications = getNotifications();
    notificationsList.innerHTML = '';
    
    notifications.forEach(notification => {
        const notificationElement = createNotificationElement(notification);
        notificationsList.appendChild(notificationElement);
    });
    
    modal.style.display = 'flex';
}

// إغلاق الإشعارات
function closeNotifications() {
    document.getElementById('notificationsModal').style.display = 'none';
}

// تبديل ملء الشاشة
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('userSession');
        redirectToLogin();
    }
}

// الإجراءات السريعة
function addNewCustomer() {
    showNotification('فتح نموذج إضافة عميل جديد', 'info');
}

function addNewVehicle() {
    showNotification('فتح نموذج تسجيل مركبة جديدة', 'info');
}

function issueCertificate() {
    showNotification('فتح نموذج إصدار شهادة', 'info');
}

function generateReport() {
    showNotification('فتح مولد التقارير', 'info');
}

// إظهار إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// بدء التحديث التلقائي
function startAutoRefresh() {
    setInterval(() => {
        if (currentSection === 'dashboard') {
            loadDashboardData();
        }
    }, 30000); // كل 30 ثانية
}

// دوال إنشاء البيانات التجريبية
function generateMockCustomers(count) {
    const customers = [];
    for (let i = 0; i < count; i++) {
        customers.push({
            id: i + 1,
            name: `عميل ${i + 1}`,
            phone: `05${Math.floor(Math.random() * 100000000)}`,
            createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
        });
    }
    return customers;
}

function generateMockVehicles(count) {
    const vehicles = [];
    for (let i = 0; i < count; i++) {
        vehicles.push({
            id: i + 1,
            plateNumber: `${Math.floor(Math.random() * 9999)}`,
            type: ['سيارة', 'شاحنة', 'دراجة نارية'][Math.floor(Math.random() * 3)],
            createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
        });
    }
    return vehicles;
}

function generateMockCertificates(count) {
    const certificates = [];
    for (let i = 0; i < count; i++) {
        certificates.push({
            id: i + 1,
            type: ['تركيب', 'مراقبة دورية'][Math.floor(Math.random() * 2)],
            status: ['active', 'expired'][Math.floor(Math.random() * 2)],
            createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
        });
    }
    return certificates;
}

function generateMockSales(count) {
    const sales = [];
    for (let i = 0; i < count; i++) {
        sales.push({
            id: i + 1,
            amount: Math.floor(Math.random() * 5000) + 1000,
            date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
        });
    }
    return sales;
}

function generateMockActivities(count) {
    const activities = [];
    const types = [
        { title: 'تم إضافة عميل جديد', icon: 'fas fa-user-plus', color: '#27ae60' },
        { title: 'تم إصدار شهادة جديدة', icon: 'fas fa-certificate', color: '#3498db' },
        { title: 'تم تسجيل مركبة جديدة', icon: 'fas fa-car', color: '#f39c12' },
        { title: 'تم إنشاء تقرير', icon: 'fas fa-file-alt', color: '#9b59b6' }
    ];
    
    for (let i = 0; i < count; i++) {
        const type = types[Math.floor(Math.random() * types.length)];
        activities.push({
            id: i + 1,
            title: type.title,
            icon: type.icon,
            color: type.color,
            time: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000)
        });
    }
    
    return activities.sort((a, b) => b.time - a.time);
}

function generateSalesData() {
    return Array.from({ length: 6 }, () => Math.floor(Math.random() * 30000) + 10000);
}

function generateFuelData() {
    return [
        Math.floor(Math.random() * 50) + 30,
        Math.floor(Math.random() * 40) + 20,
        Math.floor(Math.random() * 30) + 15,
        Math.floor(Math.random() * 20) + 5
    ];
}

function getNotifications() {
    return [
        { id: 1, title: 'شهادة تنتهي قريباً', message: 'شهادة العميل أحمد محمد تنتهي خلال 7 أيام', time: new Date(), type: 'warning' },
        { id: 2, title: 'عميل جديد', message: 'تم تسجيل عميل جديد: سارة أحمد', time: new Date(Date.now() - 3600000), type: 'info' },
        { id: 3, title: 'تقرير شهري', message: 'تقرير المبيعات الشهري جاهز للمراجعة', time: new Date(Date.now() - 7200000), type: 'success' }
    ];
}

function createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = `notification-item ${notification.type}`;
    div.innerHTML = `
        <div class="notification-content">
            <h4>${notification.title}</h4>
            <p>${notification.message}</p>
            <span class="notification-time">${formatTime(notification.time)}</span>
        </div>
    `;
    return div;
}

function handleModalClose(e) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}
