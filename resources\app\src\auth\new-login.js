// متغيرات عامة
let isLoading = false;
let currentTab = 'admin';

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeLogin();
    loadTheme();
    setupEventListeners();
    initializeTabs();
});

// تهيئة صفحة تسجيل الدخول
function initializeLogin() {
    // التحقق من وجود جلسة محفوظة
    checkSavedSession();

    // إضافة تأثيرات بصرية
    addVisualEffects();

    // التركيز سيتم في initializeTabs()
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نماذج تسجيل الدخول
    const adminForm = document.getElementById('adminLoginForm');
    const licenseForm = document.getElementById('licenseLoginForm');

    // حقول الإدخال
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const licenseKeyInput = document.getElementById('licenseKey');
    const customerNameInput = document.getElementById('customerName');

    // معالجات إرسال النماذج
    if (adminForm) {
        adminForm.addEventListener('submit', handleAdminLogin);
    }
    if (licenseForm) {
        licenseForm.addEventListener('submit', handleLicenseLogin);
    }

    // معالج الضغط على Enter للحقول
    if (usernameInput && passwordInput) {
        usernameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                passwordInput.focus();
            }
        });

        passwordInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // إنشاء event مناسب لإرسال النموذج
                const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                adminForm.dispatchEvent(submitEvent);
            }
        });
    }

    if (licenseKeyInput && customerNameInput) {
        licenseKeyInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                customerNameInput.focus();
            }
        });

        customerNameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // إنشاء event مناسب لإرسال النموذج
                const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                licenseForm.dispatchEvent(submitEvent);
            }
        });
    }

    // إزالة رسائل الخطأ عند الكتابة
    if (usernameInput) usernameInput.addEventListener('input', clearErrors);
    if (passwordInput) passwordInput.addEventListener('input', clearErrors);
    if (licenseKeyInput) licenseKeyInput.addEventListener('input', () => clearError('licenseKeyError'));
    if (customerNameInput) customerNameInput.addEventListener('input', () => clearError('customerNameError'));
}

// تم حذف الدالة القديمة handleLogin لتجنب التداخل

// تم حذف دالة attemptLogin القديمة

// تم حذف دالة validateInputs القديمة - استخدم validateAdminLogin و validateLicenseLogin بدلاً منها

// إظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// تسجيل دخول سريع
function quickLogin(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
    
    // تأثير بصري
    const form = document.getElementById('loginForm');
    form.style.transform = 'scale(1.02)';
    setTimeout(() => {
        form.style.transform = 'scale(1)';
        handleLogin(new Event('submit'));
    }, 200);
}

// إظهار نافذة نسيان كلمة المرور
function showForgotPassword() {
    document.getElementById('forgotPasswordModal').style.display = 'flex';
}

// إغلاق نافذة نسيان كلمة المرور
function closeForgotPassword() {
    document.getElementById('forgotPasswordModal').style.display = 'none';
}

// تبديل المظهر
function toggleTheme() {
    const body = document.body;
    const themeIcon = document.getElementById('themeIcon');
    
    body.classList.toggle('dark-mode');
    
    if (body.classList.contains('dark-mode')) {
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
        localStorage.setItem('theme', 'dark');
    } else {
        themeIcon.classList.remove('fa-sun');
        themeIcon.classList.add('fa-moon');
        localStorage.setItem('theme', 'light');
    }
}

// تحميل المظهر المحفوظ
function loadTheme() {
    const savedTheme = localStorage.getItem('theme');
    const themeIcon = document.getElementById('themeIcon');
    
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
    }
}

// التحقق من الجلسة المحفوظة
function checkSavedSession() {
    const savedSession = localStorage.getItem('userSession');
    if (savedSession) {
        try {
            const session = JSON.parse(savedSession);

            // التحقق من صحة الجلسة
            if (session.isValid) {
                // إذا كانت الجلسة صالحة، ملء البيانات المحفوظة
                setTimeout(() => {
                    const usernameField = document.getElementById('username');
                    const rememberField = document.getElementById('rememberMe');

                    if (usernameField && session.username) {
                        usernameField.value = session.username;
                    }
                    if (rememberField) {
                        rememberField.checked = true;
                    }
                }, 100);
            }
        } catch (error) {
            console.error('خطأ في تحميل الجلسة المحفوظة:', error);
            // مسح الجلسة التالفة
            localStorage.removeItem('userSession');
        }
    }
}

// حفظ الجلسة
function saveSession(user) {
    localStorage.setItem('userSession', JSON.stringify(user));
}

// إعادة التوجيه إلى لوحة التحكم
function redirectToDashboard() {
    if (window.electronAPI) {
        window.electronAPI.reloadAfterLogin();
    } else {
        window.location.href = '../../index.html';
    }
}

// إدارة حالة التحميل
function setLoading(loading, buttonId = 'loginBtn') {
    isLoading = loading;
    const loginBtn = document.getElementById(buttonId);

    if (!loginBtn) return;

    const btnText = loginBtn.querySelector('.btn-text');
    const spinner = loginBtn.querySelector('.loading-spinner');

    if (loading) {
        btnText.style.opacity = '0';
        spinner.style.display = 'block';
        loginBtn.disabled = true;
    } else {
        btnText.style.opacity = '1';
        spinner.style.display = 'none';
        loginBtn.disabled = false;
    }
}

// إظهار رسالة خطأ
function showError(message, elementId = 'mainError') {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';

        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }
}

// إظهار رسالة نجاح
function showSuccess(message, elementId = 'successMessage') {
    const successElement = document.getElementById(elementId);
    if (successElement) {
        successElement.innerHTML = message;
        successElement.style.display = 'block';

        // إخفاء الرسالة بعد 3 ثوان
        setTimeout(() => {
            successElement.style.display = 'none';
        }, 3000);
    }
}

// إظهار خطأ في حقل معين
function showFieldError(fieldId, message) {
    const errorElement = document.getElementById(fieldId);
    errorElement.textContent = message;
    errorElement.style.display = 'block';
}

// مسح الرسائل
function clearMessages() {
    document.getElementById('mainError').style.display = 'none';
    document.getElementById('successMessage').style.display = 'none';
}

// مسح أخطاء الحقول
function clearErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.style.display = 'none';
    });
}

// مسح رسالة خطأ معينة
function clearError(errorId) {
    const errorElement = document.getElementById(errorId);
    if (errorElement) {
        errorElement.style.display = 'none';
        errorElement.textContent = '';
    }
}

// إضافة تأثيرات بصرية
function addVisualEffects() {
    // تأثير الظهور التدريجي
    const loginCard = document.querySelector('.login-card');
    loginCard.style.opacity = '0';
    loginCard.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        loginCard.style.transition = 'all 0.6s ease';
        loginCard.style.opacity = '1';
        loginCard.style.transform = 'translateY(0)';
    }, 100);
    
    // تأثير التركيز على الحقول
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
}

// إغلاق النافذة المنبثقة بالضغط خارجها
window.addEventListener('click', function(e) {
    const modal = document.getElementById('forgotPasswordModal');
    if (e.target === modal) {
        closeForgotPassword();
    }
});

// ===== دوال التبويبات والترخيص =====

// تهيئة التبويبات
function initializeTabs() {
    // إظهار تبويب المدير افتراضياً
    switchTab('admin');
}

// تبديل التبويبات
function switchTab(tabName) {
    currentTab = tabName;

    // إزالة الحالة النشطة من جميع التبويبات
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إخفاء جميع النماذج
    document.querySelectorAll('.login-form').forEach(form => {
        form.classList.remove('active');
    });

    // تفعيل التبويب والنموذج المحدد
    if (tabName === 'admin') {
        document.getElementById('adminTab').classList.add('active');
        document.getElementById('adminLoginForm').classList.add('active');
        // تركيز على حقل اسم المستخدم
        setTimeout(() => {
            document.getElementById('username').focus();
        }, 100);
    } else if (tabName === 'license') {
        document.getElementById('licenseTab').classList.add('active');
        document.getElementById('licenseLoginForm').classList.add('active');
        // تركيز على حقل الترخيص
        setTimeout(() => {
            document.getElementById('licenseKey').focus();
        }, 100);
    }

    // مسح الرسائل
    clearAllErrors();
}

// تنسيق مفتاح الترخيص أثناء الكتابة
function formatLicenseKey(input) {
    let value = input.value.replace(/[^A-Z0-9]/g, ''); // إزالة كل شيء عدا الأحرف والأرقام

    // تقسيم إلى مجموعات من 4 أحرف
    let formatted = '';
    for (let i = 0; i < value.length; i += 4) {
        if (i > 0) formatted += '-';
        formatted += value.substr(i, 4);
    }

    input.value = formatted;
}

// معالج تسجيل دخول المدير
async function handleAdminLogin(e) {
    e.preventDefault();
    console.log('🔐 بدء عملية تسجيل دخول المدير...');

    if (isLoading) {
        console.log('⏳ العملية قيد التنفيذ بالفعل...');
        return;
    }

    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();

    console.log('📝 البيانات المدخلة:', { username: username, passwordLength: password.length });

    // التحقق من البيانات
    if (!validateAdminLogin(username, password)) {
        console.log('❌ فشل في التحقق من البيانات');
        return;
    }

    console.log('✅ تم التحقق من البيانات بنجاح');

    setLoading(true, 'adminLoginBtn');
    clearError('adminError');

    try {
        console.log('⏳ بدء التحقق من البيانات...');

        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1500));

        console.log('🔍 التحقق من بيانات الاعتماد...');

        // التحقق من بيانات المدير
        if (username === 'admin' && password === 'admin123') {
            console.log('✅ بيانات الاعتماد صحيحة!');

            // حفظ الجلسة
            const sessionData = {
                username: username,
                userType: 'admin',
                loginTime: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                isValid: true,
                expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 ساعة
            };

            localStorage.setItem('userSession', JSON.stringify(sessionData));
            console.log('💾 تم حفظ الجلسة');

            // إظهار رسالة نجاح
            showSuccess('تم تسجيل الدخول بنجاح! جاري التحويل...', 'adminSuccess');
            console.log('✅ تم إظهار رسالة النجاح');

            // التحويل للوحة التحكم
            setTimeout(() => {
                console.log('🔄 بدء عملية التحويل...');

                // التحقق من حفظ الجلسة قبل التحويل
                const savedSession = localStorage.getItem('userSession');
                console.log('🔍 الجلسة المحفوظة قبل التحويل:', savedSession);

                if (window.electronAPI) {
                    console.log('📱 استخدام Electron API');
                    window.electronAPI.reloadAfterLogin();
                } else {
                    console.log('🌐 استخدام المتصفح العادي');
                    window.location.href = '../components/admin/license-management.html';
                }
            }, 1000); // تقليل الوقت لتحسين تجربة المستخدم

        } else {
            console.log('❌ بيانات الاعتماد غير صحيحة');
            showError('اسم المستخدم أو كلمة المرور غير صحيحة', 'adminError');
        }

    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        showError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'adminError');
    } finally {
        setLoading(false, 'adminLoginBtn');
    }
}

// معالج تسجيل دخول بالترخيص
async function handleLicenseLogin(e) {
    e.preventDefault();

    if (isLoading) return;

    const licenseKey = document.getElementById('licenseKey').value.trim();
    const customerName = document.getElementById('customerName').value.trim();

    // التحقق من البيانات
    if (!validateLicenseLogin(licenseKey, customerName)) {
        return;
    }

    setLoading(true, 'licenseLoginBtn');
    clearError('licenseError');

    try {
        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 2000));

        // التحقق من الترخيص
        const licenseValidation = await validateLicense(licenseKey, customerName);

        if (licenseValidation.valid) {
            // حفظ الجلسة
            const sessionData = {
                licenseKey: licenseKey,
                customerName: customerName,
                userType: 'customer',
                loginTime: new Date().toISOString(),
                licenseInfo: licenseValidation.license,
                isValid: true
            };

            localStorage.setItem('userSession', JSON.stringify(sessionData));

            // إظهار رسالة نجاح
            showSuccess('تم تسجيل الدخول بنجاح! جاري التحويل...', 'licenseSuccess');

            // التحويل للوحة التحكم
            setTimeout(() => {
                if (window.electronAPI) {
                    window.electronAPI.reloadAfterLogin();
                } else {
                    window.location.href = '../components/dashboard/dashboard.html';
                }
            }, 1500);

        } else {
            showError(licenseValidation.error || 'الترخيص غير صحيح أو منتهي الصلاحية', 'licenseError');
        }

    } catch (error) {
        console.error('خطأ في التحقق من الترخيص:', error);
        showError('حدث خطأ في التحقق من الترخيص. يرجى المحاولة مرة أخرى.', 'licenseError');
    } finally {
        setLoading(false, 'licenseLoginBtn');
    }
}

// التحقق من صحة بيانات المدير
function validateAdminLogin(username, password) {
    let isValid = true;

    if (!username) {
        showError('يرجى إدخال اسم المستخدم', 'usernameError');
        isValid = false;
    }

    if (!password) {
        showError('يرجى إدخال كلمة المرور', 'passwordError');
        isValid = false;
    }

    return isValid;
}

// التحقق من صحة بيانات الترخيص
function validateLicenseLogin(licenseKey, customerName) {
    let isValid = true;

    if (!licenseKey) {
        showError('يرجى إدخال مفتاح الترخيص', 'licenseKeyError');
        isValid = false;
    } else if (licenseKey.replace(/-/g, '').length !== 16) {
        showError('مفتاح الترخيص يجب أن يكون 16 رقم/حرف', 'licenseKeyError');
        isValid = false;
    }

    if (!customerName) {
        showError('يرجى إدخال اسم العميل', 'customerNameError');
        isValid = false;
    } else if (customerName.length < 3) {
        showError('اسم العميل يجب أن يكون 3 أحرف على الأقل', 'customerNameError');
        isValid = false;
    }

    return isValid;
}

// التحقق من صحة الترخيص
async function validateLicense(licenseKey, customerName) {
    try {
        // تحميل التراخيص المحفوظة
        const licenses = JSON.parse(localStorage.getItem('licenses') || '[]');

        // البحث عن الترخيص
        const license = licenses.find(l =>
            l.licenseKey === licenseKey &&
            l.customerName.toLowerCase() === customerName.toLowerCase()
        );

        if (!license) {
            return { valid: false, error: 'الترخيص غير موجود أو اسم العميل غير مطابق' };
        }

        // التحقق من حالة الترخيص
        if (license.status !== 'active') {
            return { valid: false, error: 'الترخيص غير نشط' };
        }

        // التحقق من تاريخ انتهاء الصلاحية
        if (license.expiryDate) {
            const now = new Date();
            const expiry = new Date(license.expiryDate);

            if (now > expiry) {
                return { valid: false, error: 'انتهت صلاحية الترخيص' };
            }
        }

        // التحقق من الجهاز (إذا كان متوفراً)
        if (window.deviceVerification) {
            const deviceCheck = await window.deviceVerification.verifyLicenseDevice(
                license.id,
                license.deviceFingerprint || license.machineId
            );

            if (!deviceCheck.isValid) {
                return { valid: false, error: 'هذا الترخيص مخصص لجهاز آخر' };
            }
        }

        return { valid: true, license: license };

    } catch (error) {
        console.error('خطأ في التحقق من الترخيص:', error);
        return { valid: false, error: 'حدث خطأ في التحقق من الترخيص' };
    }
}

// مسح جميع رسائل الخطأ
function clearAllErrors() {
    const errorElements = document.querySelectorAll('.error-message');
    errorElements.forEach(element => {
        element.style.display = 'none';
        element.textContent = '';
    });
}

// إظهار واجهة طلب التفعيل
function showActivationRequest() {
    if (window.electronAPI) {
        // في Electron، فتح نافذة جديدة أو تحويل
        window.location.href = 'activation-request.html';
    } else {
        // في المتصفح، فتح في نفس النافذة
        window.location.href = 'activation-request.html';
    }
}
