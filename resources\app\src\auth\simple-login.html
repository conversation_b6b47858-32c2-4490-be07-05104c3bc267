<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مؤسسة وقود المستقبل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .logo {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 1.8em;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 500;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            color: #e74c3c;
            margin-top: 10px;
            padding: 10px;
            background: #ffeaea;
            border-radius: 5px;
            display: none;
        }
        
        .success-message {
            color: #27ae60;
            margin-top: 10px;
            padding: 10px;
            background: #eafaf1;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">⛽</div>
        <h1>مؤسسة وقود المستقبل</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <div class="form-group">
                <label for="licenseKey">مفتاح الترخيص (للعملاء):</label>
                <input type="text" id="licenseKey" name="licenseKey" placeholder="أدخل مفتاح الترخيص">
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                تسجيل الدخول
            </button>
            
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
        </form>
    </div>

    <script>
        console.log('تم تحميل صفحة تسجيل الدخول البسيطة');
        
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const licenseKey = document.getElementById('licenseKey').value.trim();
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            const loginBtn = document.getElementById('loginBtn');
            
            // إخفاء الرسائل السابقة
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            
            console.log('محاولة تسجيل دخول:', { username, passwordLength: password.length });
            
            // التحقق من البيانات
            if (!username || !password) {
                errorDiv.textContent = 'يرجى إدخال اسم المستخدم وكلمة المرور';
                errorDiv.style.display = 'block';
                return;
            }
            
            // تعطيل الزر
            loginBtn.disabled = true;
            loginBtn.textContent = 'جاري التحقق...';
            
            // محاكاة عملية التحقق
            setTimeout(() => {
                // تحقق من بيانات المدير
                if (username === 'admin' && password === 'admin123') {
                    successDiv.textContent = 'تم تسجيل الدخول بنجاح! جاري التحويل...';
                    successDiv.style.display = 'block';

                    // حفظ الجلسة
                    const sessionData = {
                        username: username,
                        userType: 'admin',
                        loginTime: new Date().toISOString(),
                        isValid: true
                    };
                    localStorage.setItem('userSession', JSON.stringify(sessionData));

                    // التحويل
                    setTimeout(() => {
                        if (window.electronAPI) {
                            window.electronAPI.reloadAfterLogin();
                        } else {
                            window.location.href = '../components/admin/dashboard.html';
                        }
                    }, 1500);

                } else if (licenseKey) {
                    // التحقق من مفتاح الترخيص
                    const licenses = JSON.parse(localStorage.getItem('licenses') || '[]');
                    const validLicense = licenses.find(license =>
                        license.key === licenseKey && license.status === 'active'
                    );

                    if (validLicense) {
                        // التحقق من انتهاء الصلاحية للتراخيص التجريبية
                        if (validLicense.type === 'trial' && validLicense.expiryDate) {
                            const expiryDate = new Date(validLicense.expiryDate);
                            if (expiryDate < new Date()) {
                                errorDiv.textContent = 'انتهت صلاحية الترخيص التجريبي';
                                errorDiv.style.display = 'block';
                                return;
                            }
                        }

                        successDiv.textContent = 'تم التحقق من الترخيص بنجاح! مرحباً ' + validLicense.customerName;
                        successDiv.style.display = 'block';

                        // حفظ الجلسة
                        const sessionData = {
                            username: validLicense.customerName,
                            userType: 'client',
                            licenseKey: licenseKey,
                            licenseType: validLicense.type,
                            loginTime: new Date().toISOString(),
                            isValid: true
                        };
                        localStorage.setItem('userSession', JSON.stringify(sessionData));

                        // التحويل لصفحة العميل (يمكن إنشاؤها لاحقاً)
                        setTimeout(() => {
                            alert('مرحباً بك! صفحة العميل قيد التطوير');
                        }, 1500);

                    } else {
                        errorDiv.textContent = 'مفتاح الترخيص غير صحيح أو منتهي الصلاحية';
                        errorDiv.style.display = 'block';
                    }

                } else {
                    errorDiv.textContent = 'يرجى إدخال بيانات المدير أو مفتاح ترخيص صحيح';
                    errorDiv.style.display = 'block';
                }
                
                // إعادة تفعيل الزر
                loginBtn.disabled = false;
                loginBtn.textContent = 'تسجيل الدخول';
            }, 1000);
        });
        
        // التركيز على حقل اسم المستخدم
        document.getElementById('username').focus();
    </script>
</body>
</html>
