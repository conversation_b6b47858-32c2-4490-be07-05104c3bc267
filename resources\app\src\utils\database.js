// نظام إدارة البيانات المحسن

class DatabaseManager {
    constructor() {
        this.dbPath = this.getDbPath();
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;
        this.cache = new Map();
        this.initialized = false;
    }

    // الحصول على مسار قاعدة البيانات
    getDbPath() {
        if (this.isElectron) {
            const path = require('path');
            const os = require('os');
            return path.join(os.homedir(), 'FutureFuelData', 'database.json');
        }
        return 'localStorage';
    }

    // تهيئة قاعدة البيانات
    async initialize() {
        if (this.initialized) return;

        try {
            await this.createDirectoryIfNotExists();
            await this.loadInitialData();
            this.initialized = true;
            console.log('تم تهيئة قاعدة البيانات بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة قاعدة البيانات:', error);
            throw error;
        }
    }

    // إنشاء المجلد إذا لم يكن موجوداً
    async createDirectoryIfNotExists() {
        if (!this.isElectron) return;

        const fs = require('fs').promises;
        const path = require('path');
        const dir = path.dirname(this.dbPath);

        try {
            await fs.access(dir);
        } catch {
            await fs.mkdir(dir, { recursive: true });
        }
    }

    // تحميل البيانات الأولية
    async loadInitialData() {
        const defaultData = {
            customers: [],
            vehicles: [],
            certificates: [],
            inventory: [],
            sales: [],
            settings: {
                shopName: 'مؤسسة وقود المستقبل',
                version: '3.0.0',
                lastBackup: null,
                autoBackup: true
            },
            metadata: {
                version: '3.0.0',
                createdAt: new Date().toISOString(),
                lastModified: new Date().toISOString()
            }
        };

        const existingData = await this.loadData();
        if (!existingData) {
            await this.saveData(defaultData);
        }
    }

    // تحميل البيانات
    async loadData() {
        try {
            if (this.isElectron) {
                return await this.loadFromFile();
            } else {
                return this.loadFromLocalStorage();
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return null;
        }
    }

    // تحميل من الملف (Electron)
    async loadFromFile() {
        const fs = require('fs').promises;
        
        try {
            const data = await fs.readFile(this.dbPath, 'utf8');
            const parsedData = JSON.parse(data);
            
            // تحديث الكاش
            this.updateCache(parsedData);
            
            return parsedData;
        } catch (error) {
            if (error.code === 'ENOENT') {
                return null; // الملف غير موجود
            }
            throw error;
        }
    }

    // تحميل من localStorage (المتصفح)
    loadFromLocalStorage() {
        try {
            const data = localStorage.getItem('futureFuelData');
            if (data) {
                const parsedData = JSON.parse(data);
                this.updateCache(parsedData);
                return parsedData;
            }
            return null;
        } catch (error) {
            console.error('خطأ في تحميل البيانات من localStorage:', error);
            return null;
        }
    }

    // حفظ البيانات
    async saveData(data) {
        try {
            // تحديث معلومات التعديل
            data.metadata = {
                ...data.metadata,
                lastModified: new Date().toISOString()
            };

            if (this.isElectron) {
                await this.saveToFile(data);
            } else {
                this.saveToLocalStorage(data);
            }

            // تحديث الكاش
            this.updateCache(data);

            console.log('تم حفظ البيانات بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            throw error;
        }
    }

    // حفظ في الملف (Electron)
    async saveToFile(data) {
        const fs = require('fs').promises;
        
        // إنشاء نسخة احتياطية أولاً
        await this.createBackup();
        
        // حفظ البيانات
        const jsonData = JSON.stringify(data, null, 2);
        await fs.writeFile(this.dbPath, jsonData, 'utf8');
    }

    // حفظ في localStorage (المتصفح)
    saveToLocalStorage(data) {
        const jsonData = JSON.stringify(data);
        localStorage.setItem('futureFuelData', jsonData);
    }

    // تحديث الكاش
    updateCache(data) {
        Object.keys(data).forEach(key => {
            this.cache.set(key, data[key]);
        });
    }

    // الحصول على جدول معين
    async getTable(tableName) {
        if (!this.initialized) {
            await this.initialize();
        }

        // التحقق من الكاش أولاً
        if (this.cache.has(tableName)) {
            return this.cache.get(tableName);
        }

        // تحميل من قاعدة البيانات
        const data = await this.loadData();
        return data ? data[tableName] || [] : [];
    }

    // حفظ جدول معين
    async saveTable(tableName, tableData) {
        if (!this.initialized) {
            await this.initialize();
        }

        const fullData = await this.loadData() || {};
        fullData[tableName] = tableData;
        
        await this.saveData(fullData);
    }

    // إضافة سجل جديد
    async addRecord(tableName, record) {
        const table = await this.getTable(tableName);
        
        // إضافة معرف فريد إذا لم يكن موجوداً
        if (!record.id) {
            record.id = this.generateId();
        }
        
        // إضافة طوابع زمنية
        record.createdAt = new Date().toISOString();
        record.updatedAt = new Date().toISOString();
        
        table.push(record);
        await this.saveTable(tableName, table);
        
        return record;
    }

    // تحديث سجل
    async updateRecord(tableName, id, updates) {
        const table = await this.getTable(tableName);
        const index = table.findIndex(record => record.id === id);
        
        if (index === -1) {
            throw new Error(`السجل غير موجود: ${id}`);
        }
        
        // تحديث السجل
        table[index] = {
            ...table[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        
        await this.saveTable(tableName, table);
        return table[index];
    }

    // حذف سجل
    async deleteRecord(tableName, id) {
        const table = await this.getTable(tableName);
        const index = table.findIndex(record => record.id === id);
        
        if (index === -1) {
            throw new Error(`السجل غير موجود: ${id}`);
        }
        
        const deletedRecord = table.splice(index, 1)[0];
        await this.saveTable(tableName, table);
        
        return deletedRecord;
    }

    // البحث في الجدول
    async searchTable(tableName, query, fields = []) {
        const table = await this.getTable(tableName);
        
        if (!query) return table;
        
        const searchTerm = query.toLowerCase();
        
        return table.filter(record => {
            if (fields.length === 0) {
                // البحث في جميع الحقول
                return Object.values(record).some(value => 
                    value && value.toString().toLowerCase().includes(searchTerm)
                );
            } else {
                // البحث في حقول محددة
                return fields.some(field => 
                    record[field] && record[field].toString().toLowerCase().includes(searchTerm)
                );
            }
        });
    }

    // فلترة الجدول
    async filterTable(tableName, filterFn) {
        const table = await this.getTable(tableName);
        return table.filter(filterFn);
    }

    // ترتيب الجدول
    async sortTable(tableName, sortField, sortOrder = 'asc') {
        const table = await this.getTable(tableName);
        
        return table.sort((a, b) => {
            const aValue = a[sortField];
            const bValue = b[sortField];
            
            if (sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    // إنشاء نسخة احتياطية
    async createBackup() {
        if (!this.isElectron) return;

        try {
            const fs = require('fs').promises;
            const path = require('path');
            
            const backupDir = path.join(path.dirname(this.dbPath), 'backups');
            await fs.mkdir(backupDir, { recursive: true });
            
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = path.join(backupDir, `backup-${timestamp}.json`);
            
            const data = await this.loadFromFile();
            if (data) {
                await fs.writeFile(backupPath, JSON.stringify(data, null, 2));
                console.log('تم إنشاء نسخة احتياطية:', backupPath);
            }
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        }
    }

    // استعادة من نسخة احتياطية
    async restoreFromBackup(backupPath) {
        if (!this.isElectron) return false;

        try {
            const fs = require('fs').promises;
            const data = await fs.readFile(backupPath, 'utf8');
            const parsedData = JSON.parse(data);
            
            await this.saveData(parsedData);
            console.log('تم استعادة البيانات من النسخة الاحتياطية');
            return true;
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    // تصدير البيانات
    async exportData(format = 'json') {
        const data = await this.loadData();
        
        switch (format) {
            case 'json':
                return JSON.stringify(data, null, 2);
            case 'csv':
                return this.convertToCSV(data);
            default:
                throw new Error(`تنسيق غير مدعوم: ${format}`);
        }
    }

    // تحويل إلى CSV
    convertToCSV(data) {
        // تنفيذ بسيط لتحويل JSON إلى CSV
        const tables = Object.keys(data).filter(key => Array.isArray(data[key]));
        let csv = '';
        
        tables.forEach(tableName => {
            const table = data[tableName];
            if (table.length > 0) {
                csv += `\n\n=== ${tableName} ===\n`;
                const headers = Object.keys(table[0]);
                csv += headers.join(',') + '\n';
                
                table.forEach(record => {
                    const values = headers.map(header => 
                        record[header] ? `"${record[header]}"` : ''
                    );
                    csv += values.join(',') + '\n';
                });
            }
        });
        
        return csv;
    }

    // إنشاء معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // تنظيف الكاش
    clearCache() {
        this.cache.clear();
    }

    // إحصائيات قاعدة البيانات
    async getStats() {
        const data = await this.loadData();
        if (!data) return null;

        const stats = {};
        Object.keys(data).forEach(key => {
            if (Array.isArray(data[key])) {
                stats[key] = data[key].length;
            }
        });

        return {
            tables: stats,
            totalRecords: Object.values(stats).reduce((sum, count) => sum + count, 0),
            lastModified: data.metadata?.lastModified,
            version: data.metadata?.version
        };
    }
}

// إنشاء مثيل واحد للاستخدام العام
const dbManager = new DatabaseManager();

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DatabaseManager, dbManager };
} else {
    window.DatabaseManager = DatabaseManager;
    window.dbManager = dbManager;
}
