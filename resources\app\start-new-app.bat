@echo off
chcp 65001 >nul
title مؤسسة وقود المستقبل - تشغيل التطبيق المحدث

echo.
echo ========================================
echo    مؤسسة وقود المستقبل - الإصدار 3.0.0
echo ========================================
echo.

echo 🚀 بدء تشغيل التطبيق المحدث...
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit /b 1
)

REM التحقق من وجود npm
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: npm غير متوفر
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
echo ✅ npm متوفر
echo.

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
)

echo 🎯 تشغيل التطبيق...
echo.
echo 📋 بيانات تسجيل الدخول التجريبية:
echo    المدير: admin / admin123
echo    المستخدم: user / user123
echo    مدير الفرع: manager / manager123
echo.
echo 🌟 الميزات الجديدة:
echo    - واجهة تسجيل دخول محدثة
echo    - لوحة تحكم تفاعلية
echo    - الوضع المظلم
echo    - نظام قاعدة بيانات محسن
echo.

REM تشغيل التطبيق
npm start

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo.
    echo 🔧 حلول مقترحة:
    echo    1. تأكد من إغلاق أي نسخة أخرى من التطبيق
    echo    2. أعد تشغيل الكمبيوتر
    echo    3. احذف مجلد node_modules وأعد تثبيت التبعيات
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق التطبيق بنجاح
pause
