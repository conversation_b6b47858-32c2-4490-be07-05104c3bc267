<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - إدارة التراخيص</title>
    <link rel="stylesheet" href="license-management.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- الشريط الجانبي -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <span>لوحة تحكم المدير</span>
                </div>
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#requests" onclick="showSection('requests')">
                            <i class="fas fa-inbox"></i>
                            <span>طلبات التفعيل</span>
                            <span class="badge" id="pendingCount">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#licenses" onclick="showSection('licenses')">
                            <i class="fas fa-key"></i>
                            <span>التراخيص النشطة</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#analytics" onclick="showSection('analytics')">
                            <i class="fas fa-chart-bar"></i>
                            <span>الإحصائيات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" onclick="showSection('settings')">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <div class="admin-info">
                    <div class="admin-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="admin-details">
                        <span class="admin-name">المدير العام</span>
                        <span class="admin-role">مدير النظام</span>
                    </div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>تسجيل الخروج</span>
                </button>
            </div>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- الهيدر العلوي -->
            <header class="top-header">
                <div class="header-left">
                    <button class="menu-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="pageTitle">طلبات التفعيل</h1>
                </div>
                <div class="header-right">
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-value" id="totalRequests">0</span>
                            <span class="stat-label">إجمالي الطلبات</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="activeLicenses">0</span>
                            <span class="stat-label">التراخيص النشطة</span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="action-btn" onclick="refreshData()" title="تحديث البيانات">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="action-btn" onclick="toggleTheme()" title="تغيير المظهر">
                            <i class="fas fa-moon" id="themeIcon"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- محتوى الصفحات -->
            <div class="content-area">
                <!-- قسم طلبات التفعيل -->
                <section id="requests-section" class="content-section active">
                    <div class="section-header">
                        <h2>طلبات التفعيل</h2>
                        <div class="section-actions">
                            <div class="filter-group">
                                <select id="statusFilter" onchange="filterRequests()">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending">في الانتظار</option>
                                    <option value="approved">موافق عليه</option>
                                    <option value="rejected">مرفوض</option>
                                </select>
                            </div>
                            <div class="search-group">
                                <input type="text" id="searchRequests" placeholder="البحث..." onkeyup="searchRequests()">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                    </div>

                    <div class="requests-container">
                        <div class="requests-grid" id="requestsGrid">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </section>

                <!-- قسم التراخيص النشطة -->
                <section id="licenses-section" class="content-section">
                    <div class="section-header">
                        <h2>التراخيص النشطة</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" onclick="showCreateLicenseModal()">
                                <i class="fas fa-plus"></i>
                                إنشاء ترخيص جديد
                            </button>
                        </div>
                    </div>

                    <div class="licenses-container">
                        <div class="licenses-table-container">
                            <table class="licenses-table" id="licensesTable">
                                <thead>
                                    <tr>
                                        <th>رقم الترخيص</th>
                                        <th>اسم العميل</th>
                                        <th>نوع الترخيص</th>
                                        <th>تاريخ الإصدار</th>
                                        <th>تاريخ انتهاء الصلاحية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="licensesTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- قسم الإحصائيات -->
                <section id="analytics-section" class="content-section">
                    <div class="section-header">
                        <h2>الإحصائيات والتقارير</h2>
                    </div>

                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <div class="card-header">
                                <h3>إحصائيات عامة</h3>
                            </div>
                            <div class="card-content">
                                <div class="stats-grid">
                                    <div class="stat-box">
                                        <div class="stat-icon requests">
                                            <i class="fas fa-inbox"></i>
                                        </div>
                                        <div class="stat-info">
                                            <span class="stat-number" id="totalRequestsCount">0</span>
                                            <span class="stat-text">إجمالي الطلبات</span>
                                        </div>
                                    </div>
                                    <div class="stat-box">
                                        <div class="stat-icon licenses">
                                            <i class="fas fa-key"></i>
                                        </div>
                                        <div class="stat-info">
                                            <span class="stat-number" id="activeLicensesCount">0</span>
                                            <span class="stat-text">التراخيص النشطة</span>
                                        </div>
                                    </div>
                                    <div class="stat-box">
                                        <div class="stat-icon expired">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="stat-info">
                                            <span class="stat-number" id="expiredLicensesCount">0</span>
                                            <span class="stat-text">التراخيص المنتهية</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="analytics-card">
                            <div class="card-header">
                                <h3>توزيع التراخيص حسب النوع</h3>
                            </div>
                            <div class="card-content">
                                <canvas id="licenseTypesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- قسم الإعدادات -->
                <section id="settings-section" class="content-section">
                    <div class="section-header">
                        <h2>إعدادات النظام</h2>
                    </div>

                    <div class="settings-container">
                        <div class="settings-card">
                            <h3>إعدادات التراخيص</h3>
                            <div class="setting-item">
                                <label>مدة الترخيص التجريبي (بالأيام)</label>
                                <input type="number" id="trialDuration" value="30" min="1" max="365">
                            </div>
                            <div class="setting-item">
                                <label>تفعيل التحديث التلقائي</label>
                                <input type="checkbox" id="autoUpdate" checked>
                            </div>
                            <div class="setting-item">
                                <label>إرسال تنبيهات انتهاء الصلاحية</label>
                                <input type="checkbox" id="expiryNotifications" checked>
                            </div>
                        </div>

                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save"></i>
                                حفظ الإعدادات
                            </button>
                            <button class="btn btn-secondary" onclick="exportData()">
                                <i class="fas fa-download"></i>
                                تصدير البيانات
                            </button>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- نافذة تفاصيل الطلب -->
    <div id="requestDetailsModal" class="modal" style="display: none;">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>تفاصيل طلب التفعيل</h3>
                <span class="close" onclick="closeRequestDetails()">&times;</span>
            </div>
            <div class="modal-body" id="requestDetailsBody">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" onclick="approveRequest()">
                    <i class="fas fa-check"></i>
                    موافقة
                </button>
                <button class="btn btn-danger" onclick="rejectRequest()">
                    <i class="fas fa-times"></i>
                    رفض
                </button>
                <button class="btn btn-secondary" onclick="closeRequestDetails()">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة إنشاء ترخيص -->
    <div id="createLicenseModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إنشاء ترخيص جديد</h3>
                <span class="close" onclick="closeCreateLicense()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="createLicenseForm">
                    <div class="form-group">
                        <label>اسم العميل</label>
                        <input type="text" id="newLicenseCustomerName" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="newLicensePhone" required>
                    </div>
                    <div class="form-group">
                        <label>نوع الترخيص</label>
                        <select id="newLicenseType" required>
                            <option value="trial">تجريبي (30 يوم)</option>
                            <option value="lifetime">مدى الحياة</option>
                            <option value="monthly">شهري</option>
                            <option value="yearly">سنوي</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>معرف الجهاز</label>
                        <input type="text" id="newLicenseMachineId" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="createLicense()">
                    <i class="fas fa-plus"></i>
                    إنشاء الترخيص
                </button>
                <button class="btn btn-secondary" onclick="closeCreateLicense()">إلغاء</button>
            </div>
        </div>
    </div>

    <script>
        // تحميل الثوابت مباشرة
        const ALGERIAN_STATES = {
            '01': 'أدرار', '02': 'الشلف', '03': 'الأغواط', '04': 'أم البواقي', '05': 'باتنة',
            '06': 'بجاية', '07': 'بسكرة', '08': 'بشار', '09': 'البليدة', '10': 'البويرة',
            '11': 'تمنراست', '12': 'تبسة', '13': 'تلمسان', '14': 'تيارت', '15': 'تيزي وزو',
            '16': 'الجزائر', '17': 'الجلفة', '18': 'جيجل', '19': 'سطيف', '20': 'سعيدة',
            '21': 'سكيكدة', '22': 'سيدي بلعباس', '23': 'عنابة', '24': 'قالمة', '25': 'قسنطينة',
            '26': 'المدية', '27': 'مستغانم', '28': 'المسيلة', '29': 'معسكر', '30': 'ورقلة',
            '31': 'وهران', '32': 'البيض', '33': 'إليزي', '34': 'برج بوعريريج', '35': 'بومرداس',
            '36': 'الطارف', '37': 'تندوف', '38': 'تيسمسيلت', '39': 'الوادي', '40': 'خنشلة',
            '41': 'سوق أهراس', '42': 'تيبازة', '43': 'ميلة', '44': 'عين الدفلى', '45': 'النعامة',
            '46': 'عين تموشنت', '47': 'غرداية', '48': 'غليزان', '49': 'تيميمون', '50': 'برج باجي مختار',
            '51': 'أولاد جلال', '52': 'بني عباس', '53': 'عين صالح', '54': 'عين قزام', '55': 'تقرت',
            '56': 'جانت', '57': 'المغير', '58': 'المنيعة'
        };

        const LICENSE_TYPE_LABELS = {
            'trial': 'تجريبي (30 يوم)',
            'lifetime': 'مدى الحياة',
            'monthly': 'شهري',
            'yearly': 'سنوي'
        };

        const LICENSE_STATUS_LABELS = {
            'pending': 'في الانتظار',
            'active': 'نشط',
            'expired': 'منتهي الصلاحية',
            'suspended': 'معلق',
            'revoked': 'ملغي'
        };
    </script>
    <!-- نظام الحماية المحسن -->
    <script src="../../utils/page-guard-fixed.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../../utils/license-manager.js"></script>
    <script src="license-management.js"></script>
</body>
</html>
