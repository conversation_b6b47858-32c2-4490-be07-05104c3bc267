// تحسينات واجهة المستخدم
// UI Enhancements - تحسين تجربة المستخدم

class UIEnhancements {
    constructor() {
        this.initialized = false;
        this.notifications = [];
        this.loadingOverlay = null;
        this.tooltips = new Map();
    }

    // تهيئة التحسينات
    initialize() {
        if (this.initialized) return;

        try {
            this.setupLoadingOverlay();
            this.setupNotificationSystem();
            this.setupTooltips();
            this.setupFormEnhancements();
            this.setupAnimations();
            this.setupKeyboardShortcuts();
            
            this.initialized = true;
            console.log('✨ تم تهيئة تحسينات واجهة المستخدم');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة تحسينات الواجهة:', error);
        }
    }

    // إعداد شاشة التحميل
    setupLoadingOverlay() {
        this.loadingOverlay = document.createElement('div');
        this.loadingOverlay.id = 'globalLoadingOverlay';
        this.loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        `;

        this.loadingOverlay.innerHTML = `
            <div style="
                background: white;
                padding: 2rem;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                max-width: 300px;
                direction: rtl;
            ">
                <div class="loading-spinner" style="
                    width: 50px;
                    height: 50px;
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #3498db;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 1rem auto;
                "></div>
                <h3 id="loadingTitle" style="color: #2c3e50; margin-bottom: 0.5rem;">جاري التحميل...</h3>
                <p id="loadingMessage" style="color: #7f8c8d; margin: 0;">يرجى الانتظار</p>
                <div style="
                    width: 100%;
                    height: 4px;
                    background: #ecf0f1;
                    border-radius: 2px;
                    margin-top: 1rem;
                    overflow: hidden;
                ">
                    <div id="loadingProgress" style="
                        height: 100%;
                        background: linear-gradient(90deg, #3498db, #2ecc71);
                        width: 0%;
                        transition: width 0.3s ease;
                        border-radius: 2px;
                    "></div>
                </div>
            </div>
        `;

        // إضافة CSS للأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(this.loadingOverlay);
    }

    // إظهار شاشة التحميل
    showLoading(title = 'جاري التحميل...', message = 'يرجى الانتظار') {
        if (this.loadingOverlay) {
            document.getElementById('loadingTitle').textContent = title;
            document.getElementById('loadingMessage').textContent = message;
            document.getElementById('loadingProgress').style.width = '0%';
            this.loadingOverlay.style.display = 'flex';
        }
    }

    // تحديث تقدم التحميل
    updateLoadingProgress(percent, message = null) {
        if (this.loadingOverlay) {
            document.getElementById('loadingProgress').style.width = percent + '%';
            if (message) {
                document.getElementById('loadingMessage').textContent = message;
            }
        }
    }

    // إخفاء شاشة التحميل
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    }

    // إعداد نظام الإشعارات
    setupNotificationSystem() {
        // إنشاء حاوي الإشعارات
        const container = document.createElement('div');
        container.id = 'notificationContainer';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 10000;
            pointer-events: none;
            direction: rtl;
        `;
        document.body.appendChild(container);
    }

    // إظهار إشعار
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        const id = 'notification_' + Date.now();
        notification.id = id;
        
        const colors = {
            success: { bg: '#2ecc71', icon: 'check-circle' },
            error: { bg: '#e74c3c', icon: 'times-circle' },
            warning: { bg: '#f39c12', icon: 'exclamation-triangle' },
            info: { bg: '#3498db', icon: 'info-circle' }
        };
        
        const color = colors[type] || colors.info;
        
        notification.style.cssText = `
            background: ${color.bg};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            max-width: 400px;
            pointer-events: auto;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            cursor: pointer;
        `;
        
        notification.innerHTML = `
            <i class="fas fa-${color.icon}"></i>
            <span>${message}</span>
            <i class="fas fa-times" style="margin-right: auto; opacity: 0.7; cursor: pointer;"></i>
        `;
        
        // إضافة للحاوي
        const container = document.getElementById('notificationContainer');
        container.appendChild(notification);
        
        // أنيميشن الدخول
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // معالج الإغلاق
        const closeBtn = notification.querySelector('.fa-times');
        const closeNotification = () => {
            notification.style.transform = 'translateX(-100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        };
        
        closeBtn.addEventListener('click', closeNotification);
        notification.addEventListener('click', closeNotification);
        
        // إغلاق تلقائي
        if (duration > 0) {
            setTimeout(closeNotification, duration);
        }
        
        return id;
    }

    // إعداد التلميحات
    setupTooltips() {
        // إضافة CSS للتلميحات
        const style = document.createElement('style');
        style.textContent = `
            .tooltip {
                position: relative;
                cursor: help;
            }
            
            .tooltip-content {
                position: absolute;
                bottom: 125%;
                left: 50%;
                transform: translateX(-50%);
                background: #2c3e50;
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                font-size: 0.8rem;
                white-space: nowrap;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                z-index: 1000;
                pointer-events: none;
            }
            
            .tooltip-content::after {
                content: '';
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                border: 5px solid transparent;
                border-top-color: #2c3e50;
            }
            
            .tooltip:hover .tooltip-content {
                opacity: 1;
                visibility: visible;
            }
        `;
        document.head.appendChild(style);
    }

    // إضافة تلميح
    addTooltip(element, text) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return;
        
        element.classList.add('tooltip');
        
        const tooltipContent = document.createElement('div');
        tooltipContent.className = 'tooltip-content';
        tooltipContent.textContent = text;
        
        element.appendChild(tooltipContent);
        this.tooltips.set(element, tooltipContent);
    }

    // إعداد تحسينات النماذج
    setupFormEnhancements() {
        // تحسين حقول الإدخال
        document.addEventListener('focusin', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                e.target.style.transform = 'scale(1.02)';
                e.target.style.transition = 'transform 0.2s ease';
            }
        });
        
        document.addEventListener('focusout', (e) => {
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                e.target.style.transform = 'scale(1)';
            }
        });
        
        // تحسين الأزرار
        document.addEventListener('mousedown', (e) => {
            if (e.target.tagName === 'BUTTON') {
                e.target.style.transform = 'scale(0.95)';
                e.target.style.transition = 'transform 0.1s ease';
            }
        });
        
        document.addEventListener('mouseup', (e) => {
            if (e.target.tagName === 'BUTTON') {
                e.target.style.transform = 'scale(1)';
            }
        });
    }

    // إعداد الأنيميشن
    setupAnimations() {
        // أنيميشن ظهور العناصر
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // مراقبة العناصر الجديدة
        const observeNewElements = () => {
            document.querySelectorAll('.fade-in:not(.observed)').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                el.classList.add('observed');
                observer.observe(el);
            });
        };
        
        // مراقبة أولية
        observeNewElements();
        
        // مراقبة العناصر الجديدة
        const mutationObserver = new MutationObserver(observeNewElements);
        mutationObserver.observe(document.body, { childList: true, subtree: true });
    }

    // إعداد اختصارات لوحة المفاتيح
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter لإرسال النماذج
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                const activeForm = document.activeElement.closest('form');
                if (activeForm) {
                    const submitBtn = activeForm.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        submitBtn.click();
                    }
                }
            }
            
            // Escape لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal[style*="display: flex"], .modal[style*="display: block"]');
                modals.forEach(modal => {
                    modal.style.display = 'none';
                });
                
                // إخفاء شاشة التحميل
                this.hideLoading();
            }
        });
    }

    // إضافة تأثير الموجة للأزرار
    addRippleEffect(button) {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // إضافة CSS للأنيميشن
        if (!document.querySelector('#rippleAnimation')) {
            const style = document.createElement('style');
            style.id = 'rippleAnimation';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // تحسين الأداء بتأخير التحميل
    lazyLoadImages() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }

    // إضافة مؤشر التقدم للصفحة
    addPageProgressBar() {
        const progressBar = document.createElement('div');
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            z-index: 10000;
            transition: width 0.3s ease;
        `;
        document.body.appendChild(progressBar);
        
        const updateProgress = () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            progressBar.style.width = scrollPercent + '%';
        };
        
        window.addEventListener('scroll', updateProgress);
        return progressBar;
    }

    // تطبيق جميع التحسينات على عنصر
    enhanceElement(element) {
        if (element.tagName === 'BUTTON') {
            this.addRippleEffect(element);
        }
        
        if (element.classList.contains('tooltip-enabled')) {
            const tooltipText = element.getAttribute('data-tooltip');
            if (tooltipText) {
                this.addTooltip(element, tooltipText);
            }
        }
        
        if (element.classList.contains('fade-in')) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        }
    }
}

// إنشاء مثيل واحد للاستخدام العام
const uiEnhancements = new UIEnhancements();

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    uiEnhancements.initialize();
});

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UIEnhancements, uiEnhancements };
} else {
    window.UIEnhancements = UIEnhancements;
    window.uiEnhancements = uiEnhancements;
}
