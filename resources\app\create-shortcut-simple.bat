@echo off
chcp 65001 >nul
echo ========================================
echo     تثبيت مؤسسة وقود المستقبل
echo     Future Fuel Corporation
echo ========================================
echo.

echo 🔧 إنشاء اختصار سطح المكتب...

:: الحصول على مسار سطح المكتب
for /f "tokens=3*" %%i in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop') do set DESKTOP=%%i %%j

:: إنشاء ملف VBS لإنشاء الاختصار
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%DESKTOP%\مؤسسة وقود المستقبل.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%~dp0run-app.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%~dp0" >> CreateShortcut.vbs
echo oLink.Description = "نظام إدارة مؤسسة وقود المستقبل" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

:: تشغيل ملف VBS
cscript CreateShortcut.vbs >nul

:: حذف ملف VBS المؤقت
del CreateShortcut.vbs

if exist "%DESKTOP%\مؤسسة وقود المستقبل.lnk" (
    echo ✅ تم إنشاء اختصار سطح المكتب بنجاح!
    echo.
    echo 📁 مكان الاختصار: %DESKTOP%
    echo 🎯 اسم الاختصار: مؤسسة وقود المستقبل
    echo.
    echo يمكنك الآن النقر المزدوج على الاختصار لتشغيل التطبيق
) else (
    echo ❌ فشل في إنشاء الاختصار
)

echo.
echo ========================================
echo تم الانتهاء من التثبيت!
echo ========================================
echo.

set /p choice="هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%choice%"=="y" (
    echo 🚀 تشغيل التطبيق...
    start "" "%~dp0run-app.bat"
) else (
    echo يمكنك تشغيل التطبيق لاحقاً من اختصار سطح المكتب
)

echo.
pause
