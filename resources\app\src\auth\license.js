// نظام الترخيص والتفعيل المتقدم
class LicenseSystem {
    constructor() {
        this.licenseKey = null;
        this.licenseData = null;
        this.serverUrl = 'https://license-server.futurefuel.sa'; // في التطبيق الحقيقي
        this.deviceId = this.generateDeviceId();
        this.init();
    }

    init() {
        console.log('🔐 تهيئة نظام الترخيص...');
        this.loadLocalLicense();
        this.startLicenseValidation();
        
        // فحص الترخيص كل ساعة
        setInterval(() => this.validateLicense(), 3600000);
    }

    generateDeviceId() {
        // إنشاء معرف فريد للجهاز
        let deviceId = localStorage.getItem('deviceId');
        if (!deviceId) {
            deviceId = 'DEV-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('deviceId', deviceId);
        }
        return deviceId;
    }

    loadLocalLicense() {
        try {
            const storedLicense = localStorage.getItem('appLicense');
            if (storedLicense) {
                this.licenseData = JSON.parse(storedLicense);
                this.licenseKey = this.licenseData.key;
                console.log('📄 تم تحميل الترخيص المحلي');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل الترخيص المحلي:', error);
            this.clearLicense();
        }
    }

    async validateLicense() {
        if (!this.licenseData) {
            console.log('⚠️ لا يوجد ترخيص للتحقق منه');
            return false;
        }

        try {
            console.log('🔍 التحقق من صحة الترخيص...');
            
            // فحص محلي أولاً
            const localValidation = this.validateLocalLicense();
            if (!localValidation.valid) {
                console.log('❌ فشل التحقق المحلي:', localValidation.reason);
                return false;
            }

            // فحص عن بُعد
            const remoteValidation = await this.validateRemoteLicense();
            if (!remoteValidation.valid) {
                console.log('❌ فشل التحقق عن بُعد:', remoteValidation.reason);
                return false;
            }

            console.log('✅ الترخيص صالح');
            return true;

        } catch (error) {
            console.error('❌ خطأ في التحقق من الترخيص:', error);
            return false;
        }
    }

    validateLocalLicense() {
        if (!this.licenseData) {
            return { valid: false, reason: 'لا يوجد ترخيص' };
        }

        // فحص تاريخ الانتهاء
        const now = new Date();
        const expiryDate = new Date(this.licenseData.expiryDate);
        
        if (now > expiryDate) {
            return { valid: false, reason: 'الترخيص منتهي الصلاحية' };
        }

        // فحص معرف الجهاز
        if (this.licenseData.deviceId && this.licenseData.deviceId !== this.deviceId) {
            return { valid: false, reason: 'الترخيص غير صالح لهذا الجهاز' };
        }

        // فحص الحالة
        if (this.licenseData.status !== 'active') {
            return { valid: false, reason: 'الترخيص غير نشط' };
        }

        // فحص التوقيع (محاكاة)
        const expectedSignature = this.generateSignature(this.licenseData);
        if (this.licenseData.signature !== expectedSignature) {
            return { valid: false, reason: 'توقيع الترخيص غير صحيح' };
        }

        return { valid: true };
    }

    async validateRemoteLicense() {
        try {
            // محاكاة التحقق عن بُعد
            await new Promise(resolve => setTimeout(resolve, 1000));

            // في التطبيق الحقيقي، سيتم إرسال طلب إلى الخادم
            const response = await this.simulateServerValidation();
            
            return response;
        } catch (error) {
            console.warn('⚠️ فشل الاتصال بخادم الترخيص، الاعتماد على التحقق المحلي');
            return { valid: true }; // السماح بالعمل في حالة عدم الاتصال
        }
    }

    async simulateServerValidation() {
        // محاكاة استجابة الخادم
        const responses = [
            { valid: true },
            { valid: false, reason: 'الترخيص معلق من الخادم' },
            { valid: false, reason: 'تم إلغاء الترخيص' }
        ];

        // إرجاع استجابة عشوائية للاختبار (في الواقع ستكون دائماً صحيحة)
        return { valid: true };
    }

    generateSignature(licenseData) {
        // محاكاة إنشاء توقيع رقمي
        const dataString = `${licenseData.key}-${licenseData.deviceId}-${licenseData.expiryDate}`;
        return btoa(dataString).substr(0, 16);
    }

    async activateLicense(licenseKey, activationData) {
        try {
            console.log('🔑 تفعيل الترخيص...', licenseKey);

            // التحقق من صحة مفتاح الترخيص
            const validationResult = await this.validateLicenseKey(licenseKey);
            if (!validationResult.valid) {
                throw new Error(validationResult.reason || 'مفتاح الترخيص غير صحيح');
            }

            // إنشاء بيانات الترخيص
            const licenseData = {
                key: licenseKey,
                deviceId: this.deviceId,
                activationDate: new Date().toISOString(),
                expiryDate: this.calculateExpiryDate(validationResult.duration),
                status: 'active',
                type: validationResult.type || 'basic',
                clientInfo: activationData,
                signature: null
            };

            // إنشاء التوقيع
            licenseData.signature = this.generateSignature(licenseData);

            // حفظ الترخيص محلياً
            this.licenseData = licenseData;
            this.licenseKey = licenseKey;
            localStorage.setItem('appLicense', JSON.stringify(licenseData));

            // إرسال تأكيد التفعيل إلى الخادم
            await this.confirmActivation(licenseData);

            console.log('✅ تم تفعيل الترخيص بنجاح');
            return { success: true, licenseData };

        } catch (error) {
            console.error('❌ فشل في تفعيل الترخيص:', error);
            throw error;
        }
    }

    async validateLicenseKey(licenseKey) {
        // محاكاة التحقق من مفتاح الترخيص مع الخادم
        await new Promise(resolve => setTimeout(resolve, 1500));

        // تنسيق مفتاح الترخيص المتوقع: FF-XXXX-XXXX-XXXX
        const keyPattern = /^FF-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
        
        if (!keyPattern.test(licenseKey)) {
            return { valid: false, reason: 'تنسيق مفتاح الترخيص غير صحيح' };
        }

        // محاكاة قاعدة بيانات التراخيص
        const validKeys = {
            'FF-DEMO-2024-TEST': { type: 'demo', duration: 30 },
            'FF-BASIC-2024-001': { type: 'basic', duration: 365 },
            'FF-PREM-2024-001': { type: 'premium', duration: 365 },
            'FF-ENTER-2024-001': { type: 'enterprise', duration: 365 }
        };

        const keyData = validKeys[licenseKey];
        if (!keyData) {
            return { valid: false, reason: 'مفتاح الترخيص غير موجود في قاعدة البيانات' };
        }

        return { 
            valid: true, 
            type: keyData.type, 
            duration: keyData.duration 
        };
    }

    calculateExpiryDate(durationDays) {
        const now = new Date();
        const expiryDate = new Date(now.getTime() + (durationDays * 24 * 60 * 60 * 1000));
        return expiryDate.toISOString();
    }

    async confirmActivation(licenseData) {
        // محاكاة إرسال تأكيد التفعيل إلى الخادم
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        console.log('📤 تم إرسال تأكيد التفعيل إلى الخادم');
        
        // في التطبيق الحقيقي، سيتم إرسال البيانات إلى الخادم
        return { success: true };
    }

    async requestActivation(userData) {
        try {
            console.log('📝 إرسال طلب تفعيل...', userData);

            const requestData = {
                ...userData,
                deviceId: this.deviceId,
                deviceInfo: this.getDeviceInfo(),
                requestDate: new Date().toISOString(),
                appVersion: '2.2.0'
            };

            // محاولة إرسال الطلب إلى الخادم (محاكاة)
            try {
                await this.submitActivationRequest(requestData);
                console.log('✅ تم إرسال طلب التفعيل بنجاح');
            } catch (err) {
                // إذا فشل الإرسال، احفظ الطلب محلياً
                console.warn('⚠️ لم يتم إرسال الطلب للخادم، سيتم حفظه محلياً');
            }

            // حفظ الطلب محلياً دائماً
            this.saveActivationRequest(requestData);

            return { success: true, requestId: this.generateRequestId() };
        } catch (error) {
            console.error('❌ فشل في إرسال طلب التفعيل:', error);
            // لا ترمي الخطأ للمستخدم، اعتبر الطلب ناجحاً محلياً
            return { success: true, requestId: this.generateRequestId(), localOnly: true };
        }
    }

    async submitActivationRequest(requestData) {
        // محاكاة إرسال الطلب إلى الخادم
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // في التطبيق الحقيقي، سيتم إرسال البيانات عبر API
        console.log('📤 تم إرسال طلب التفعيل إلى الخادم');
        
        return { success: true };
    }

    saveActivationRequest(requestData) {
        const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
        requests.push(requestData);
        localStorage.setItem('activationRequests', JSON.stringify(requests));
    }

    generateRequestId() {
        return 'REQ-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            cpuCores: navigator.hardwareConcurrency || 'غير معروف',
            memory: navigator.deviceMemory || 'غير معروف'
        };
    }

    getLicenseInfo() {
        if (!this.licenseData) {
            return null;
        }

        return {
            key: this.licenseKey,
            type: this.licenseData.type,
            status: this.licenseData.status,
            activationDate: this.licenseData.activationDate,
            expiryDate: this.licenseData.expiryDate,
            daysRemaining: this.getDaysRemaining(),
            clientInfo: this.licenseData.clientInfo
        };
    }

    getDaysRemaining() {
        if (!this.licenseData) return 0;

        const now = new Date();
        const expiryDate = new Date(this.licenseData.expiryDate);
        const diffTime = expiryDate - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return Math.max(0, diffDays);
    }

    isLicenseValid() {
        if (!this.licenseData) return false;

        const localValidation = this.validateLocalLicense();
        return localValidation.valid;
    }

    isLicenseExpiringSoon(days = 7) {
        const remaining = this.getDaysRemaining();
        return remaining > 0 && remaining <= days;
    }

    clearLicense() {
        this.licenseData = null;
        this.licenseKey = null;
        localStorage.removeItem('appLicense');
        console.log('🗑️ تم مسح بيانات الترخيص');
    }

    async renewLicense() {
        if (!this.licenseData) {
            throw new Error('لا يوجد ترخيص للتجديد');
        }

        try {
            console.log('🔄 تجديد الترخيص...');

            // محاكاة طلب التجديد
            await new Promise(resolve => setTimeout(resolve, 2000));

            // تحديث تاريخ الانتهاء
            const newExpiryDate = this.calculateExpiryDate(365);
            this.licenseData.expiryDate = newExpiryDate;
            this.licenseData.signature = this.generateSignature(this.licenseData);

            // حفظ الترخيص المحدث
            localStorage.setItem('appLicense', JSON.stringify(this.licenseData));

            console.log('✅ تم تجديد الترخيص بنجاح');
            return { success: true, newExpiryDate };

        } catch (error) {
            console.error('❌ فشل في تجديد الترخيص:', error);
            throw error;
        }
    }

    startLicenseValidation() {
        // فحص دوري للترخيص
        setInterval(() => {
            if (this.isLicenseExpiringSoon()) {
                this.notifyExpiringLicense();
            }
        }, 3600000); // كل ساعة
    }

    notifyExpiringLicense() {
        const daysRemaining = this.getDaysRemaining();
        const message = `تنبيه: سينتهي الترخيص خلال ${daysRemaining} أيام. يرجى التجديد قريباً.`;
        
        // إظهار إشعار
        if (typeof addNotification === 'function') {
            addNotification('تنبيه الترخيص', message, 'warning');
        }
        
        console.warn('⚠️', message);
    }

    // دوال للاختبار والتطوير
    createTestLicense(type = 'demo', days = 30) {
        const testLicense = {
            key: `FF-TEST-${type.toUpperCase()}-${Date.now().toString().substr(-4)}`,
            deviceId: this.deviceId,
            activationDate: new Date().toISOString(),
            expiryDate: this.calculateExpiryDate(days),
            status: 'active',
            type: type,
            clientInfo: {
                firstName: 'مستخدم',
                lastName: 'تجريبي',
                phone: '**********',
                state: 'الجزائر',
                municipality: 'الجزائر الوسطى'
            },
            signature: null
        };

        testLicense.signature = this.generateSignature(testLicense);
        
        this.licenseData = testLicense;
        this.licenseKey = testLicense.key;
        localStorage.setItem('appLicense', JSON.stringify(testLicense));

        console.log('🧪 تم إنشاء ترخيص تجريبي:', testLicense.key);
        return testLicense;
    }

    exportLicenseInfo() {
        if (!this.licenseData) {
            return null;
        }

        return {
            key: this.licenseKey,
            type: this.licenseData.type,
            status: this.licenseData.status,
            activationDate: this.licenseData.activationDate,
            expiryDate: this.licenseData.expiryDate,
            daysRemaining: this.getDaysRemaining(),
            deviceId: this.deviceId,
            isValid: this.isLicenseValid(),
            isExpiringSoon: this.isLicenseExpiringSoon()
        };
    }
}

// إنشاء مثيل عام لنظام الترخيص
window.licenseSystem = new LicenseSystem();

// تصدير للاستخدام في وحدات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LicenseSystem;
}
