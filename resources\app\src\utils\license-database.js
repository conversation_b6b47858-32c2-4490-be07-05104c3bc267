// قاعدة بيانات التراخيص المحسنة

class LicenseDatabase {
    constructor() {
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;
        this.dbPath = this.getDbPath();
        this.cache = new Map();
        this.initialized = false;
        this.encryptionKey = 'FutureFuel2024License';
    }

    // الحصول على مسار قاعدة البيانات
    getDbPath() {
        if (this.isElectron) {
            const path = require('path');
            const os = require('os');
            return {
                licenses: path.join(os.homedir(), 'FutureFuelData', 'licenses.json'),
                requests: path.join(os.homedir(), 'FutureFuelData', 'activation-requests.json'),
                devices: path.join(os.homedir(), 'FutureFuelData', 'registered-devices.json'),
                logs: path.join(os.homedir(), 'FutureFuelData', 'license-logs.json')
            };
        }
        return {
            licenses: 'localStorage:licenses',
            requests: 'localStorage:activationRequests',
            devices: 'localStorage:registeredDevices',
            logs: 'localStorage:licenseLogs'
        };
    }

    // تهيئة قاعدة البيانات
    async initialize() {
        if (this.initialized) return;

        try {
            await this.createDirectoryIfNotExists();
            await this.loadInitialData();
            await this.setupAutoBackup();
            this.initialized = true;
            console.log('تم تهيئة قاعدة بيانات التراخيص بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة قاعدة بيانات التراخيص:', error);
            throw error;
        }
    }

    // إنشاء المجلد إذا لم يكن موجوداً
    async createDirectoryIfNotExists() {
        if (!this.isElectron) return;

        const fs = require('fs').promises;
        const path = require('path');
        const dir = path.dirname(this.dbPath.licenses);

        try {
            await fs.access(dir);
        } catch {
            await fs.mkdir(dir, { recursive: true });
        }
    }

    // تحميل البيانات الأولية
    async loadInitialData() {
        const tables = ['licenses', 'requests', 'devices', 'logs'];
        
        for (const table of tables) {
            const data = await this.loadTable(table);
            if (!data || data.length === 0) {
                await this.saveTable(table, []);
            }
        }
    }

    // تحميل جدول
    async loadTable(tableName) {
        try {
            if (this.isElectron) {
                return await this.loadFromFile(tableName);
            } else {
                return this.loadFromLocalStorage(tableName);
            }
        } catch (error) {
            console.error(`خطأ في تحميل جدول ${tableName}:`, error);
            return [];
        }
    }

    // تحميل من الملف (Electron)
    async loadFromFile(tableName) {
        const fs = require('fs').promises;
        const filePath = this.dbPath[tableName];
        
        try {
            const data = await fs.readFile(filePath, 'utf8');
            const decryptedData = this.decryptData(data);
            const parsedData = JSON.parse(decryptedData);
            
            // تحديث الكاش
            this.cache.set(tableName, parsedData);
            
            return parsedData;
        } catch (error) {
            if (error.code === 'ENOENT') {
                return []; // الملف غير موجود
            }
            throw error;
        }
    }

    // تحميل من localStorage (المتصفح)
    loadFromLocalStorage(tableName) {
        try {
            const key = this.dbPath[tableName].split(':')[1];
            const data = localStorage.getItem(key);
            if (data) {
                const decryptedData = this.decryptData(data);
                const parsedData = JSON.parse(decryptedData);
                this.cache.set(tableName, parsedData);
                return parsedData;
            }
            return [];
        } catch (error) {
            console.error(`خطأ في تحميل ${tableName} من localStorage:`, error);
            return [];
        }
    }

    // حفظ جدول
    async saveTable(tableName, data) {
        try {
            // تحديث الكاش
            this.cache.set(tableName, data);
            
            if (this.isElectron) {
                await this.saveToFile(tableName, data);
            } else {
                this.saveToLocalStorage(tableName, data);
            }

            // تسجيل العملية
            await this.logOperation('save', tableName, data.length);
            
            console.log(`تم حفظ جدول ${tableName} بنجاح`);
            return true;
        } catch (error) {
            console.error(`خطأ في حفظ جدول ${tableName}:`, error);
            throw error;
        }
    }

    // حفظ في الملف (Electron)
    async saveToFile(tableName, data) {
        const fs = require('fs').promises;
        const filePath = this.dbPath[tableName];
        
        // إنشاء نسخة احتياطية أولاً
        await this.createBackup(tableName);
        
        // تشفير وحفظ البيانات
        const encryptedData = this.encryptData(JSON.stringify(data, null, 2));
        await fs.writeFile(filePath, encryptedData, 'utf8');
    }

    // حفظ في localStorage (المتصفح)
    saveToLocalStorage(tableName, data) {
        const key = this.dbPath[tableName].split(':')[1];
        const encryptedData = this.encryptData(JSON.stringify(data));
        localStorage.setItem(key, encryptedData);
    }

    // إضافة سجل جديد
    async addRecord(tableName, record) {
        const table = await this.loadTable(tableName);
        
        // إضافة معرف فريد إذا لم يكن موجوداً
        if (!record.id) {
            record.id = this.generateId();
        }
        
        // إضافة طوابع زمنية
        record.createdAt = record.createdAt || new Date().toISOString();
        record.updatedAt = new Date().toISOString();
        
        table.push(record);
        await this.saveTable(tableName, table);
        
        return record;
    }

    // تحديث سجل
    async updateRecord(tableName, id, updates) {
        const table = await this.loadTable(tableName);
        const index = table.findIndex(record => record.id === id);
        
        if (index === -1) {
            throw new Error(`السجل غير موجود: ${id}`);
        }
        
        // تحديث السجل
        table[index] = {
            ...table[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        
        await this.saveTable(tableName, table);
        return table[index];
    }

    // حذف سجل
    async deleteRecord(tableName, id) {
        const table = await this.loadTable(tableName);
        const index = table.findIndex(record => record.id === id);
        
        if (index === -1) {
            throw new Error(`السجل غير موجود: ${id}`);
        }
        
        const deletedRecord = table.splice(index, 1)[0];
        await this.saveTable(tableName, table);
        
        return deletedRecord;
    }

    // البحث في الجدول
    async searchRecords(tableName, query, fields = []) {
        const table = await this.loadTable(tableName);
        
        if (!query) return table;
        
        const searchTerm = query.toLowerCase();
        
        return table.filter(record => {
            if (fields.length === 0) {
                // البحث في جميع الحقول
                return Object.values(record).some(value => 
                    value && value.toString().toLowerCase().includes(searchTerm)
                );
            } else {
                // البحث في حقول محددة
                return fields.some(field => 
                    record[field] && record[field].toString().toLowerCase().includes(searchTerm)
                );
            }
        });
    }

    // فلترة السجلات
    async filterRecords(tableName, filterFn) {
        const table = await this.loadTable(tableName);
        return table.filter(filterFn);
    }

    // ترتيب السجلات
    async sortRecords(tableName, sortField, sortOrder = 'asc') {
        const table = await this.loadTable(tableName);
        
        return table.sort((a, b) => {
            const aValue = a[sortField];
            const bValue = b[sortField];
            
            if (sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    // الحصول على إحصائيات
    async getStatistics() {
        const licenses = await this.loadTable('licenses');
        const requests = await this.loadTable('requests');
        
        const stats = {
            totalLicenses: licenses.length,
            activeLicenses: licenses.filter(l => l.status === 'active').length,
            expiredLicenses: licenses.filter(l => l.status === 'expired').length,
            suspendedLicenses: licenses.filter(l => l.status === 'suspended').length,
            
            totalRequests: requests.length,
            pendingRequests: requests.filter(r => r.status === 'pending').length,
            approvedRequests: requests.filter(r => r.status === 'approved').length,
            rejectedRequests: requests.filter(r => r.status === 'rejected').length,
            
            licenseTypes: this.groupBy(licenses, 'licenseType'),
            requestsByState: this.groupBy(requests, 'state'),
            
            lastUpdated: new Date().toISOString()
        };
        
        return stats;
    }

    // تجميع البيانات حسب حقل معين
    groupBy(array, field) {
        return array.reduce((groups, item) => {
            const key = item[field] || 'غير محدد';
            groups[key] = (groups[key] || 0) + 1;
            return groups;
        }, {});
    }

    // إنشاء نسخة احتياطية
    async createBackup(tableName = null) {
        if (!this.isElectron) return;

        try {
            const fs = require('fs').promises;
            const path = require('path');
            
            const backupDir = path.join(path.dirname(this.dbPath.licenses), 'backups');
            await fs.mkdir(backupDir, { recursive: true });
            
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            
            if (tableName) {
                // نسخ احتياطية لجدول واحد
                const data = await this.loadFromFile(tableName);
                const backupPath = path.join(backupDir, `${tableName}-${timestamp}.json`);
                await fs.writeFile(backupPath, JSON.stringify(data, null, 2));
            } else {
                // نسخ احتياطية لجميع الجداول
                const tables = ['licenses', 'requests', 'devices', 'logs'];
                const allData = {};
                
                for (const table of tables) {
                    allData[table] = await this.loadTable(table);
                }
                
                const backupPath = path.join(backupDir, `full-backup-${timestamp}.json`);
                await fs.writeFile(backupPath, JSON.stringify(allData, null, 2));
            }
            
            console.log('تم إنشاء نسخة احتياطية بنجاح');
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        }
    }

    // إعداد النسخ الاحتياطية التلقائية
    async setupAutoBackup() {
        // إنشاء نسخة احتياطية كل 24 ساعة
        setInterval(async () => {
            await this.createBackup();
        }, 24 * 60 * 60 * 1000);
        
        // إنشاء نسخة احتياطية عند بدء التشغيل
        setTimeout(async () => {
            await this.createBackup();
        }, 5000);
    }

    // تسجيل العمليات
    async logOperation(operation, tableName, recordCount = null) {
        try {
            const logEntry = {
                id: this.generateId(),
                operation: operation,
                tableName: tableName,
                recordCount: recordCount,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent || 'Unknown'
            };
            
            const logs = await this.loadTable('logs');
            logs.push(logEntry);
            
            // الاحتفاظ بآخر 1000 سجل فقط
            if (logs.length > 1000) {
                logs.splice(0, logs.length - 1000);
            }
            
            await this.saveTable('logs', logs);
        } catch (error) {
            console.error('خطأ في تسجيل العملية:', error);
        }
    }

    // تشفير البيانات
    encryptData(data) {
        try {
            // تشفير بسيط - يمكن تحسينه لاحقاً
            const encrypted = btoa(unescape(encodeURIComponent(data + this.encryptionKey)));
            return encrypted;
        } catch {
            return data;
        }
    }

    // فك تشفير البيانات
    decryptData(encryptedData) {
        try {
            const decrypted = decodeURIComponent(escape(atob(encryptedData)));
            return decrypted.replace(this.encryptionKey, '');
        } catch {
            return encryptedData;
        }
    }

    // إنشاء معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // تنظيف الكاش
    clearCache() {
        this.cache.clear();
    }

    // تصدير البيانات
    async exportData(format = 'json') {
        const data = {
            licenses: await this.loadTable('licenses'),
            requests: await this.loadTable('requests'),
            devices: await this.loadTable('devices'),
            exportDate: new Date().toISOString(),
            version: '3.0.0'
        };
        
        switch (format) {
            case 'json':
                return JSON.stringify(data, null, 2);
            case 'csv':
                return this.convertToCSV(data);
            default:
                throw new Error(`تنسيق غير مدعوم: ${format}`);
        }
    }

    // تحويل إلى CSV
    convertToCSV(data) {
        let csv = '';
        
        // تصدير التراخيص
        if (data.licenses.length > 0) {
            csv += '=== التراخيص ===\n';
            const headers = Object.keys(data.licenses[0]);
            csv += headers.join(',') + '\n';
            
            data.licenses.forEach(license => {
                const values = headers.map(header => 
                    license[header] ? `"${license[header]}"` : ''
                );
                csv += values.join(',') + '\n';
            });
        }
        
        // تصدير الطلبات
        if (data.requests.length > 0) {
            csv += '\n=== طلبات التفعيل ===\n';
            const headers = Object.keys(data.requests[0]);
            csv += headers.join(',') + '\n';
            
            data.requests.forEach(request => {
                const values = headers.map(header => 
                    request[header] ? `"${request[header]}"` : ''
                );
                csv += values.join(',') + '\n';
            });
        }
        
        return csv;
    }
}

// إنشاء مثيل واحد للاستخدام العام
const licenseDatabase = new LicenseDatabase();

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LicenseDatabase, licenseDatabase };
} else {
    window.LicenseDatabase = LicenseDatabase;
    window.licenseDatabase = licenseDatabase;
}
