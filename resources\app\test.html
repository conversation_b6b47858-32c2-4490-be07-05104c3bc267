<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        p {
            font-size: 1.2em;
            margin: 10px 0;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🎉 التطبيق يعمل بنجاح!</h1>
    <p class="success">تم تحميل التطبيق بنجاح</p>
    <p>مؤسسة وقود المستقبل</p>
    <p>نظام الإدارة المتطور</p>
    
    <script>
        console.log('تم تحميل صفحة الاختبار بنجاح');
        
        // اختبار الاتصال مع العملية الرئيسية
        if (window.electronAPI) {
            console.log('electronAPI متوفر');
        } else {
            console.log('electronAPI غير متوفر');
        }
        
        setTimeout(() => {
            console.log('التطبيق جاهز للاستخدام');
        }, 1000);
    </script>
</body>
</html>
