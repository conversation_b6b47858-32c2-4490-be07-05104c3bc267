<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo i {
            font-size: 2em;
            color: #667eea;
        }

        .logo h1 {
            color: #333;
            font-size: 1.8em;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card h3 i {
            color: #667eea;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .license-form {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }

        .license-list {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .license-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .license-item:last-child {
            border-bottom: none;
        }

        .license-info h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .license-info p {
            color: #666;
            font-size: 14px;
        }

        .license-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-trial {
            background: #fff3cd;
            color: #856404;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            <i class="fas fa-gas-pump"></i>
            <h1>مؤسسة وقود المستقبل</h1>
        </div>
        <div class="user-info">
            <span>مرحباً، المدير</span>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل خروج
            </button>
        </div>
    </div>

    <div class="container">
        <!-- إحصائيات سريعة -->
        <div class="dashboard-grid">
            <div class="card">
                <h3><i class="fas fa-key"></i> إحصائيات التراخيص</h3>
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number" id="totalLicenses">0</div>
                        <div class="stat-label">إجمالي التراخيص</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="activeLicenses">0</div>
                        <div class="stat-label">تراخيص نشطة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="trialLicenses">0</div>
                        <div class="stat-label">تراخيص تجريبية</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="expiredLicenses">0</div>
                        <div class="stat-label">تراخيص منتهية</div>
                    </div>
                </div>
                <button class="btn" onclick="refreshStats()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث الإحصائيات
                </button>
            </div>

            <div class="card">
                <h3><i class="fas fa-plus-circle"></i> إجراءات سريعة</h3>
                <button class="btn" onclick="showCreateLicenseForm()">
                    <i class="fas fa-key"></i>
                    إنشاء ترخيص جديد
                </button>
                <button class="btn" onclick="exportLicenses()">
                    <i class="fas fa-download"></i>
                    تصدير التراخيص
                </button>
                <button class="btn" onclick="showSettings()">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
            </div>
        </div>

        <!-- نموذج إنشاء ترخيص -->
        <div class="license-form" id="licenseForm" style="display: none;">
            <h3><i class="fas fa-key"></i> إنشاء ترخيص جديد</h3>
            <form id="createLicenseForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerName">اسم العميل:</label>
                        <input type="text" id="customerName" name="customerName" required>
                    </div>
                    <div class="form-group">
                        <label for="customerPhone">رقم الهاتف:</label>
                        <input type="tel" id="customerPhone" name="customerPhone" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerState">الولاية:</label>
                        <select id="customerState" name="customerState" required>
                            <option value="">اختر الولاية</option>
                            <option value="الجزائر">الجزائر</option>
                            <option value="وهران">وهران</option>
                            <option value="قسنطينة">قسنطينة</option>
                            <option value="عنابة">عنابة</option>
                            <option value="سطيف">سطيف</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="licenseType">نوع الترخيص:</label>
                        <select id="licenseType" name="licenseType" required>
                            <option value="">اختر نوع الترخيص</option>
                            <option value="trial">تجريبي (30 يوم)</option>
                            <option value="lifetime">مدى الحياة</option>
                        </select>
                    </div>
                </div>
                <button type="submit" class="btn">
                    <i class="fas fa-plus"></i>
                    إنشاء الترخيص
                </button>
                <button type="button" class="btn" onclick="hideLicenseForm()" style="background: #6c757d; margin-top: 10px;">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
            </form>
            <div class="success-message" id="successMessage"></div>
            <div class="error-message" id="errorMessage"></div>
        </div>

        <!-- قائمة التراخيص -->
        <div class="license-list">
            <h3><i class="fas fa-list"></i> التراخيص المُنشأة</h3>
            <div id="licensesList">
                <p style="text-align: center; color: #666; padding: 20px;">لا توجد تراخيص حالياً</p>
            </div>
        </div>
    </div>

    <script>
        console.log('تم تحميل لوحة تحكم المدير');

        // بيانات التراخيص
        let licenses = JSON.parse(localStorage.getItem('licenses') || '[]');

        // تحديث الإحصائيات
        function refreshStats() {
            const total = licenses.length;
            const active = licenses.filter(l => l.status === 'active').length;
            const trial = licenses.filter(l => l.type === 'trial').length;
            const expired = licenses.filter(l => l.status === 'expired').length;

            document.getElementById('totalLicenses').textContent = total;
            document.getElementById('activeLicenses').textContent = active;
            document.getElementById('trialLicenses').textContent = trial;
            document.getElementById('expiredLicenses').textContent = expired;
        }

        // إظهار نموذج إنشاء ترخيص
        function showCreateLicenseForm() {
            document.getElementById('licenseForm').style.display = 'block';
            document.getElementById('customerName').focus();
        }

        // إخفاء نموذج إنشاء ترخيص
        function hideLicenseForm() {
            document.getElementById('licenseForm').style.display = 'none';
            document.getElementById('createLicenseForm').reset();
            hideMessages();
        }

        // إخفاء الرسائل
        function hideMessages() {
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
        }

        // إظهار رسالة نجاح
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => successDiv.style.display = 'none', 5000);
        }

        // إظهار رسالة خطأ
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => errorDiv.style.display = 'none', 5000);
        }

        // توليد مفتاح ترخيص
        function generateLicenseKey() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 16; i++) {
                if (i > 0 && i % 4 === 0) result += '-';
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // معالج إنشاء ترخيص
        document.getElementById('createLicenseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const licenseData = {
                id: Date.now().toString(),
                key: generateLicenseKey(),
                customerName: formData.get('customerName'),
                customerPhone: formData.get('customerPhone'),
                customerState: formData.get('customerState'),
                type: formData.get('licenseType'),
                status: 'active',
                createdAt: new Date().toISOString(),
                expiryDate: formData.get('licenseType') === 'trial' 
                    ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                    : null
            };

            licenses.push(licenseData);
            localStorage.setItem('licenses', JSON.stringify(licenses));
            
            showSuccess(`تم إنشاء الترخيص بنجاح! مفتاح الترخيص: ${licenseData.key}`);
            refreshStats();
            displayLicenses();
            
            // إعادة تعيين النموذج
            setTimeout(() => {
                hideLicenseForm();
            }, 3000);
        });

        // عرض التراخيص
        function displayLicenses() {
            const container = document.getElementById('licensesList');
            
            if (licenses.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">لا توجد تراخيص حالياً</p>';
                return;
            }

            container.innerHTML = licenses.map(license => `
                <div class="license-item">
                    <div class="license-info">
                        <h4>${license.customerName}</h4>
                        <p>المفتاح: ${license.key}</p>
                        <p>الهاتف: ${license.customerPhone} | الولاية: ${license.customerState}</p>
                        <p>تاريخ الإنشاء: ${new Date(license.createdAt).toLocaleDateString('ar-SA')}</p>
                        ${license.expiryDate ? `<p>تاريخ الانتهاء: ${new Date(license.expiryDate).toLocaleDateString('ar-SA')}</p>` : ''}
                    </div>
                    <div class="license-status status-${license.status}">
                        ${license.status === 'active' ? 'نشط' : license.status === 'trial' ? 'تجريبي' : 'منتهي'}
                    </div>
                </div>
            `).join('');
        }

        // تصدير التراخيص
        function exportLicenses() {
            const dataStr = JSON.stringify(licenses, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `licenses_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }

        // الإعدادات
        function showSettings() {
            alert('صفحة الإعدادات قيد التطوير');
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('userSession');
                window.location.href = '../../auth/simple-login.html';
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
            displayLicenses();
        });
    </script>
</body>
</html>
