<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 مجموعة الاختبارات الشاملة - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 2rem;
        }

        .test-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary { background: linear-gradient(135deg, #3498db, #2980b9); color: white; }
        .btn-success { background: linear-gradient(135deg, #27ae60, #229954); color: white; }
        .btn-warning { background: linear-gradient(135deg, #f39c12, #e67e22); color: white; }
        .btn-danger { background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; }
        .btn-info { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .test-progress {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #27ae60);
            width: 0%;
            transition: width 0.3s ease;
        }

        .test-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
        }

        .test-results {
            display: none;
            margin-top: 2rem;
        }

        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid;
        }

        .summary-card.passed { border-left-color: #27ae60; }
        .summary-card.failed { border-left-color: #e74c3c; }
        .summary-card.skipped { border-left-color: #f39c12; }
        .summary-card.total { border-left-color: #3498db; }

        .summary-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .summary-label {
            color: #666;
            font-weight: 600;
        }

        .test-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1rem;
        }

        .category-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .category-header {
            padding: 1rem;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .category-header.auth { background: #3498db; }
        .category-header.license { background: #27ae60; }
        .category-header.security { background: #e74c3c; }
        .category-header.ui { background: #f39c12; }
        .category-header.database { background: #9b59b6; }

        .category-body {
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .test-item:hover {
            background: #e9ecef;
        }

        .test-name {
            font-weight: 500;
            flex: 1;
        }

        .test-result {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            min-width: 60px;
            text-align: center;
        }

        .test-result.passed {
            background: #d4edda;
            color: #155724;
        }

        .test-result.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .test-result.skipped {
            background: #fff3cd;
            color: #856404;
        }

        .test-result.running {
            background: #cce7ff;
            color: #004085;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .test-details {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: #fff;
            border-radius: 5px;
            font-size: 0.8rem;
            color: #666;
            display: none;
        }

        .test-item.expanded .test-details {
            display: block;
        }

        .console-output {
            margin-top: 2rem;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }

        .console-line {
            margin-bottom: 0.5rem;
            padding: 0.2rem 0;
        }

        .console-line.error { color: #e74c3c; }
        .console-line.warning { color: #f39c12; }
        .console-line.success { color: #27ae60; }
        .console-line.info { color: #3498db; }

        .quick-actions {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .quick-actions h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .action-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #3498db;
        }

        @media (max-width: 768px) {
            .test-controls {
                flex-direction: column;
            }
            
            .test-categories {
                grid-template-columns: 1fr;
            }
            
            .results-summary {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-flask"></i> مجموعة الاختبارات الشاملة</h1>
            <p>اختبار جميع مكونات نظام إدارة التراخيص والتأكد من عملها بشكل صحيح</p>
        </div>

        <div class="main-content">
            <div class="test-controls">
                <button class="btn btn-primary" onclick="runAllTests()">
                    <i class="fas fa-play"></i>
                    تشغيل جميع الاختبارات
                </button>
                <button class="btn btn-success" onclick="runCriticalTests()">
                    <i class="fas fa-exclamation-triangle"></i>
                    الاختبارات الحرجة فقط
                </button>
                <button class="btn btn-info" onclick="runAuthTests()">
                    <i class="fas fa-lock"></i>
                    اختبارات المصادقة
                </button>
                <button class="btn btn-warning" onclick="runLicenseTests()">
                    <i class="fas fa-key"></i>
                    اختبارات التراخيص
                </button>
                <button class="btn btn-danger" onclick="clearResults()">
                    <i class="fas fa-trash"></i>
                    مسح النتائج
                </button>
            </div>

            <div id="testProgress" class="test-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="test-status">
                    <span id="currentTest">جاري التحضير...</span>
                    <span id="testCounter">0 / 0</span>
                </div>
            </div>

            <div id="testResults" class="test-results">
                <div class="results-summary">
                    <div class="summary-card total">
                        <div class="summary-number" id="totalTests">0</div>
                        <div class="summary-label">إجمالي الاختبارات</div>
                    </div>
                    <div class="summary-card passed">
                        <div class="summary-number" id="passedTests">0</div>
                        <div class="summary-label">نجح</div>
                    </div>
                    <div class="summary-card failed">
                        <div class="summary-number" id="failedTests">0</div>
                        <div class="summary-label">فشل</div>
                    </div>
                    <div class="summary-card skipped">
                        <div class="summary-number" id="skippedTests">0</div>
                        <div class="summary-label">تم تخطيه</div>
                    </div>
                </div>

                <div class="test-categories" id="testCategories">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <div id="consoleOutput" class="console-output">
                <div class="console-line info">🧪 مجموعة الاختبارات جاهزة للتشغيل</div>
            </div>

            <div class="quick-actions">
                <h3><i class="fas fa-tools"></i> إجراءات سريعة</h3>
                <div class="action-grid">
                    <div class="action-card" onclick="openDiagnosticTool()">
                        <div class="action-icon"><i class="fas fa-stethoscope"></i></div>
                        <h4>أداة التشخيص</h4>
                        <p>فتح أداة التشخيص الشاملة</p>
                    </div>
                    <div class="action-card" onclick="openLoginPage()">
                        <div class="action-icon"><i class="fas fa-sign-in-alt"></i></div>
                        <h4>تسجيل الدخول</h4>
                        <p>فتح صفحة تسجيل الدخول</p>
                    </div>
                    <div class="action-card" onclick="exportTestResults()">
                        <div class="action-icon"><i class="fas fa-download"></i></div>
                        <h4>تصدير النتائج</h4>
                        <p>حفظ نتائج الاختبارات</p>
                    </div>
                    <div class="action-card" onclick="showConsole()">
                        <div class="action-icon"><i class="fas fa-terminal"></i></div>
                        <h4>عرض Console</h4>
                        <p>إظهار مخرجات Console</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الأنظمة المطلوبة -->
    <script src="src/utils/system-diagnostics.js"></script>
    <script src="src/utils/error-logger.js"></script>
    <script src="test-suite.js"></script>
</body>
</html>
