// متغيرات عامة
let currentSection = 'requests';
let activationRequests = [];
let licenses = [];
let selectedRequestId = null;
let charts = {};

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPanel();
    loadData();
    setupEventListeners();
    loadTheme();
});

// تهيئة لوحة تحكم المدير
async function initializeAdminPanel() {
    try {
        // التحقق من صلاحيات المدير
        if (!checkAdminPermissions()) {
            redirectToLogin();
            return;
        }

        // تهيئة نظام إدارة التراخيص
        if (window.licenseManager) {
            await window.licenseManager.initialize();
        }

        console.log('تم تهيئة لوحة تحكم المدير بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة لوحة تحكم المدير:', error);
        showNotification('حدث خطأ في تهيئة النظام', 'error');
    }
}

// التحقق من صلاحيات المدير
function checkAdminPermissions() {
    try {
        const session = localStorage.getItem('userSession');
        if (!session) return false;

        const sessionData = JSON.parse(session);
        return sessionData.username === 'admin' && sessionData.isValid;
    } catch {
        return false;
    }
}

// تحميل البيانات
async function loadData() {
    try {
        await loadActivationRequests();
        await loadLicenses();
        updateStatistics();
        updateCharts();
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showNotification('فشل في تحميل البيانات', 'error');
    }
}

// تحميل طلبات التفعيل
async function loadActivationRequests() {
    try {
        // تحميل من localStorage كبديل
        const stored = localStorage.getItem('activationRequests');
        activationRequests = stored ? JSON.parse(stored) : [];

        // إضافة بيانات تجريبية إذا لم توجد
        if (activationRequests.length === 0) {
            activationRequests = generateSampleRequests();
            localStorage.setItem('activationRequests', JSON.stringify(activationRequests));
        }

        displayActivationRequests();
        updatePendingCount();
    } catch (error) {
        console.error('خطأ في تحميل طلبات التفعيل:', error);
        activationRequests = [];
    }
}

// تحميل التراخيص
async function loadLicenses() {
    try {
        const stored = localStorage.getItem('licenses');
        licenses = stored ? JSON.parse(stored) : [];

        // إضافة بيانات تجريبية إذا لم توجد
        if (licenses.length === 0) {
            licenses = generateSampleLicenses();
            localStorage.setItem('licenses', JSON.stringify(licenses));
        }

        displayLicenses();
    } catch (error) {
        console.error('خطأ في تحميل التراخيص:', error);
        licenses = [];
    }
}

// عرض طلبات التفعيل
function displayActivationRequests() {
    const container = document.getElementById('requestsGrid');
    container.innerHTML = '';

    if (activationRequests.length === 0) {
        container.innerHTML = '<div class="no-data">لا توجد طلبات تفعيل</div>';
        return;
    }

    activationRequests.forEach(request => {
        const requestCard = createRequestCard(request);
        container.appendChild(requestCard);
    });
}

// إنشاء بطاقة طلب
function createRequestCard(request) {
    const card = document.createElement('div');
    card.className = 'request-card';
    card.onclick = () => showRequestDetails(request.id);

    const statusClass = request.status || 'pending';
    const statusLabel = getStatusLabel(request.status);
    const stateName = ALGERIAN_STATES[request.state] || 'غير محدد';
    const licenseTypeLabel = LICENSE_TYPE_LABELS[request.licenseType] || request.licenseType;

    card.innerHTML = `
        <div class="request-header">
            <span class="request-id">${request.id}</span>
            <span class="request-status ${statusClass}">${statusLabel}</span>
        </div>
        <div class="request-info">
            <h4>${request.customerName}</h4>
            <div class="request-details">
                <div><i class="fas fa-phone"></i> ${request.phoneNumber}</div>
                <div><i class="fas fa-map-marker-alt"></i> ${stateName}</div>
                <div><i class="fas fa-certificate"></i> ${licenseTypeLabel}</div>
                <div><i class="fas fa-clock"></i> ${formatDate(request.createdAt)}</div>
            </div>
        </div>
        ${request.status === 'pending' ? `
            <div class="request-actions">
                <button class="btn btn-success btn-sm" onclick="event.stopPropagation(); approveRequestQuick('${request.id}')">
                    <i class="fas fa-check"></i> موافقة
                </button>
                <button class="btn btn-danger btn-sm" onclick="event.stopPropagation(); rejectRequestQuick('${request.id}')">
                    <i class="fas fa-times"></i> رفض
                </button>
            </div>
        ` : ''}
    `;

    return card;
}

// عرض التراخيص
function displayLicenses() {
    const tbody = document.getElementById('licensesTableBody');
    tbody.innerHTML = '';

    if (licenses.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">لا توجد تراخيص</td></tr>';
        return;
    }

    licenses.forEach(license => {
        const row = createLicenseRow(license);
        tbody.appendChild(row);
    });
}

// إنشاء صف ترخيص
function createLicenseRow(license) {
    const row = document.createElement('tr');

    const statusClass = license.status || 'active';
    const statusLabel = LICENSE_STATUS_LABELS[license.status] || license.status;
    const licenseTypeLabel = LICENSE_TYPE_LABELS[license.licenseType] || license.licenseType;

    row.innerHTML = `
        <td><code>${license.licenseKey}</code></td>
        <td>${license.customerName}</td>
        <td>${licenseTypeLabel}</td>
        <td>${formatDate(license.issuedAt)}</td>
        <td>${license.expiryDate ? formatDate(license.expiryDate) : 'مدى الحياة'}</td>
        <td><span class="request-status ${statusClass}">${statusLabel}</span></td>
        <td>
            <button class="btn btn-primary btn-sm" onclick="viewLicense('${license.id}')">
                <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-warning btn-sm" onclick="editLicense('${license.id}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-danger btn-sm" onclick="revokeLicense('${license.id}')">
                <i class="fas fa-ban"></i>
            </button>
        </td>
    `;

    return row;
}

// تحديث الإحصائيات
function updateStatistics() {
    const pendingRequests = activationRequests.filter(r => r.status === 'pending').length;
    const activeLicensesCount = licenses.filter(l => l.status === 'active').length;
    const expiredLicensesCount = licenses.filter(l => l.status === 'expired').length;

    // تحديث إحصائيات الهيدر
    document.getElementById('totalRequests').textContent = activationRequests.length;
    document.getElementById('activeLicenses').textContent = activeLicensesCount;

    // تحديث إحصائيات قسم التحليلات
    document.getElementById('totalRequestsCount').textContent = activationRequests.length;
    document.getElementById('activeLicensesCount').textContent = activeLicensesCount;
    document.getElementById('expiredLicensesCount').textContent = expiredLicensesCount;
}

// تحديث عدد الطلبات المعلقة
function updatePendingCount() {
    const pendingCount = activationRequests.filter(r => r.status === 'pending').length;
    document.getElementById('pendingCount').textContent = pendingCount;
}

// تحديث الرسوم البيانية
function updateCharts() {
    updateLicenseTypesChart();
}

// رسم بياني لأنواع التراخيص
function updateLicenseTypesChart() {
    const ctx = document.getElementById('licenseTypesChart');
    if (!ctx) return;

    // حساب توزيع أنواع التراخيص
    const typeCounts = {};
    licenses.forEach(license => {
        const type = license.licenseType;
        typeCounts[type] = (typeCounts[type] || 0) + 1;
    });

    const labels = Object.keys(typeCounts).map(type => LICENSE_TYPE_LABELS[type] || type);
    const data = Object.values(typeCounts);
    const colors = ['#3498db', '#27ae60', '#f39c12', '#e74c3c'];

    if (charts.licenseTypes) {
        charts.licenseTypes.destroy();
    }

    charts.licenseTypes = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length)
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث في الطلبات
    document.getElementById('searchRequests').addEventListener('input', searchRequests);

    // فلترة الطلبات
    document.getElementById('statusFilter').addEventListener('change', filterRequests);

    // إغلاق النوافذ المنبثقة بالضغط خارجها
    window.addEventListener('click', handleModalClose);
}

// البحث في الطلبات
function searchRequests() {
    const query = document.getElementById('searchRequests').value.toLowerCase();
    const cards = document.querySelectorAll('.request-card');

    cards.forEach(card => {
        const text = card.textContent.toLowerCase();
        card.style.display = text.includes(query) ? 'block' : 'none';
    });
}

// فلترة الطلبات
function filterRequests() {
    const status = document.getElementById('statusFilter').value;
    const cards = document.querySelectorAll('.request-card');

    cards.forEach(card => {
        if (!status) {
            card.style.display = 'block';
        } else {
            const statusElement = card.querySelector('.request-status');
            const cardStatus = statusElement.classList.contains(status);
            card.style.display = cardStatus ? 'block' : 'none';
        }
    });
}

// عرض تفاصيل الطلب
function showRequestDetails(requestId) {
    const request = activationRequests.find(r => r.id === requestId);
    if (!request) return;

    selectedRequestId = requestId;
    const modal = document.getElementById('requestDetailsModal');
    const body = document.getElementById('requestDetailsBody');

    const stateName = ALGERIAN_STATES[request.state] || 'غير محدد';
    const licenseTypeLabel = LICENSE_TYPE_LABELS[request.licenseType] || request.licenseType;
    const statusLabel = getStatusLabel(request.status);

    body.innerHTML = `
        <div class="request-details-content">
            <div class="detail-section">
                <h4>معلومات العميل</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="label">الاسم:</span>
                        <span class="value">${request.customerName}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">رقم الهاتف:</span>
                        <span class="value">${request.phoneNumber}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">الولاية:</span>
                        <span class="value">${stateName}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4>تفاصيل الطلب</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="label">رقم الطلب:</span>
                        <span class="value">${request.id}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">نوع الترخيص:</span>
                        <span class="value">${licenseTypeLabel}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">الحالة:</span>
                        <span class="value">${statusLabel}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">تاريخ الطلب:</span>
                        <span class="value">${formatDate(request.createdAt)}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4>معلومات الجهاز</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="label">معرف الجهاز:</span>
                        <span class="value"><code>${request.machineId}</code></span>
                    </div>
                </div>
            </div>

            ${request.notes ? `
                <div class="detail-section">
                    <h4>ملاحظات</h4>
                    <p>${request.notes}</p>
                </div>
            ` : ''}
        </div>
    `;

    modal.style.display = 'flex';
}

// إغلاق تفاصيل الطلب
function closeRequestDetails() {
    document.getElementById('requestDetailsModal').style.display = 'none';
    selectedRequestId = null;
}

// الموافقة على الطلب
async function approveRequest() {
    if (!selectedRequestId) return;

    try {
        const request = activationRequests.find(r => r.id === selectedRequestId);
        if (!request) return;

        // إنشاء ترخيص جديد
        const license = generateLicenseFromRequest(request);

        // حفظ الترخيص
        licenses.push(license);
        localStorage.setItem('licenses', JSON.stringify(licenses));

        // تحديث حالة الطلب
        request.status = 'approved';
        request.licenseId = license.id;
        request.updatedAt = new Date().toISOString();

        localStorage.setItem('activationRequests', JSON.stringify(activationRequests));

        // تحديث الواجهة
        await loadData();
        closeRequestDetails();

        showNotification(`تم إنشاء الترخيص بنجاح: ${license.licenseKey}`, 'success');

    } catch (error) {
        console.error('خطأ في الموافقة على الطلب:', error);
        showNotification('فشل في إنشاء الترخيص', 'error');
    }
}

// رفض الطلب
async function rejectRequest() {
    if (!selectedRequestId) return;

    try {
        const request = activationRequests.find(r => r.id === selectedRequestId);
        if (!request) return;

        // تحديث حالة الطلب
        request.status = 'rejected';
        request.updatedAt = new Date().toISOString();

        localStorage.setItem('activationRequests', JSON.stringify(activationRequests));

        // تحديث الواجهة
        await loadData();
        closeRequestDetails();

        showNotification('تم رفض الطلب', 'info');

    } catch (error) {
        console.error('خطأ في رفض الطلب:', error);
        showNotification('فشل في رفض الطلب', 'error');
    }
}

// الموافقة السريعة على الطلب
async function approveRequestQuick(requestId) {
    selectedRequestId = requestId;
    await approveRequest();
}

// الرفض السريع للطلب
async function rejectRequestQuick(requestId) {
    if (confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
        selectedRequestId = requestId;
        await rejectRequest();
    }
}

// إنشاء ترخيص من الطلب
function generateLicenseFromRequest(request) {
    if (window.licenseManager) {
        return window.licenseManager.generateLicense(request, request.licenseType);
    } else {
        // إنشاء ترخيص بسيط
        const licenseKey = generateLicenseKey();
        const now = new Date();
        let expiryDate = null;

        switch (request.licenseType) {
            case 'trial':
                expiryDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
                break;
            case 'monthly':
                expiryDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
                break;
            case 'yearly':
                expiryDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
                break;
            case 'lifetime':
                expiryDate = new Date('2099-12-31');
                break;
        }

        return {
            id: 'LIC-' + Date.now().toString(36),
            licenseKey: licenseKey,
            customerName: request.customerName,
            phoneNumber: request.phoneNumber,
            state: request.state,
            licenseType: request.licenseType,
            machineId: request.machineId,
            status: 'active',
            issuedAt: now.toISOString(),
            expiryDate: expiryDate ? expiryDate.toISOString() : null,
            createdAt: now.toISOString(),
            updatedAt: now.toISOString()
        };
    }
}

// إنشاء مفتاح ترخيص
function generateLicenseKey() {
    const segments = [];
    for (let i = 0; i < 4; i++) {
        segments.push(generateRandomString(4).toUpperCase());
    }
    return segments.join('-');
}

// إنشاء نص عشوائي
function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// عرض نافذة إنشاء ترخيص
function showCreateLicenseModal() {
    document.getElementById('createLicenseModal').style.display = 'flex';
}

// إغلاق نافذة إنشاء ترخيص
function closeCreateLicense() {
    document.getElementById('createLicenseModal').style.display = 'none';
    document.getElementById('createLicenseForm').reset();
}

// إنشاء ترخيص جديد
async function createLicense() {
    try {
        const form = document.getElementById('createLicenseForm');
        const formData = new FormData(form);

        const licenseData = {
            customerName: document.getElementById('newLicenseCustomerName').value,
            phoneNumber: document.getElementById('newLicensePhone').value,
            licenseType: document.getElementById('newLicenseType').value,
            machineId: document.getElementById('newLicenseMachineId').value
        };

        // التحقق من البيانات
        if (!licenseData.customerName || !licenseData.phoneNumber || !licenseData.machineId) {
            showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            return;
        }

        // إنشاء الترخيص
        const license = generateLicenseFromRequest(licenseData);

        // حفظ الترخيص
        licenses.push(license);
        localStorage.setItem('licenses', JSON.stringify(licenses));

        // تحديث الواجهة
        await loadData();
        closeCreateLicense();

        showNotification(`تم إنشاء الترخيص بنجاح: ${license.licenseKey}`, 'success');

    } catch (error) {
        console.error('خطأ في إنشاء الترخيص:', error);
        showNotification('فشل في إنشاء الترخيص', 'error');
    }
}

// عرض ترخيص
function viewLicense(licenseId) {
    const license = licenses.find(l => l.id === licenseId);
    if (!license) return;

    const stateName = ALGERIAN_STATES[license.state] || 'غير محدد';
    const licenseTypeLabel = LICENSE_TYPE_LABELS[license.licenseType] || license.licenseType;

    alert(`تفاصيل الترخيص:
الرقم: ${license.licenseKey}
العميل: ${license.customerName}
النوع: ${licenseTypeLabel}
الولاية: ${stateName}
تاريخ الإصدار: ${formatDate(license.issuedAt)}
تاريخ انتهاء الصلاحية: ${license.expiryDate ? formatDate(license.expiryDate) : 'مدى الحياة'}`);
}

// تعديل ترخيص
function editLicense(licenseId) {
    showNotification('ميزة التعديل قيد التطوير', 'info');
}

// إلغاء ترخيص
async function revokeLicense(licenseId) {
    if (!confirm('هل أنت متأكد من إلغاء هذا الترخيص؟')) return;

    try {
        const license = licenses.find(l => l.id === licenseId);
        if (!license) return;

        license.status = 'revoked';
        license.updatedAt = new Date().toISOString();

        localStorage.setItem('licenses', JSON.stringify(licenses));

        await loadData();
        showNotification('تم إلغاء الترخيص بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في إلغاء الترخيص:', error);
        showNotification('فشل في إلغاء الترخيص', 'error');
    }
}

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('collapsed');
}

// عرض قسم معين
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });

    // إزالة الحالة النشطة من جميع عناصر التنقل
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });

    // إظهار القسم المطلوب
    document.getElementById(sectionName + '-section').classList.add('active');

    // تفعيل عنصر التنقل المناسب
    document.querySelector(`[onclick="showSection('${sectionName}')"]`).parentElement.classList.add('active');

    // تحديث عنوان الصفحة
    updatePageTitle(sectionName);

    currentSection = sectionName;
}

// تحديث عنوان الصفحة
function updatePageTitle(sectionName) {
    const titles = {
        requests: 'طلبات التفعيل',
        licenses: 'التراخيص النشطة',
        analytics: 'الإحصائيات والتقارير',
        settings: 'إعدادات النظام'
    };

    document.getElementById('pageTitle').textContent = titles[sectionName] || 'لوحة تحكم المدير';
}

// تحديث البيانات
async function refreshData() {
    showNotification('جاري تحديث البيانات...', 'info');
    await loadData();
    showNotification('تم تحديث البيانات بنجاح', 'success');
}

// تبديل المظهر
function toggleTheme() {
    const body = document.body;
    const themeIcon = document.getElementById('themeIcon');

    body.classList.toggle('dark-mode');

    if (body.classList.contains('dark-mode')) {
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
        localStorage.setItem('theme', 'dark');
    } else {
        themeIcon.classList.remove('fa-sun');
        themeIcon.classList.add('fa-moon');
        localStorage.setItem('theme', 'light');
    }
}

// تحميل المظهر المحفوظ
function loadTheme() {
    const savedTheme = localStorage.getItem('theme');
    const themeIcon = document.getElementById('themeIcon');

    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        themeIcon.classList.remove('fa-moon');
        themeIcon.classList.add('fa-sun');
    }
}

// حفظ الإعدادات
function saveSettings() {
    const settings = {
        trialDuration: document.getElementById('trialDuration').value,
        autoUpdate: document.getElementById('autoUpdate').checked,
        expiryNotifications: document.getElementById('expiryNotifications').checked
    };

    localStorage.setItem('adminSettings', JSON.stringify(settings));
    showNotification('تم حفظ الإعدادات بنجاح', 'success');
}

// تصدير البيانات
function exportData() {
    const data = {
        activationRequests: activationRequests,
        licenses: licenses,
        exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `license-data-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification('تم تصدير البيانات بنجاح', 'success');
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('userSession');
        window.location.href = '../../auth/new-login.html';
    }
}

// إعادة التوجيه لتسجيل الدخول
function redirectToLogin() {
    window.location.href = '../../auth/new-login.html';
}

// إظهار إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
    `;

    // تنسيق الإشعار
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: var(--card-bg);
        color: var(--text-color);
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 3000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border-left: 4px solid ${getNotificationColor(type)};
        animation: slideIn 0.3s ease;
    `;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// الحصول على لون الإشعار
function getNotificationColor(type) {
    const colors = {
        success: '#27ae60',
        error: '#e74c3c',
        warning: '#f39c12',
        info: '#3498db'
    };
    return colors[type] || '#3498db';
}

// إغلاق النوافذ المنبثقة بالضغط خارجها
function handleModalClose(e) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// دوال مساعدة
function getStatusLabel(status) {
    const labels = {
        pending: 'في الانتظار',
        approved: 'موافق عليه',
        rejected: 'مرفوض',
        processing: 'قيد المعالجة'
    };
    return labels[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// إنشاء بيانات تجريبية للطلبات
function generateSampleRequests() {
    const sampleRequests = [];
    const names = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'يوسف إبراهيم'];
    const states = ['16', '31', '06', '25', '19'];
    const licenseTypes = ['trial', 'lifetime', 'monthly'];

    for (let i = 0; i < 8; i++) {
        const request = {
            id: 'REQ-' + Date.now().toString(36) + '-' + i,
            customerName: names[i % names.length],
            phoneNumber: '055512345' + i,
            state: states[i % states.length],
            licenseType: licenseTypes[i % licenseTypes.length],
            machineId: 'MACHINE-' + Math.random().toString(36).substr(2, 8),
            status: i < 3 ? 'pending' : (i < 6 ? 'approved' : 'rejected'),
            notes: i % 3 === 0 ? 'طلب عاجل للتفعيل' : '',
            createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
        };
        sampleRequests.push(request);
    }

    return sampleRequests;
}

// إنشاء بيانات تجريبية للتراخيص
function generateSampleLicenses() {
    const sampleLicenses = [];
    const names = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد'];
    const licenseTypes = ['trial', 'lifetime', 'monthly', 'yearly'];

    for (let i = 0; i < 5; i++) {
        const now = new Date();
        const licenseType = licenseTypes[i % licenseTypes.length];
        let expiryDate = null;

        switch (licenseType) {
            case 'trial':
                expiryDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
                break;
            case 'monthly':
                expiryDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
                break;
            case 'yearly':
                expiryDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
                break;
            case 'lifetime':
                expiryDate = new Date('2099-12-31');
                break;
        }

        const license = {
            id: 'LIC-' + Date.now().toString(36) + '-' + i,
            licenseKey: generateLicenseKey(),
            customerName: names[i % names.length],
            phoneNumber: '055512345' + i,
            state: '16',
            licenseType: licenseType,
            machineId: 'MACHINE-' + Math.random().toString(36).substr(2, 8),
            status: i === 4 ? 'expired' : 'active',
            issuedAt: new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
            expiryDate: expiryDate ? expiryDate.toISOString() : null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        sampleLicenses.push(license);
    }

    return sampleLicenses;
}

// إضافة أنماط CSS للإشعارات
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(-100%);
            opacity: 0;
        }
    }

    .detail-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .detail-section:last-child {
        border-bottom: none;
    }

    .detail-section h4 {
        color: var(--text-color);
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .detail-grid {
        display: grid;
        gap: 0.8rem;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
    }

    .detail-item .label {
        font-weight: 500;
        color: var(--text-muted);
    }

    .detail-item .value {
        color: var(--text-color);
        font-weight: 600;
    }

    .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--text-muted);
        font-size: 1.1rem;
    }
`;
document.head.appendChild(notificationStyles);