# نظام إدارة مؤسسة وقود المستقبل

## 📋 نظرة عامة
نظام إدارة شامل ومتطور لمؤسسة وقود المستقبل مع نظام ترخيص متقدم ولوحة تحكم إدارية.

## ✨ الميزات الرئيسية
- 🔐 نظام تسجيل دخول آمن مع دعم المدير والترخيص
- 👨‍💼 لوحة تحكم إدارية متقدمة
- 🔑 نظام ترخيص ذكي مع إدارة العملاء
- 📊 تقارير مفصلة وإحصائيات
- 🌙 دعم الوضع المظلم
- 🔄 نسخ احتياطية تلقائية
- 📱 واجهة مستخدم متجاوبة

## 📁 هيكل المشروع المنظم
```
resources/app/
├── src/                    # الكود المصدري
│   ├── auth/              # نظام تسجيل الدخول
│   │   ├── new-login.html
│   │   ├── new-login.css
│   │   ├── new-login.js
│   │   ├── activation-request.html
│   │   └── license.js
│   ├── components/        # مكونات التطبيق
│   │   ├── admin/         # لوحة تحكم المدير
│   │   │   ├── license-management.html
│   │   │   ├── license-management.css
│   │   │   ├── license-management.js
│   │   │   └── remote-admin-panel.html
│   │   └── dashboard/     # لوحة التحكم العادية
│   │       ├── dashboard.html
│   │       ├── dashboard.css
│   │       └── dashboard.js
│   ├── assets/           # الموارد
│   │   └── icons/        # الأيقونات
│   ├── utils/            # أدوات مساعدة
│   └── data/             # بيانات التطبيق
├── scripts/              # سكريبتات JavaScript
├── styles/               # ملفات CSS العامة
├── config/               # ملفات التكوين
├── docs/                 # الوثائق
├── installer/            # ملفات التثبيت
├── main.js              # الملف الرئيسي
├── preload.js           # ملف preload
└── package.json         # معلومات المشروع
```

## 🚀 كيفية الاستخدام

### تسجيل الدخول
1. **المدير**: استخدم اسم المستخدم وكلمة المرور
   - المدير: `admin` / `admin123`
   - مستخدم: `user` / `user123`
   - مدير: `manager` / `manager123`

2. **بالترخيص**: أدخل مفتاح الترخيص واسم العميل

### لوحة التحكم
- عرض الإحصائيات والتقارير
- إدارة العملاء والطلبات
- مراقبة النظام

### لوحة تحكم المدير
- إدارة طلبات التفعيل
- إنشاء وإدارة التراخيص
- مراقبة النشاط

## 🔧 التكوين
يمكن تعديل إعدادات التطبيق من ملف `config/app-config.js`:
- مدة انتهاء الجلسة
- إعدادات الأمان
- معلومات الدعم

## 📞 الدعم الفني
- **الهاتف**: 0696924176
- **واتساب**: 0696924176
- **البريد الإلكتروني**: <EMAIL>

## 📝 ملاحظات التطوير
- تم تنظيم المشروع وحذف الملفات المكررة
- تم إصلاح المسارات والمراجع
- تم تحسين هيكل المجلدات
- تم توحيد ملفات CSS و JavaScript

## 🔄 الإصدار
**الإصدار الحالي**: 3.0.0
**تاريخ التحديث**: 2025-07-01
