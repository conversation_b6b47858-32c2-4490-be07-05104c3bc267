<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link rel="stylesheet" href="new-login.css">
    <style>
        /* تحسينات إضافية للنظام المحسن */
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            animation: slideIn 0.3s ease;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .error-message {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 0.5rem;
            display: none;
        }
        
        .loading-state {
            opacity: 0.7;
            pointer-events: none;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* تحسين أزرار الدعم */
        .support-section {
            margin-top: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(155, 89, 182, 0.1));
            border-radius: 12px;
            text-align: center;
        }
        
        .support-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1rem;
            flex-wrap: wrap;
        }
        
        .support-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.2rem;
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .support-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
        }
        
        .support-btn.whatsapp {
            background: linear-gradient(135deg, #25d366, #20ba5a);
        }
        
        .support-btn.whatsapp:hover {
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
        }
        
        /* تحسين حالة التحميل */
        .btn-loading {
            position: relative;
            overflow: hidden;
        }
        
        .btn-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: loading-shine 1.5s infinite;
        }
        
        @keyframes loading-shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-gas-pump"></i>
                </div>
                <h1>مؤسسة وقود المستقبل</h1>
                <p>Future Fuel Corporation</p>
            </div>

            <!-- Navigation Tabs -->
            <div class="login-tabs">
                <button class="tab-btn active" data-tab="admin">
                    <i class="fas fa-user-shield"></i>
                    المدير
                </button>
                <button class="tab-btn" data-tab="license">
                    <i class="fas fa-key"></i>
                    العملاء
                </button>
            </div>

            <!-- Admin Login Form -->
            <div class="tab-content active" id="admin-tab">
                <form id="adminLoginForm" class="login-form">
                    <h2><i class="fas fa-sign-in-alt"></i> تسجيل دخول المدير</h2>
                    
                    <div class="form-group">
                        <label for="username">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" id="username" name="username" required 
                               placeholder="أدخل اسم المستخدم" autocomplete="username">
                        <div id="usernameError" class="error-message"></div>
                    </div>

                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <input type="password" id="password" name="password" required 
                               placeholder="أدخل كلمة المرور" autocomplete="current-password">
                        <div id="passwordError" class="error-message"></div>
                    </div>

                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل دخول المدير
                    </button>
                    
                    <div id="adminSuccess"></div>
                    <div id="adminError"></div>
                </form>
            </div>

            <!-- License Login Form -->
            <div class="tab-content" id="license-tab">
                <form id="licenseLoginForm" class="login-form">
                    <h2><i class="fas fa-key"></i> تسجيل دخول العملاء</h2>
                    
                    <div class="form-group">
                        <label for="licenseKey">
                            <i class="fas fa-key"></i>
                            مفتاح الترخيص
                        </label>
                        <input type="text" id="licenseKey" name="licenseKey" required 
                               placeholder="أدخل مفتاح الترخيص" maxlength="25">
                        <div id="licenseKeyError" class="error-message"></div>
                    </div>

                    <div class="form-group">
                        <label for="customerName">
                            <i class="fas fa-user"></i>
                            اسم العميل
                        </label>
                        <input type="text" id="customerName" name="customerName" required 
                               placeholder="أدخل اسم العميل">
                        <div id="customerNameError" class="error-message"></div>
                    </div>

                    <button type="submit" class="login-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل دخول العميل
                    </button>
                    
                    <div id="licenseSuccess"></div>
                    <div id="licenseError"></div>
                </form>

                <div class="activation-link">
                    <p>ليس لديك ترخيص؟</p>
                    <a href="activation-request.html">
                        <i class="fas fa-plus-circle"></i>
                        طلب تفعيل جديد
                    </a>
                </div>
            </div>

            <!-- Support Section -->
            <div class="support-section">
                <h3><i class="fas fa-headset"></i> الدعم الفني</h3>
                <p>للمساعدة والاستفسارات</p>
                <div class="support-buttons">
                    <a href="tel:**********" class="support-btn">
                        <i class="fas fa-phone"></i>
                        **********
                    </a>
                    <a href="https://wa.me/213696924176" target="_blank" class="support-btn whatsapp">
                        <i class="fab fa-whatsapp"></i>
                        واتساب
                    </a>
                </div>
            </div>

            <!-- Footer -->
            <div class="login-footer">
                <p>&copy; 2024 مؤسسة وقود المستقبل. جميع الحقوق محفوظة.</p>
                <div class="footer-links">
                    <a href="#" onclick="showDiagnostics()">
                        <i class="fas fa-tools"></i>
                        أدوات التشخيص
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>جاري تسجيل الدخول...</h3>
            <p>يرجى الانتظار</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../utils/auth-system-fixed.js"></script>
    <script>
        // تبديل التبويبات
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tabName = this.dataset.tab;
                
                // إزالة الفئة النشطة من جميع التبويبات
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // إضافة الفئة النشطة للتبويب المحدد
                this.classList.add('active');
                document.getElementById(tabName + '-tab').classList.add('active');
                
                // مسح الرسائل
                if (window.authSystemFixed) {
                    authSystemFixed.clearMessages();
                }
            });
        });

        // تنسيق مفتاح الترخيص
        document.getElementById('licenseKey').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
            let formatted = value.match(/.{1,5}/g)?.join('-') || value;
            if (formatted.length > 29) formatted = formatted.substring(0, 29);
            e.target.value = formatted;
        });

        // فتح أدوات التشخيص
        function showDiagnostics() {
            const diagnosticUrl = '../../test-login-flow.html';
            window.open(diagnosticUrl, '_blank');
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // تركيز تلقائي على أول حقل
            const usernameInput = document.getElementById('username');
            if (usernameInput) {
                setTimeout(() => usernameInput.focus(), 500);
            }

            // إضافة تأثيرات بصرية
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });

            console.log('✅ تم تحميل صفحة تسجيل الدخول المحسنة');
        });
    </script>
</body>
</html>
