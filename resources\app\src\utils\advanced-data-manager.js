// نظام إدارة البيانات المتقدم
// Advanced Data Management System

class AdvancedDataManager {
    constructor() {
        this.storageKeys = {
            activationRequests: 'activationRequests',
            licenses: 'licenses',
            customers: 'customers',
            systemSettings: 'systemSettings',
            statistics: 'systemStatistics',
            logs: 'systemLogs'
        };
        
        this.initialized = false;
        this.eventListeners = new Map();
        
        // إعدادات افتراضية
        this.defaultSettings = {
            autoApproval: false,
            licenseValidityDays: 365,
            maxDevicesPerLicense: 1,
            supportInfo: {
                phone: '**********',
                whatsapp: '213696924176',
                email: '<EMAIL>'
            },
            companyInfo: {
                name: 'مؤسسة وقود المستقبل',
                nameEn: 'Future Fuel Corporation',
                address: 'الجزائر',
                website: 'www.futurefuel.dz'
            }
        };
    }

    // تهيئة النظام
    initialize() {
        if (this.initialized) return;
        
        console.log('🔧 تهيئة نظام إدارة البيانات المتقدم...');
        
        try {
            // تهيئة البيانات الافتراضية
            this.initializeDefaultData();
            
            // تحميل الإعدادات
            this.loadSettings();
            
            // إنشاء بيانات تجريبية إذا لم تكن موجودة
            this.createSampleDataIfNeeded();
            
            this.initialized = true;
            console.log('✅ تم تهيئة نظام إدارة البيانات بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام إدارة البيانات:', error);
        }
    }

    // تهيئة البيانات الافتراضية
    initializeDefaultData() {
        // تهيئة طلبات التفعيل
        if (!this.getData(this.storageKeys.activationRequests)) {
            this.saveData(this.storageKeys.activationRequests, []);
        }
        
        // تهيئة التراخيص
        if (!this.getData(this.storageKeys.licenses)) {
            this.saveData(this.storageKeys.licenses, []);
        }
        
        // تهيئة العملاء
        if (!this.getData(this.storageKeys.customers)) {
            this.saveData(this.storageKeys.customers, []);
        }
        
        // تهيئة الإحصائيات
        if (!this.getData(this.storageKeys.statistics)) {
            this.saveData(this.storageKeys.statistics, {
                totalRequests: 0,
                approvedRequests: 0,
                rejectedRequests: 0,
                activeLicenses: 0,
                expiredLicenses: 0,
                totalCustomers: 0,
                lastUpdated: new Date().toISOString()
            });
        }
        
        // تهيئة السجلات
        if (!this.getData(this.storageKeys.logs)) {
            this.saveData(this.storageKeys.logs, []);
        }
    }

    // تحميل الإعدادات
    loadSettings() {
        const settings = this.getData(this.storageKeys.systemSettings);
        if (!settings) {
            this.saveData(this.storageKeys.systemSettings, this.defaultSettings);
            return this.defaultSettings;
        }
        return { ...this.defaultSettings, ...settings };
    }

    // حفظ الإعدادات
    saveSettings(settings) {
        const currentSettings = this.loadSettings();
        const newSettings = { ...currentSettings, ...settings };
        this.saveData(this.storageKeys.systemSettings, newSettings);
        this.triggerEvent('settingsUpdated', newSettings);
        return newSettings;
    }

    // إنشاء بيانات تجريبية
    createSampleDataIfNeeded() {
        const requests = this.getActivationRequests();
        const licenses = this.getLicenses();
        
        if (requests.length === 0) {
            this.createSampleActivationRequests();
        }
        
        if (licenses.length === 0) {
            this.createSampleLicenses();
        }
    }

    // إنشاء طلبات تفعيل تجريبية
    createSampleActivationRequests() {
        const sampleRequests = [
            {
                id: this.generateId(),
                customerName: 'أحمد محمد علي',
                phone: '**********',
                state: 'الجزائر',
                requestDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'pending',
                licenseType: 'trial',
                deviceInfo: {
                    os: 'Windows 10',
                    processor: 'Intel Core i5',
                    ram: '8GB'
                }
            },
            {
                id: this.generateId(),
                customerName: 'فاطمة حسن محمود',
                phone: '**********',
                state: 'وهران',
                requestDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'approved',
                licenseType: 'lifetime',
                deviceInfo: {
                    os: 'Windows 11',
                    processor: 'AMD Ryzen 7',
                    ram: '16GB'
                }
            },
            {
                id: this.generateId(),
                customerName: 'محمد عبد الرحمن',
                phone: '**********',
                state: 'قسنطينة',
                requestDate: new Date().toISOString(),
                status: 'pending',
                licenseType: 'trial',
                deviceInfo: {
                    os: 'Windows 10',
                    processor: 'Intel Core i7',
                    ram: '12GB'
                }
            }
        ];
        
        this.saveData(this.storageKeys.activationRequests, sampleRequests);
        console.log('✅ تم إنشاء طلبات تفعيل تجريبية');
    }

    // إنشاء تراخيص تجريبية
    createSampleLicenses() {
        const sampleLicenses = [
            {
                id: this.generateId(),
                licenseKey: 'FUEL-ABCD-1234-EFGH-5678',
                customerName: 'فاطمة حسن محمود',
                phone: '**********',
                state: 'وهران',
                licenseType: 'lifetime',
                createdDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                expiryDate: null,
                status: 'active',
                deviceInfo: {
                    os: 'Windows 11',
                    processor: 'AMD Ryzen 7',
                    ram: '16GB'
                },
                activationCount: 1,
                lastActivation: new Date().toISOString()
            }
        ];
        
        this.saveData(this.storageKeys.licenses, sampleLicenses);
        console.log('✅ تم إنشاء تراخيص تجريبية');
    }

    // === طلبات التفعيل ===
    
    // الحصول على جميع طلبات التفعيل
    getActivationRequests() {
        return this.getData(this.storageKeys.activationRequests) || [];
    }

    // إضافة طلب تفعيل جديد مع تشخيص مفصل
    addActivationRequest(requestData) {
        console.log('🚀 بدء إضافة طلب تفعيل جديد...');
        console.log('📝 بيانات الطلب المستلمة:', requestData);

        try {
            // التحقق من صحة البيانات المدخلة
            if (!requestData) {
                throw new Error('بيانات الطلب مفقودة');
            }

            if (!requestData.customerName || !requestData.phone) {
                throw new Error('بيانات الزبون غير مكتملة (الاسم أو الهاتف مفقود)');
            }

            // الحصول على الطلبات الحالية
            console.log('📋 جلب الطلبات الحالية...');
            const requests = this.getActivationRequests();
            console.log(`📊 عدد الطلبات الحالية: ${requests.length}`);

            // إنشاء الطلب الجديد
            const newRequest = {
                id: this.generateId(),
                ...requestData,
                requestDate: new Date().toISOString(),
                status: 'pending',
                createdBy: 'system',
                version: '1.0'
            };

            console.log('✨ تم إنشاء الطلب الجديد:', newRequest);

            // إضافة الطلب للقائمة
            requests.push(newRequest);
            console.log(`📈 العدد الجديد للطلبات: ${requests.length}`);

            // حفظ البيانات
            console.log('💾 حفظ الطلبات المحدثة...');
            const saveResult = this.saveData(this.storageKeys.activationRequests, requests);

            if (!saveResult) {
                throw new Error('فشل في حفظ البيانات');
            }

            console.log('✅ تم حفظ البيانات بنجاح');

            // التحقق من الحفظ
            const savedRequests = this.getActivationRequests();
            const savedRequest = savedRequests.find(r => r.id === newRequest.id);

            if (!savedRequest) {
                throw new Error('فشل في التحقق من حفظ الطلب');
            }

            console.log('✅ تم التحقق من حفظ الطلب بنجاح');

            // تحديث الإحصائيات
            console.log('📊 تحديث الإحصائيات...');
            this.updateStatistics();

            // إضافة سجل
            this.addLog(`تم إضافة طلب تفعيل جديد: ${newRequest.customerName}`, 'success', newRequest);

            // إرسال حدث
            this.triggerEvent('requestAdded', newRequest);

            console.log('🎉 تم إضافة طلب التفعيل بنجاح!');
            console.log('📋 معرف الطلب:', newRequest.id);

            return newRequest;

        } catch (error) {
            console.error('❌ خطأ في إضافة طلب التفعيل:', error);

            // إضافة سجل خطأ
            this.addLog(`فشل في إضافة طلب التفعيل: ${error.message}`, 'error', {
                requestData,
                error: error.message
            });

            // محاولة حفظ احتياطي
            this.saveEmergencyRequest(requestData, error);

            throw error;
        }
    }

    // حفظ طلب طارئ في حالة فشل الحفظ العادي
    saveEmergencyRequest(requestData, error) {
        console.log('🆘 محاولة حفظ طلب طارئ...');

        try {
            const emergencyRequest = {
                ...requestData,
                id: this.generateId(),
                requestDate: new Date().toISOString(),
                status: 'emergency',
                error: error.message,
                savedAt: new Date().toISOString()
            };

            // حفظ في التخزين الطارئ
            if (!window.emergencyStorage) {
                window.emergencyStorage = {};
            }

            if (!window.emergencyStorage.emergencyRequests) {
                window.emergencyStorage.emergencyRequests = [];
            }

            window.emergencyStorage.emergencyRequests.push(emergencyRequest);

            // محاولة حفظ في localStorage أيضاً
            try {
                const emergencyKey = `emergency_request_${Date.now()}`;
                localStorage.setItem(emergencyKey, JSON.stringify(emergencyRequest));
                console.log(`🆘 تم حفظ الطلب الطارئ بالمفتاح: ${emergencyKey}`);
            } catch (localError) {
                console.error('❌ فشل في حفظ الطلب الطارئ في localStorage:', localError);
            }

            console.log('✅ تم حفظ الطلب في التخزين الطارئ');

        } catch (emergencyError) {
            console.error('❌ فشل في حفظ الطلب الطارئ:', emergencyError);
        }
    }

    // الموافقة على طلب تفعيل
    approveActivationRequest(requestId, licenseType = 'trial') {
        const requests = this.getActivationRequests();
        const requestIndex = requests.findIndex(r => r.id === requestId);
        
        if (requestIndex === -1) {
            throw new Error('طلب التفعيل غير موجود');
        }
        
        const request = requests[requestIndex];
        request.status = 'approved';
        request.approvedDate = new Date().toISOString();
        request.licenseType = licenseType;
        
        // إنشاء ترخيص جديد
        const license = this.createLicenseFromRequest(request);
        
        // حفظ التحديثات
        this.saveData(this.storageKeys.activationRequests, requests);
        this.updateStatistics();
        this.addLog(`تم الموافقة على طلب التفعيل وإنشاء ترخيص: ${license.licenseKey}`, 'success', request);
        this.triggerEvent('requestApproved', { request, license });
        
        return { request, license };
    }

    // رفض طلب تفعيل
    rejectActivationRequest(requestId, reason = '') {
        const requests = this.getActivationRequests();
        const requestIndex = requests.findIndex(r => r.id === requestId);
        
        if (requestIndex === -1) {
            throw new Error('طلب التفعيل غير موجود');
        }
        
        const request = requests[requestIndex];
        request.status = 'rejected';
        request.rejectedDate = new Date().toISOString();
        request.rejectionReason = reason;
        
        this.saveData(this.storageKeys.activationRequests, requests);
        this.updateStatistics();
        this.addLog(`تم رفض طلب التفعيل: ${reason}`, 'warning', request);
        this.triggerEvent('requestRejected', request);
        
        return request;
    }

    // === التراخيص ===
    
    // الحصول على جميع التراخيص
    getLicenses() {
        return this.getData(this.storageKeys.licenses) || [];
    }

    // إنشاء ترخيص من طلب تفعيل
    createLicenseFromRequest(request) {
        const licenses = this.getLicenses();
        const settings = this.loadSettings();
        
        const license = {
            id: this.generateId(),
            licenseKey: this.generateLicenseKey(),
            customerName: request.customerName,
            phone: request.phone,
            state: request.state,
            licenseType: request.licenseType,
            createdDate: new Date().toISOString(),
            expiryDate: request.licenseType === 'lifetime' ? null : 
                       new Date(Date.now() + settings.licenseValidityDays * 24 * 60 * 60 * 1000).toISOString(),
            status: 'active',
            deviceInfo: request.deviceInfo,
            activationCount: 0,
            maxDevices: settings.maxDevicesPerLicense,
            requestId: request.id
        };
        
        licenses.push(license);
        this.saveData(this.storageKeys.licenses, licenses);
        this.addLog(`تم إنشاء ترخيص جديد: ${license.licenseKey}`, 'success', license);
        this.triggerEvent('licenseCreated', license);
        
        return license;
    }

    // إنشاء ترخيص يدوي
    createManualLicense(licenseData) {
        const licenses = this.getLicenses();
        const settings = this.loadSettings();
        
        const license = {
            id: this.generateId(),
            licenseKey: this.generateLicenseKey(),
            ...licenseData,
            createdDate: new Date().toISOString(),
            status: 'active',
            activationCount: 0,
            maxDevices: settings.maxDevicesPerLicense
        };
        
        if (license.licenseType !== 'lifetime' && !license.expiryDate) {
            license.expiryDate = new Date(Date.now() + settings.licenseValidityDays * 24 * 60 * 60 * 1000).toISOString();
        }
        
        licenses.push(license);
        this.saveData(this.storageKeys.licenses, licenses);
        this.updateStatistics();
        this.addLog(`تم إنشاء ترخيص يدوي: ${license.licenseKey}`, 'success', license);
        this.triggerEvent('licenseCreated', license);
        
        return license;
    }

    // تعطيل ترخيص
    deactivateLicense(licenseId, reason = '') {
        const licenses = this.getLicenses();
        const licenseIndex = licenses.findIndex(l => l.id === licenseId);
        
        if (licenseIndex === -1) {
            throw new Error('الترخيص غير موجود');
        }
        
        const license = licenses[licenseIndex];
        license.status = 'deactivated';
        license.deactivatedDate = new Date().toISOString();
        license.deactivationReason = reason;
        
        this.saveData(this.storageKeys.licenses, licenses);
        this.updateStatistics();
        this.addLog(`تم تعطيل الترخيص: ${license.licenseKey} - ${reason}`, 'warning', license);
        this.triggerEvent('licenseDeactivated', license);
        
        return license;
    }

    // === الإحصائيات ===
    
    // تحديث الإحصائيات
    updateStatistics() {
        const requests = this.getActivationRequests();
        const licenses = this.getLicenses();
        
        const stats = {
            totalRequests: requests.length,
            approvedRequests: requests.filter(r => r.status === 'approved').length,
            rejectedRequests: requests.filter(r => r.status === 'rejected').length,
            pendingRequests: requests.filter(r => r.status === 'pending').length,
            activeLicenses: licenses.filter(l => l.status === 'active').length,
            expiredLicenses: licenses.filter(l => this.isLicenseExpired(l)).length,
            deactivatedLicenses: licenses.filter(l => l.status === 'deactivated').length,
            totalLicenses: licenses.length,
            lifetimeLicenses: licenses.filter(l => l.licenseType === 'lifetime').length,
            trialLicenses: licenses.filter(l => l.licenseType === 'trial').length,
            lastUpdated: new Date().toISOString()
        };
        
        this.saveData(this.storageKeys.statistics, stats);
        this.triggerEvent('statisticsUpdated', stats);
        
        return stats;
    }

    // الحصول على الإحصائيات
    getStatistics() {
        return this.getData(this.storageKeys.statistics) || this.updateStatistics();
    }

    // === السجلات ===
    
    // إضافة سجل
    addLog(message, type = 'info', data = null) {
        const logs = this.getData(this.storageKeys.logs) || [];
        const log = {
            id: this.generateId(),
            message: message,
            type: type,
            timestamp: new Date().toISOString(),
            data: data
        };
        
        logs.unshift(log); // إضافة في المقدمة
        
        // الاحتفاظ بآخر 1000 سجل فقط
        if (logs.length > 1000) {
            logs.splice(1000);
        }
        
        this.saveData(this.storageKeys.logs, logs);
        this.triggerEvent('logAdded', log);
        
        return log;
    }

    // الحصول على السجلات
    getLogs(limit = 100) {
        const logs = this.getData(this.storageKeys.logs) || [];
        return logs.slice(0, limit);
    }

    // === دوال مساعدة ===
    
    // توليد مفتاح ترخيص
    generateLicenseKey() {
        const prefix = 'FUEL';
        const segments = [];
        
        for (let i = 0; i < 4; i++) {
            const segment = Math.random().toString(36).substr(2, 4).toUpperCase();
            segments.push(segment);
        }
        
        return `${prefix}-${segments.join('-')}`;
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // التحقق من انتهاء صلاحية الترخيص
    isLicenseExpired(license) {
        if (license.licenseType === 'lifetime' || !license.expiryDate) {
            return false;
        }
        
        return new Date() > new Date(license.expiryDate);
    }

    // حفظ البيانات مع تشخيص مفصل
    saveData(key, data) {
        console.log(`💾 محاولة حفظ البيانات - المفتاح: ${key}`);
        console.log(`📊 حجم البيانات: ${JSON.stringify(data).length} حرف`);
        console.log(`🔍 نوع البيانات:`, typeof data);

        try {
            // التحقق من صحة المفتاح
            if (!key || typeof key !== 'string') {
                throw new Error('مفتاح التخزين غير صالح');
            }

            // التحقق من صحة البيانات
            if (data === undefined) {
                throw new Error('البيانات غير محددة');
            }

            // تحويل البيانات إلى JSON
            const jsonData = JSON.stringify(data);
            console.log(`📝 البيانات المحولة: ${jsonData.substring(0, 100)}...`);

            // محاولة الحفظ
            localStorage.setItem(key, jsonData);

            // التحقق من نجاح الحفظ
            const savedData = localStorage.getItem(key);
            if (savedData === jsonData) {
                console.log(`✅ تم حفظ البيانات بنجاح - المفتاح: ${key}`);
                console.log(`📈 إجمالي العناصر في localStorage: ${localStorage.length}`);
                return true;
            } else {
                throw new Error('فشل في التحقق من الحفظ');
            }

        } catch (error) {
            console.error(`❌ خطأ في حفظ البيانات ${key}:`, error);
            console.error(`🔍 تفاصيل الخطأ:`, {
                name: error.name,
                message: error.message,
                stack: error.stack
            });

            // محاولة حفظ بديل
            return this.saveDataFallback(key, data, error);
        }
    }

    // حفظ بديل في حالة فشل الحفظ الأساسي
    saveDataFallback(key, data, originalError) {
        console.log(`🔄 محاولة حفظ بديل للمفتاح: ${key}`);

        try {
            // محاولة 1: حفظ مع مفتاح مختلف
            const fallbackKey = `${key}_backup_${Date.now()}`;
            localStorage.setItem(fallbackKey, JSON.stringify(data));
            console.log(`✅ تم الحفظ البديل بالمفتاح: ${fallbackKey}`);

            // حفظ معلومات الخطأ
            const errorInfo = {
                originalKey: key,
                fallbackKey: fallbackKey,
                error: originalError.message,
                timestamp: new Date().toISOString(),
                dataSize: JSON.stringify(data).length
            };

            localStorage.setItem(`error_log_${Date.now()}`, JSON.stringify(errorInfo));

            return true;

        } catch (fallbackError) {
            console.error(`❌ فشل الحفظ البديل أيضاً:`, fallbackError);

            // محاولة أخيرة: حفظ في متغير عام
            if (!window.emergencyStorage) {
                window.emergencyStorage = {};
            }

            window.emergencyStorage[key] = data;
            console.log(`🆘 تم الحفظ في التخزين الطارئ`);

            return false;
        }
    }

    // قراءة البيانات مع تشخيص مفصل
    getData(key) {
        console.log(`📖 محاولة قراءة البيانات - المفتاح: ${key}`);

        try {
            // التحقق من صحة المفتاح
            if (!key || typeof key !== 'string') {
                throw new Error('مفتاح التخزين غير صالح');
            }

            // محاولة قراءة البيانات
            const rawData = localStorage.getItem(key);
            console.log(`🔍 البيانات الخام موجودة: ${rawData ? 'نعم' : 'لا'}`);

            if (!rawData) {
                console.log(`⚠️ لا توجد بيانات للمفتاح: ${key}`);

                // البحث عن نسخ احتياطية
                const backupData = this.findBackupData(key);
                if (backupData) {
                    console.log(`✅ تم العثور على نسخة احتياطية للمفتاح: ${key}`);
                    return backupData;
                }

                // البحث في التخزين الطارئ
                if (window.emergencyStorage && window.emergencyStorage[key]) {
                    console.log(`🆘 تم العثور على البيانات في التخزين الطارئ`);
                    return window.emergencyStorage[key];
                }

                return null;
            }

            // تحويل البيانات من JSON
            const parsedData = JSON.parse(rawData);
            console.log(`✅ تم قراءة البيانات بنجاح - المفتاح: ${key}`);
            console.log(`📊 عدد العناصر: ${Array.isArray(parsedData) ? parsedData.length : 'غير محدد'}`);

            return parsedData;

        } catch (error) {
            console.error(`❌ خطأ في قراءة البيانات ${key}:`, error);
            console.error(`🔍 تفاصيل الخطأ:`, {
                name: error.name,
                message: error.message
            });

            // محاولة استرداد من النسخ الاحتياطية
            return this.recoverDataFromBackup(key);
        }
    }

    // البحث عن نسخ احتياطية
    findBackupData(key) {
        console.log(`🔍 البحث عن نسخ احتياطية للمفتاح: ${key}`);

        try {
            // البحث في جميع مفاتيح localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const storageKey = localStorage.key(i);
                if (storageKey && storageKey.startsWith(`${key}_backup_`)) {
                    console.log(`📦 تم العثور على نسخة احتياطية: ${storageKey}`);
                    const backupData = localStorage.getItem(storageKey);
                    return JSON.parse(backupData);
                }
            }

            console.log(`❌ لم يتم العثور على نسخ احتياطية للمفتاح: ${key}`);
            return null;

        } catch (error) {
            console.error(`❌ خطأ في البحث عن النسخ الاحتياطية:`, error);
            return null;
        }
    }

    // استرداد البيانات من النسخ الاحتياطية
    recoverDataFromBackup(key) {
        console.log(`🔄 محاولة استرداد البيانات للمفتاح: ${key}`);

        // محاولة البحث عن نسخة احتياطية
        const backupData = this.findBackupData(key);
        if (backupData) {
            console.log(`✅ تم استرداد البيانات من النسخة الاحتياطية`);

            // محاولة إعادة حفظ البيانات في المفتاح الأصلي
            try {
                this.saveData(key, backupData);
                console.log(`✅ تم إعادة حفظ البيانات في المفتاح الأصلي`);
            } catch (error) {
                console.error(`⚠️ فشل في إعادة حفظ البيانات:`, error);
            }

            return backupData;
        }

        // إرجاع قيمة افتراضية حسب نوع المفتاح
        if (key.includes('activationRequests') || key.includes('licenses')) {
            console.log(`📝 إرجاع مصفوفة فارغة للمفتاح: ${key}`);
            return [];
        }

        console.log(`❌ فشل في استرداد البيانات للمفتاح: ${key}`);
        return null;
    }

    // === نظام الأحداث ===
    
    // إضافة مستمع للأحداث
    addEventListener(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    // إزالة مستمع للأحداث
    removeEventListener(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    // تشغيل حدث
    triggerEvent(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`خطأ في معالج الحدث ${event}:`, error);
                }
            });
        }
    }

    // === تصدير البيانات ===
    
    // تصدير جميع البيانات
    exportAllData() {
        const data = {
            activationRequests: this.getActivationRequests(),
            licenses: this.getLicenses(),
            statistics: this.getStatistics(),
            logs: this.getLogs(500),
            settings: this.loadSettings(),
            exportDate: new Date().toISOString()
        };
        
        return data;
    }

    // استيراد البيانات
    importData(data) {
        try {
            if (data.activationRequests) {
                this.saveData(this.storageKeys.activationRequests, data.activationRequests);
            }
            if (data.licenses) {
                this.saveData(this.storageKeys.licenses, data.licenses);
            }
            if (data.settings) {
                this.saveData(this.storageKeys.systemSettings, data.settings);
            }
            
            this.updateStatistics();
            this.addLog('تم استيراد البيانات بنجاح', 'success');
            
            return true;
        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            return false;
        }
    }
}

// إنشاء مثيل واحد للاستخدام العام
const advancedDataManager = new AdvancedDataManager();

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    advancedDataManager.initialize();
});

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AdvancedDataManager, advancedDataManager };
} else {
    window.AdvancedDataManager = AdvancedDataManager;
    window.advancedDataManager = advancedDataManager;
}
