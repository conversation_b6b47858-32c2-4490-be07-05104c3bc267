// نظام المصادقة المحسن والمُصلح
// Fixed Authentication System

class AuthSystemFixed {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        this.sessionKey = 'userSession';
        this.isLoading = false;
        
        // معلومات الدعم
        this.supportInfo = {
            phone: '0696924176',
            whatsapp: '213696924176',
            email: '<EMAIL>'
        };
        
        // بيانات المصادقة
        this.credentials = {
            admin: {
                username: 'admin',
                password: 'admin123',
                userType: 'admin',
                redirectUrl: '../components/admin/advanced-dashboard.html'
            }
        };
    }

    // تهيئة النظام
    initialize() {
        if (this.isInitialized) return;
        
        console.log('🔧 تهيئة نظام المصادقة المحسن...');
        
        try {
            // فحص الجلسة الحالية
            this.loadCurrentSession();
            
            // إعداد معالجات الأحداث
            this.setupEventHandlers();
            
            // إعداد مراقبة الجلسة
            this.setupSessionMonitoring();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة نظام المصادقة بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام المصادقة:', error);
        }
    }

    // تحميل الجلسة الحالية
    loadCurrentSession() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            if (sessionData) {
                const session = JSON.parse(sessionData);
                if (this.isValidSession(session)) {
                    this.currentUser = session;
                    console.log('✅ تم تحميل جلسة صالحة:', session.userType);
                } else {
                    console.log('❌ جلسة غير صالحة، مسح البيانات');
                    this.clearSession();
                }
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل الجلسة:', error);
            this.clearSession();
        }
    }

    // التحقق من صحة الجلسة
    isValidSession(session) {
        if (!session || !session.isValid || !session.loginTime || !session.userType) {
            return false;
        }

        // التحقق من انتهاء الصلاحية (24 ساعة)
        const now = new Date().getTime();
        const loginTime = new Date(session.loginTime).getTime();
        const maxAge = 24 * 60 * 60 * 1000; // 24 ساعة

        if ((now - loginTime) > maxAge) {
            console.log('❌ انتهت صلاحية الجلسة');
            return false;
        }

        return true;
    }

    // إعداد معالجات الأحداث
    setupEventHandlers() {
        // معالج تسجيل دخول المدير
        const adminForm = document.getElementById('adminLoginForm');
        if (adminForm) {
            adminForm.addEventListener('submit', (e) => this.handleAdminLogin(e));
            console.log('✅ تم ربط معالج تسجيل دخول المدير');
        }

        // معالج تسجيل دخول العملاء
        const licenseForm = document.getElementById('licenseLoginForm');
        if (licenseForm) {
            licenseForm.addEventListener('submit', (e) => this.handleLicenseLogin(e));
            console.log('✅ تم ربط معالج تسجيل دخول العملاء');
        }

        // معالجات Enter للحقول
        this.setupEnterKeyHandlers();
    }

    // إعداد معالجات مفتاح Enter
    setupEnterKeyHandlers() {
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        const licenseKeyInput = document.getElementById('licenseKey');
        const customerNameInput = document.getElementById('customerName');

        if (usernameInput && passwordInput) {
            usernameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    passwordInput.focus();
                }
            });

            passwordInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleAdminLogin(e);
                }
            });
        }

        if (licenseKeyInput && customerNameInput) {
            licenseKeyInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    customerNameInput.focus();
                }
            });

            customerNameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleLicenseLogin(e);
                }
            });
        }
    }

    // معالج تسجيل دخول المدير
    async handleAdminLogin(e) {
        e.preventDefault();
        
        if (this.isLoading) {
            console.log('⏳ العملية قيد التنفيذ...');
            return;
        }

        console.log('🔐 بدء تسجيل دخول المدير...');
        this.isLoading = true;

        try {
            // الحصول على البيانات
            const username = document.getElementById('username')?.value?.trim();
            const password = document.getElementById('password')?.value;

            console.log('📝 البيانات المدخلة:', { username, passwordLength: password?.length });

            // التحقق من البيانات
            if (!this.validateAdminInput(username, password)) {
                return;
            }

            // إظهار حالة التحميل
            this.showLoadingState('جاري التحقق من البيانات...');

            // محاكاة تأخير الشبكة
            await this.sleep(1000);

            // التحقق من بيانات الاعتماد
            if (this.verifyAdminCredentials(username, password)) {
                console.log('✅ بيانات صحيحة، إنشاء الجلسة...');
                
                // إنشاء الجلسة
                const sessionData = this.createSession('admin', username);
                
                // حفظ الجلسة
                this.saveSession(sessionData);
                
                // إظهار رسالة النجاح
                this.showSuccess('تم تسجيل الدخول بنجاح! جاري التحويل...');
                
                // التحويل بعد تأخير قصير
                setTimeout(() => {
                    this.redirectToAdminDashboard();
                }, 1500);
                
            } else {
                console.log('❌ بيانات غير صحيحة');
                this.showError('اسم المستخدم أو كلمة المرور غير صحيحة');
            }

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            this.showError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    // التحقق من صحة بيانات المدير
    validateAdminInput(username, password) {
        this.clearMessages();

        if (!username) {
            this.showFieldError('usernameError', 'يرجى إدخال اسم المستخدم');
            return false;
        }

        if (!password) {
            this.showFieldError('passwordError', 'يرجى إدخال كلمة المرور');
            return false;
        }

        if (username.length < 3) {
            this.showFieldError('usernameError', 'اسم المستخدم قصير جداً');
            return false;
        }

        if (password.length < 6) {
            this.showFieldError('passwordError', 'كلمة المرور قصيرة جداً');
            return false;
        }

        return true;
    }

    // التحقق من بيانات الاعتماد
    verifyAdminCredentials(username, password) {
        const admin = this.credentials.admin;
        return username === admin.username && password === admin.password;
    }

    // إنشاء جلسة جديدة
    createSession(userType, username) {
        const now = new Date();
        return {
            username: username,
            userType: userType,
            loginTime: now.toISOString(),
            lastActivity: now.toISOString(),
            expiryTime: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString(),
            isValid: true,
            sessionId: this.generateSessionId()
        };
    }

    // حفظ الجلسة
    saveSession(sessionData) {
        try {
            localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
            this.currentUser = sessionData;
            console.log('💾 تم حفظ الجلسة:', sessionData.userType);
            
            // إرسال حدث تحديث الجلسة
            window.dispatchEvent(new CustomEvent('sessionUpdated', { 
                detail: sessionData 
            }));
            
        } catch (error) {
            console.error('❌ خطأ في حفظ الجلسة:', error);
            throw new Error('فشل في حفظ الجلسة');
        }
    }

    // مسح الجلسة
    clearSession() {
        localStorage.removeItem(this.sessionKey);
        this.currentUser = null;
        console.log('🗑️ تم مسح الجلسة');
    }

    // إعادة التوجيه للوحة تحكم المدير
    redirectToAdminDashboard() {
        console.log('🔄 إعادة التوجيه للوحة تحكم المدير...');
        
        // التأكد من حفظ الجلسة قبل التحويل
        const savedSession = localStorage.getItem(this.sessionKey);
        console.log('🔍 الجلسة قبل التحويل:', savedSession ? 'موجودة' : 'غير موجودة');
        
        if (savedSession) {
            window.location.href = this.credentials.admin.redirectUrl;
        } else {
            console.error('❌ لم يتم حفظ الجلسة، إعادة المحاولة...');
            this.showError('خطأ في حفظ الجلسة، يرجى المحاولة مرة أخرى');
        }
    }

    // معالج تسجيل دخول العملاء (مبسط)
    async handleLicenseLogin(e) {
        e.preventDefault();
        console.log('🎫 تسجيل دخول العملاء قيد التطوير...');
        this.showError('تسجيل دخول العملاء قيد التطوير');
    }

    // إعداد مراقبة الجلسة
    setupSessionMonitoring() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (e.key === this.sessionKey) {
                console.log('🔄 تم تحديث الجلسة');
                this.loadCurrentSession();
            }
        });

        // فحص دوري للجلسة
        setInterval(() => {
            if (this.currentUser && !this.isValidSession(this.currentUser)) {
                console.log('⚠️ انتهت صلاحية الجلسة');
                this.clearSession();
                this.showSessionExpiredMessage();
            }
        }, 60000); // فحص كل دقيقة
    }

    // دوال مساعدة للواجهة
    showLoadingState(message) {
        const submitBtn = document.querySelector('#adminLoginForm button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${message}`;
        }
    }

    hideLoadingState() {
        const submitBtn = document.querySelector('#adminLoginForm button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> تسجيل دخول المدير';
        }
    }

    showSuccess(message) {
        this.showMessage(message, 'success', 'adminSuccess');
    }

    showError(message) {
        this.showMessage(message, 'error', 'adminError');
    }

    showFieldError(fieldId, message) {
        const errorElement = document.getElementById(fieldId);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    clearMessages() {
        const errorElements = document.querySelectorAll('.error-message');
        errorElements.forEach(el => {
            el.textContent = '';
            el.style.display = 'none';
        });

        const messageElements = document.querySelectorAll('.message');
        messageElements.forEach(el => el.remove());
    }

    showMessage(message, type, containerId) {
        // إزالة الرسائل السابقة
        const existingMessages = document.querySelectorAll('.message');
        existingMessages.forEach(msg => msg.remove());

        // إنشاء رسالة جديدة
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        `;

        // إضافة الرسالة
        const container = document.getElementById(containerId) || document.querySelector('.login-form');
        if (container) {
            container.appendChild(messageDiv);
        }

        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 5000);
    }

    showSessionExpiredMessage() {
        this.showError('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
    }

    // دوال مساعدة
    generateSessionId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // التحقق من تسجيل الدخول
    isLoggedIn() {
        return this.currentUser && this.isValidSession(this.currentUser);
    }

    // تسجيل الخروج
    logout() {
        this.clearSession();
        window.location.href = 'new-login.html';
    }
}

// إنشاء مثيل واحد للاستخدام العام
const authSystemFixed = new AuthSystemFixed();

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة نظام المصادقة المحسن...');
    authSystemFixed.initialize();
});

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AuthSystemFixed, authSystemFixed };
} else {
    window.AuthSystemFixed = AuthSystemFixed;
    window.authSystemFixed = authSystemFixed;
}
