// نظام حماية المصادقة - Auth Guard
// يحمي جميع الصفحات من الوصول غير المصرح به

class AuthGuard {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        this.redirectUrl = null;
        this.supportInfo = {
            phone: '0696924176',
            whatsapp: '213696924176',
            email: '<EMAIL>'
        };
    }

    // تهيئة نظام الحماية
    async initialize() {
        if (this.isInitialized) return;

        try {
            // التحقق من الجلسة الحالية
            await this.checkCurrentSession();
            
            // إعداد مراقبة تغييرات الجلسة
            this.setupSessionMonitoring();
            
            // إعداد حماية الصفحة
            this.setupPageProtection();
            
            this.isInitialized = true;
            console.log('🛡️ تم تهيئة نظام الحماية بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام الحماية:', error);
            this.redirectToLogin();
        }
    }

    // التحقق من الجلسة الحالية
    async checkCurrentSession() {
        try {
            const sessionData = localStorage.getItem('userSession');
            
            if (!sessionData) {
                throw new Error('لا توجد جلسة نشطة');
            }

            const session = JSON.parse(sessionData);
            
            // التحقق من صحة الجلسة
            if (!this.isValidSession(session)) {
                throw new Error('جلسة غير صالحة');
            }

            // التحقق من انتهاء صلاحية الجلسة
            if (this.isSessionExpired(session)) {
                throw new Error('انتهت صلاحية الجلسة');
            }

            // حفظ بيانات المستخدم
            this.currentUser = session;
            
            // تحديث آخر نشاط
            this.updateLastActivity();
            
            console.log('✅ الجلسة صالحة:', session.userType);
            return true;
            
        } catch (error) {
            console.log('❌ جلسة غير صالحة:', error.message);
            this.clearSession();
            throw error;
        }
    }

    // التحقق من صحة الجلسة
    isValidSession(session) {
        return session && 
               session.isValid && 
               (session.userType === 'admin' || session.userType === 'customer') &&
               session.loginTime;
    }

    // التحقق من انتهاء صلاحية الجلسة
    isSessionExpired(session) {
        const now = new Date().getTime();
        const loginTime = new Date(session.loginTime).getTime();
        const maxAge = 24 * 60 * 60 * 1000; // 24 ساعة
        
        return (now - loginTime) > maxAge;
    }

    // تحديث آخر نشاط
    updateLastActivity() {
        if (this.currentUser) {
            this.currentUser.lastActivity = new Date().toISOString();
            localStorage.setItem('userSession', JSON.stringify(this.currentUser));
        }
    }

    // إعداد مراقبة تغييرات الجلسة
    setupSessionMonitoring() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (e) => {
            if (e.key === 'userSession') {
                if (!e.newValue) {
                    // تم حذف الجلسة
                    this.handleSessionLoss();
                }
            }
        });

        // فحص دوري للجلسة كل 5 دقائق
        setInterval(() => {
            this.validateCurrentSession();
        }, 5 * 60 * 1000);

        // تحديث النشاط عند تفاعل المستخدم
        ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
            document.addEventListener(event, () => {
                this.updateLastActivity();
            }, { passive: true });
        });
    }

    // إعداد حماية الصفحة
    setupPageProtection() {
        // منع الوصول المباشر للملفات
        this.protectDirectAccess();
        
        // حماية من فتح أدوات المطور (اختياري)
        this.setupDevToolsProtection();
        
        // حماية من النسخ واللصق (اختياري)
        this.setupContentProtection();
    }

    // حماية الوصول المباشر
    protectDirectAccess() {
        // التحقق من المرجع
        if (document.referrer === '' && window.location.pathname !== '/src/auth/new-login.html') {
            console.log('🚫 محاولة وصول مباشر مرفوضة');
            this.redirectToLogin();
        }
    }

    // حماية من أدوات المطور (اختياري)
    setupDevToolsProtection() {
        // يمكن تفعيل هذا في الإنتاج
        if (false) { // تم تعطيله للتطوير
            let devtools = {open: false, orientation: null};
            
            setInterval(() => {
                if (window.outerHeight - window.innerHeight > 200 || 
                    window.outerWidth - window.innerWidth > 200) {
                    if (!devtools.open) {
                        devtools.open = true;
                        console.log('🚫 تم اكتشاف فتح أدوات المطور');
                        this.showDevToolsWarning();
                    }
                } else {
                    devtools.open = false;
                }
            }, 500);
        }
    }

    // حماية المحتوى (اختياري)
    setupContentProtection() {
        // منع النقر بالزر الأيمن
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showProtectionMessage('النقر بالزر الأيمن غير مسموح');
        });

        // منع اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            // منع F12, Ctrl+Shift+I, Ctrl+U, إلخ
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
                this.showProtectionMessage('هذا الإجراء غير مسموح');
            }
        });

        // منع تحديد النص
        document.addEventListener('selectstart', (e) => {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
            }
        });
    }

    // التحقق من الجلسة الحالية
    async validateCurrentSession() {
        try {
            await this.checkCurrentSession();
        } catch (error) {
            this.handleSessionLoss();
        }
    }

    // معالجة فقدان الجلسة
    handleSessionLoss() {
        console.log('⚠️ تم فقدان الجلسة');
        this.showSessionExpiredMessage();
        setTimeout(() => {
            this.redirectToLogin();
        }, 3000);
    }

    // مسح الجلسة
    clearSession() {
        localStorage.removeItem('userSession');
        this.currentUser = null;
    }

    // إعادة التوجيه لتسجيل الدخول
    redirectToLogin() {
        // حفظ الصفحة الحالية للعودة إليها لاحقاً
        this.redirectUrl = window.location.href;
        
        // تحديد مسار تسجيل الدخول حسب الموقع الحالي
        const currentPath = window.location.pathname;
        let loginPath = '';
        
        if (currentPath.includes('/auth/')) {
            loginPath = 'new-login.html';
        } else if (currentPath.includes('/components/')) {
            loginPath = '../../auth/new-login.html';
        } else if (currentPath.includes('/utils/')) {
            loginPath = '../auth/new-login.html';
        } else {
            loginPath = 'src/auth/new-login.html';
        }
        
        console.log('🔄 إعادة توجيه لتسجيل الدخول:', loginPath);
        window.location.href = loginPath;
    }

    // الحصول على معلومات المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // التحقق من نوع المستخدم
    isAdmin() {
        return this.currentUser && this.currentUser.userType === 'admin';
    }

    isCustomer() {
        return this.currentUser && this.currentUser.userType === 'customer';
    }

    // تسجيل الخروج
    logout() {
        this.clearSession();
        this.redirectToLogin();
    }

    // إظهار رسالة انتهاء الجلسة
    showSessionExpiredMessage() {
        this.showMessage('انتهت صلاحية الجلسة. سيتم إعادة توجيهك لتسجيل الدخول...', 'warning');
    }

    // إظهار تحذير أدوات المطور
    showDevToolsWarning() {
        this.showMessage('تم اكتشاف محاولة فتح أدوات المطور. يرجى إغلاقها للمتابعة.', 'error');
    }

    // إظهار رسالة الحماية
    showProtectionMessage(message) {
        this.showMessage(message, 'info');
    }

    // إظهار رسالة عامة
    showMessage(message, type = 'info') {
        // إنشاء عنصر الرسالة
        const messageDiv = document.createElement('div');
        messageDiv.className = `auth-guard-message ${type}`;
        messageDiv.innerHTML = `
            <div class="message-content">
                <i class="fas fa-${this.getMessageIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <div class="support-info">
                <p>للدعم الفني:</p>
                <div class="support-contacts">
                    <a href="tel:${this.supportInfo.phone}" class="support-link">
                        <i class="fas fa-phone"></i> ${this.supportInfo.phone}
                    </a>
                    <a href="https://wa.me/${this.supportInfo.whatsapp}" target="_blank" class="support-link">
                        <i class="fab fa-whatsapp"></i> واتساب
                    </a>
                </div>
            </div>
        `;

        // إضافة التنسيقات
        messageDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 400px;
            text-align: center;
            border: 3px solid ${this.getMessageColor(type)};
            font-family: Arial, sans-serif;
            direction: rtl;
        `;

        // إضافة للصفحة
        document.body.appendChild(messageDiv);

        // إزالة بعد 5 ثوان
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 5000);
    }

    // الحصول على أيقونة الرسالة
    getMessageIcon(type) {
        const icons = {
            info: 'info-circle',
            warning: 'exclamation-triangle',
            error: 'exclamation-circle',
            success: 'check-circle'
        };
        return icons[type] || 'info-circle';
    }

    // الحصول على لون الرسالة
    getMessageColor(type) {
        const colors = {
            info: '#3498db',
            warning: '#f39c12',
            error: '#e74c3c',
            success: '#27ae60'
        };
        return colors[type] || '#3498db';
    }
}

// إنشاء مثيل واحد للاستخدام العام
const authGuard = new AuthGuard();

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AuthGuard, authGuard };
} else {
    window.AuthGuard = AuthGuard;
    window.authGuard = authGuard;
}

// تهيئة تلقائية عند تحميل الصفحة (إلا إذا كانت صفحة تسجيل الدخول)
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    
    // عدم تطبيق الحماية على صفحة تسجيل الدخول وطلب التفعيل
    if (!currentPath.includes('new-login.html') && 
        !currentPath.includes('activation-request.html') &&
        !currentPath.includes('test-login.html')) {
        
        console.log('🛡️ تطبيق حماية المصادقة على:', currentPath);
        authGuard.initialize().catch(() => {
            console.log('🚫 فشل في المصادقة، إعادة توجيه...');
        });
    }
});
