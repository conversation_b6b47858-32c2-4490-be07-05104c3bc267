// مكتبة التحقق من صحة البيانات

class Validator {
    constructor() {
        this.errors = [];
    }

    // مسح الأخطاء
    clearErrors() {
        this.errors = [];
    }

    // إضافة خطأ
    addError(field, message) {
        this.errors.push({ field, message });
    }

    // الحصول على الأخطاء
    getErrors() {
        return this.errors;
    }

    // التحقق من وجود أخطاء
    hasErrors() {
        return this.errors.length > 0;
    }

    // التحقق من النص المطلوب
    required(value, fieldName) {
        if (!value || value.toString().trim() === '') {
            this.addError(fieldName, `${fieldName} مطلوب`);
            return false;
        }
        return true;
    }

    // التحقق من الحد الأدنى للطول
    minLength(value, min, fieldName) {
        if (value && value.toString().length < min) {
            this.addError(fieldName, `${fieldName} يجب أن يكون ${min} أحرف على الأقل`);
            return false;
        }
        return true;
    }

    // التحقق من الحد الأقصى للطول
    maxLength(value, max, fieldName) {
        if (value && value.toString().length > max) {
            this.addError(fieldName, `${fieldName} يجب أن يكون ${max} أحرف كحد أقصى`);
            return false;
        }
        return true;
    }

    // التحقق من البريد الإلكتروني
    email(value, fieldName = 'البريد الإلكتروني') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (value && !emailRegex.test(value)) {
            this.addError(fieldName, `${fieldName} غير صحيح`);
            return false;
        }
        return true;
    }

    // التحقق من رقم الهاتف
    phone(value, fieldName = 'رقم الهاتف') {
        const phoneRegex = /^(\+966|0)?[5-9]\d{8}$/;
        if (value && !phoneRegex.test(value.replace(/\s/g, ''))) {
            this.addError(fieldName, `${fieldName} غير صحيح`);
            return false;
        }
        return true;
    }

    // التحقق من الرقم
    numeric(value, fieldName) {
        if (value && isNaN(value)) {
            this.addError(fieldName, `${fieldName} يجب أن يكون رقماً`);
            return false;
        }
        return true;
    }

    // التحقق من الرقم الموجب
    positive(value, fieldName) {
        if (value && (isNaN(value) || parseFloat(value) <= 0)) {
            this.addError(fieldName, `${fieldName} يجب أن يكون رقماً موجباً`);
            return false;
        }
        return true;
    }

    // التحقق من التاريخ
    date(value, fieldName = 'التاريخ') {
        if (value && isNaN(Date.parse(value))) {
            this.addError(fieldName, `${fieldName} غير صحيح`);
            return false;
        }
        return true;
    }

    // التحقق من كلمة المرور القوية
    strongPassword(value, fieldName = 'كلمة المرور') {
        if (!value) return true;
        
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        const hasNumbers = /\d/.test(value);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
        
        if (value.length < minLength) {
            this.addError(fieldName, `${fieldName} يجب أن تكون ${minLength} أحرف على الأقل`);
            return false;
        }
        
        if (!hasUpperCase || !hasLowerCase) {
            this.addError(fieldName, `${fieldName} يجب أن تحتوي على أحرف كبيرة وصغيرة`);
            return false;
        }
        
        if (!hasNumbers) {
            this.addError(fieldName, `${fieldName} يجب أن تحتوي على أرقام`);
            return false;
        }
        
        if (!hasSpecialChar) {
            this.addError(fieldName, `${fieldName} يجب أن تحتوي على رموز خاصة`);
            return false;
        }
        
        return true;
    }

    // التحقق من تطابق كلمات المرور
    passwordMatch(password, confirmPassword) {
        if (password !== confirmPassword) {
            this.addError('تأكيد كلمة المرور', 'كلمات المرور غير متطابقة');
            return false;
        }
        return true;
    }

    // التحقق من الرقم القومي السعودي
    saudiId(value, fieldName = 'رقم الهوية') {
        if (!value) return true;
        
        const idRegex = /^[12]\d{9}$/;
        if (!idRegex.test(value)) {
            this.addError(fieldName, `${fieldName} غير صحيح`);
            return false;
        }
        
        // خوارزمية التحقق من صحة الرقم القومي السعودي
        const digits = value.split('').map(Number);
        let sum = 0;
        
        for (let i = 0; i < 9; i++) {
            if (i % 2 === 0) {
                const doubled = digits[i] * 2;
                sum += doubled > 9 ? doubled - 9 : doubled;
            } else {
                sum += digits[i];
            }
        }
        
        const checkDigit = (10 - (sum % 10)) % 10;
        if (checkDigit !== digits[9]) {
            this.addError(fieldName, `${fieldName} غير صحيح`);
            return false;
        }
        
        return true;
    }

    // التحقق من نطاق الأرقام
    range(value, min, max, fieldName) {
        const num = parseFloat(value);
        if (value && (isNaN(num) || num < min || num > max)) {
            this.addError(fieldName, `${fieldName} يجب أن يكون بين ${min} و ${max}`);
            return false;
        }
        return true;
    }

    // التحقق من الملف
    file(file, allowedTypes, maxSize, fieldName = 'الملف') {
        if (!file) return true;
        
        // التحقق من نوع الملف
        if (allowedTypes && allowedTypes.length > 0) {
            const fileType = file.type;
            const fileName = file.name.toLowerCase();
            const isAllowed = allowedTypes.some(type => 
                fileType.includes(type) || fileName.endsWith(type)
            );
            
            if (!isAllowed) {
                this.addError(fieldName, `${fieldName} يجب أن يكون من النوع: ${allowedTypes.join(', ')}`);
                return false;
            }
        }
        
        // التحقق من حجم الملف
        if (maxSize && file.size > maxSize) {
            const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(1);
            this.addError(fieldName, `${fieldName} يجب أن يكون أقل من ${maxSizeMB} ميجابايت`);
            return false;
        }
        
        return true;
    }

    // التحقق من عنوان URL
    url(value, fieldName = 'الرابط') {
        if (!value) return true;
        
        try {
            new URL(value);
            return true;
        } catch {
            this.addError(fieldName, `${fieldName} غير صحيح`);
            return false;
        }
    }

    // التحقق المخصص
    custom(value, validationFn, errorMessage, fieldName) {
        if (!validationFn(value)) {
            this.addError(fieldName, errorMessage);
            return false;
        }
        return true;
    }
}

// دوال مساعدة للتحقق السريع
const ValidationHelpers = {
    // تنظيف النص
    sanitizeText(text) {
        if (!text) return '';
        return text.toString().trim().replace(/\s+/g, ' ');
    },

    // تنسيق رقم الهاتف
    formatPhone(phone) {
        if (!phone) return '';
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.startsWith('966')) {
            return '+' + cleaned;
        } else if (cleaned.startsWith('0')) {
            return '+966' + cleaned.substring(1);
        } else if (cleaned.length === 9) {
            return '+966' + cleaned;
        }
        return phone;
    },

    // تنسيق البريد الإلكتروني
    formatEmail(email) {
        if (!email) return '';
        return email.toLowerCase().trim();
    },

    // إنشاء كلمة مرور قوية
    generateStrongPassword(length = 12) {
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return password;
    },

    // التحقق من قوة كلمة المرور
    getPasswordStrength(password) {
        if (!password) return { score: 0, text: 'ضعيف جداً' };
        
        let score = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            numbers: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };
        
        score = Object.values(checks).filter(Boolean).length;
        
        const strengthLevels = [
            { score: 0, text: 'ضعيف جداً', color: '#e74c3c' },
            { score: 1, text: 'ضعيف', color: '#e67e22' },
            { score: 2, text: 'متوسط', color: '#f39c12' },
            { score: 3, text: 'جيد', color: '#27ae60' },
            { score: 4, text: 'قوي', color: '#2ecc71' },
            { score: 5, text: 'قوي جداً', color: '#16a085' }
        ];
        
        return strengthLevels[score] || strengthLevels[0];
    }
};

// تصدير للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Validator, ValidationHelpers };
} else {
    window.Validator = Validator;
    window.ValidationHelpers = ValidationHelpers;
}
