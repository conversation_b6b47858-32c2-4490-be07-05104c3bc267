<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب تفعيل محسن - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #f39c12;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .form-container {
            padding: 2rem;
        }

        .progress-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 20px;
            right: 0;
            left: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 20px;
            right: 0;
            height: 2px;
            background: #3498db;
            z-index: 2;
            transition: width 0.3s ease;
            width: 0%;
        }

        .step {
            background: white;
            border: 3px solid #e9ecef;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            z-index: 3;
            position: relative;
            transition: all 0.3s ease;
        }

        .step.active {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }

        .step.completed {
            border-color: #27ae60;
            background: #27ae60;
            color: white;
        }

        .form-step {
            display: none;
        }

        .form-step.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step-title {
            text-align: center;
            margin-bottom: 2rem;
        }

        .step-title h2 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .step-title p {
            color: #7f8c8d;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-group.error input,
        .form-group.error select {
            border-color: #e74c3c;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 0.5rem;
            display: none;
        }

        .form-group.error .error-message {
            display: block;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .license-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .license-option {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .license-option:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }

        .license-option.selected {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        .license-option input[type="radio"] {
            position: absolute;
            opacity: 0;
        }

        .license-option .icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #3498db;
        }

        .license-option h3 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .license-option p {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .device-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .device-info h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .device-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .device-detail:last-child {
            border-bottom: none;
        }

        .device-detail .label {
            font-weight: 600;
            color: #2c3e50;
        }

        .device-detail .value {
            color: #7f8c8d;
        }

        .summary-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }

        .summary-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .summary-item .label {
            font-weight: 600;
            color: #2c3e50;
        }

        .summary-item .value {
            color: #3498db;
            font-weight: 600;
        }

        .form-actions {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .support-section {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(155, 89, 182, 0.1));
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            margin-top: 2rem;
        }

        .support-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .support-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .support-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.2rem;
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .support-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
        }

        .support-btn.whatsapp {
            background: linear-gradient(135deg, #25d366, #20ba5a);
        }

        .support-btn.whatsapp:hover {
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
        }

        .success-message {
            text-align: center;
            padding: 2rem;
        }

        .success-icon {
            font-size: 4rem;
            color: #27ae60;
            margin-bottom: 1rem;
        }

        .success-message h2 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .success-message p {
            color: #7f8c8d;
            margin-bottom: 2rem;
        }

        .request-id {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 2rem;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .license-options {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }

            .support-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <i class="fas fa-gas-pump"></i>
            </div>
            <h1>طلب تفعيل محسن</h1>
            <p>مؤسسة وقود المستقبل - Future Fuel Corporation</p>
        </div>

        <div class="form-container">
            <!-- شريط التقدم -->
            <div class="progress-bar" id="progressBar">
                <div class="step active" data-step="1">1</div>
                <div class="step" data-step="2">2</div>
                <div class="step" data-step="3">3</div>
                <div class="step" data-step="4">4</div>
            </div>

            <form id="activationForm">
                <!-- الخطوة 1: المعلومات الشخصية -->
                <div class="form-step active" data-step="1">
                    <div class="step-title">
                        <h2><i class="fas fa-user"></i> المعلومات الشخصية</h2>
                        <p>يرجى إدخال معلوماتك الشخصية بدقة</p>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerName">الاسم الكامل *</label>
                            <input type="text" id="customerName" name="customerName" required>
                            <div class="error-message">يرجى إدخال الاسم الكامل</div>
                        </div>

                        <div class="form-group">
                            <label for="phone">رقم الهاتف *</label>
                            <input type="tel" id="phone" name="phone" required placeholder="0555123456">
                            <div class="error-message">يرجى إدخال رقم هاتف صحيح</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="state">الولاية *</label>
                        <select id="state" name="state" required>
                            <option value="">اختر الولاية</option>
                            <option value="الجزائر">01 - الجزائر</option>
                            <option value="البليدة">09 - البليدة</option>
                            <option value="وهران">31 - وهران</option>
                            <option value="قسنطينة">25 - قسنطينة</option>
                            <option value="عنابة">23 - عنابة</option>
                            <option value="باتنة">05 - باتنة</option>
                            <option value="سطيف">19 - سطيف</option>
                            <option value="سيدي بلعباس">22 - سيدي بلعباس</option>
                            <option value="بسكرة">07 - بسكرة</option>
                            <option value="تلمسان">13 - تلمسان</option>
                        </select>
                        <div class="error-message">يرجى اختيار الولاية</div>
                    </div>
                </div>

                <!-- الخطوة 2: نوع الترخيص -->
                <div class="form-step" data-step="2">
                    <div class="step-title">
                        <h2><i class="fas fa-key"></i> نوع الترخيص</h2>
                        <p>اختر نوع الترخيص المناسب لاحتياجاتك</p>
                    </div>

                    <div class="license-options">
                        <div class="license-option" onclick="selectLicense('trial')">
                            <input type="radio" name="licenseType" value="trial" id="trial">
                            <div class="icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h3>ترخيص تجريبي</h3>
                            <p>صالح لمدة 30 يوم<br>مجاني للتجربة</p>
                        </div>

                        <div class="license-option" onclick="selectLicense('lifetime')">
                            <input type="radio" name="licenseType" value="lifetime" id="lifetime">
                            <div class="icon">
                                <i class="fas fa-infinity"></i>
                            </div>
                            <h3>ترخيص مدى الحياة</h3>
                            <p>صالح مدى الحياة<br>دفعة واحدة</p>
                        </div>
                    </div>
                </div>

                <!-- الخطوة 3: معلومات الجهاز -->
                <div class="form-step" data-step="3">
                    <div class="step-title">
                        <h2><i class="fas fa-desktop"></i> معلومات الجهاز</h2>
                        <p>سيتم جمع معلومات الجهاز تلقائياً</p>
                    </div>

                    <div class="device-info">
                        <h3><i class="fas fa-info-circle"></i> تفاصيل الجهاز</h3>
                        <div class="device-detail">
                            <span class="label">نظام التشغيل:</span>
                            <span class="value" id="deviceOS">جاري التحديد...</span>
                        </div>
                        <div class="device-detail">
                            <span class="label">المعالج:</span>
                            <span class="value" id="deviceProcessor">جاري التحديد...</span>
                        </div>
                        <div class="device-detail">
                            <span class="label">الذاكرة:</span>
                            <span class="value" id="deviceRAM">جاري التحديد...</span>
                        </div>
                        <div class="device-detail">
                            <span class="label">المتصفح:</span>
                            <span class="value" id="deviceBrowser">جاري التحديد...</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="agreeTerms" required>
                            أوافق على <a href="#" onclick="showTerms()">شروط الاستخدام</a> و <a href="#" onclick="showPrivacy()">سياسة الخصوصية</a>
                        </label>
                        <div class="error-message">يجب الموافقة على الشروط والأحكام</div>
                    </div>
                </div>

                <!-- الخطوة 4: المراجعة والإرسال -->
                <div class="form-step" data-step="4">
                    <div class="step-title">
                        <h2><i class="fas fa-check-circle"></i> مراجعة الطلب</h2>
                        <p>يرجى مراجعة المعلومات قبل الإرسال</p>
                    </div>

                    <div class="summary-section">
                        <div class="summary-item">
                            <span class="label">الاسم:</span>
                            <span class="value" id="summaryName">-</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">رقم الهاتف:</span>
                            <span class="value" id="summaryPhone">-</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">الولاية:</span>
                            <span class="value" id="summaryState">-</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">نوع الترخيص:</span>
                            <span class="value" id="summaryLicenseType">-</span>
                        </div>
                    </div>
                </div>

                <!-- رسالة النجاح -->
                <div class="form-step" data-step="success" style="display: none;">
                    <div class="success-message">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2>تم إرسال طلبك بنجاح!</h2>
                        <p>سيتم مراجعة طلبك والرد عليك في أقرب وقت ممكن</p>
                        
                        <div class="request-id">
                            رقم الطلب: <span id="requestIdDisplay">-</span>
                        </div>

                        <div class="support-section">
                            <h3>للاستفسار عن حالة طلبك</h3>
                            <div class="support-buttons">
                                <a href="tel:0696924176" class="support-btn">
                                    <i class="fas fa-phone"></i>
                                    0696924176
                                </a>
                                <a href="https://wa.me/213696924176" target="_blank" class="support-btn whatsapp">
                                    <i class="fab fa-whatsapp"></i>
                                    واتساب
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التنقل -->
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button type="button" class="btn btn-primary" id="nextBtn" onclick="nextStep()">
                        التالي
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                        <i class="fas fa-paper-plane"></i>
                        إرسال الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="../utils/advanced-data-manager.js"></script>
    <script src="activation-request-enhanced.js"></script>
</body>
</html>
