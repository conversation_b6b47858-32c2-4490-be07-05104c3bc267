<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار النظام المحسن - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .link-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
            border: 2px solid #e9ecef;
        }

        .link-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
        }

        .link-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #3498db;
        }

        .credentials {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: monospace;
        }

        .test-results {
            max-height: 300px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
        }

        .log-entry {
            padding: 0.5rem;
            border-bottom: 1px solid #eee;
            font-family: monospace;
            font-size: 0.8rem;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-entry.success { color: #27ae60; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.warning { color: #f39c12; }
        .log-entry.info { color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار النظام المحسن</h1>
            <p>اختبار شامل لنظام المصادقة والحماية المحسن</p>
        </div>

        <div class="content">
            <!-- معلومات تسجيل الدخول -->
            <div class="test-section">
                <h3><i class="fas fa-key"></i> معلومات تسجيل الدخول</h3>
                <div class="credentials">
                    <strong>🔐 بيانات المدير:</strong><br>
                    اسم المستخدم: admin<br>
                    كلمة المرور: admin123
                </div>
            </div>

            <!-- اختبار الجلسة -->
            <div class="test-section">
                <h3><i class="fas fa-user-check"></i> اختبار الجلسة الحالية</h3>
                <button class="btn btn-primary" onclick="checkSession()">
                    <i class="fas fa-search"></i> فحص الجلسة
                </button>
                <button class="btn btn-success" onclick="createTestSession()">
                    <i class="fas fa-plus"></i> إنشاء جلسة تجريبية
                </button>
                <button class="btn btn-danger" onclick="clearSession()">
                    <i class="fas fa-trash"></i> مسح الجلسة
                </button>
                <div id="sessionStatus" class="status info">
                    اضغط "فحص الجلسة" لعرض الحالة الحالية
                </div>
            </div>

            <!-- اختبار النظام المحسن -->
            <div class="test-section">
                <h3><i class="fas fa-cogs"></i> اختبار النظام المحسن</h3>
                <button class="btn btn-success" onclick="testFixedSystem()">
                    <i class="fas fa-play"></i> اختبار النظام المحسن
                </button>
                <button class="btn btn-warning" onclick="testProtectionSystem()">
                    <i class="fas fa-shield-alt"></i> اختبار نظام الحماية
                </button>
                <button class="btn btn-info" onclick="simulateLoginFlow()">
                    <i class="fas fa-route"></i> محاكاة تدفق تسجيل الدخول
                </button>
                <div id="systemTestStatus" class="status info">
                    جاهز لاختبار النظام المحسن
                </div>
            </div>

            <!-- سجل الاختبارات -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> سجل الاختبارات</h3>
                <button class="btn btn-warning" onclick="clearLog()">
                    <i class="fas fa-broom"></i> مسح السجل
                </button>
                <div id="testLog" class="test-results">
                    <div class="log-entry info">🧪 جاهز لبدء الاختبارات...</div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="quick-links">
                <a href="src/auth/login-fixed.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-sign-in-alt"></i></div>
                    <h4>صفحة تسجيل الدخول المحسنة</h4>
                    <p>النظام الجديد المُصلح</p>
                </a>
                <a href="src/auth/new-login.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-sign-in-alt"></i></div>
                    <h4>صفحة تسجيل الدخول الأصلية</h4>
                    <p>النظام القديم للمقارنة</p>
                </a>
                <a href="src/components/admin/license-management.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-cogs"></i></div>
                    <h4>لوحة تحكم المدير</h4>
                    <p>الصفحة المحمية</p>
                </a>
                <a href="diagnostic-tool.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-stethoscope"></i></div>
                    <h4>أداة التشخيص</h4>
                    <p>تشخيص شامل للنظام</p>
                </a>
            </div>
        </div>
    </div>

    <script>
        // متغيرات النظام
        const SESSION_KEY = 'userSession';
        let testCounter = 0;

        // إضافة سجل
        function addLog(message, type = 'info') {
            testCounter++;
            const logContainer = document.getElementById('testLog');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${testCounter}. ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // فحص الجلسة
        function checkSession() {
            addLog('🔍 فحص الجلسة الحالية...', 'info');
            
            const sessionData = localStorage.getItem(SESSION_KEY);
            const statusDiv = document.getElementById('sessionStatus');
            
            if (sessionData) {
                try {
                    const session = JSON.parse(sessionData);
                    const isValid = session.isValid && session.userType && session.loginTime;
                    
                    statusDiv.className = `status ${isValid ? 'success' : 'warning'}`;
                    statusDiv.innerHTML = `
                        <strong>${isValid ? '✅ جلسة صالحة' : '⚠️ جلسة غير مكتملة'}:</strong><br>
                        نوع المستخدم: ${session.userType || 'غير محدد'}<br>
                        اسم المستخدم: ${session.username || session.customerName || 'غير محدد'}<br>
                        وقت تسجيل الدخول: ${session.loginTime || 'غير محدد'}<br>
                        صالحة: ${session.isValid ? 'نعم' : 'لا'}<br>
                        <details style="margin-top: 0.5rem;">
                            <summary>البيانات الكاملة</summary>
                            <pre style="font-size: 0.7rem; margin-top: 0.5rem;">${JSON.stringify(session, null, 2)}</pre>
                        </details>
                    `;
                    
                    addLog(`${isValid ? '✅' : '⚠️'} الجلسة: ${session.userType || 'غير محدد'}`, isValid ? 'success' : 'warning');
                    
                } catch (error) {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `❌ خطأ في قراءة الجلسة: ${error.message}`;
                    addLog(`❌ خطأ في قراءة الجلسة: ${error.message}`, 'error');
                }
            } else {
                statusDiv.className = 'status warning';
                statusDiv.innerHTML = '❌ لا توجد جلسة محفوظة';
                addLog('❌ لا توجد جلسة محفوظة', 'warning');
            }
        }

        // إنشاء جلسة تجريبية
        function createTestSession() {
            addLog('🔧 إنشاء جلسة تجريبية...', 'info');
            
            const sessionData = {
                username: 'admin',
                userType: 'admin',
                loginTime: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                isValid: true,
                sessionId: 'test_' + Date.now()
            };
            
            localStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));
            addLog('✅ تم إنشاء جلسة تجريبية بنجاح', 'success');
            
            // إرسال حدث تحديث الجلسة
            window.dispatchEvent(new CustomEvent('sessionUpdated', { 
                detail: sessionData 
            }));
            
            setTimeout(checkSession, 500);
        }

        // مسح الجلسة
        function clearSession() {
            addLog('🗑️ مسح الجلسة...', 'info');
            localStorage.removeItem(SESSION_KEY);
            addLog('✅ تم مسح الجلسة', 'success');
            setTimeout(checkSession, 500);
        }

        // اختبار النظام المحسن
        function testFixedSystem() {
            addLog('🧪 بدء اختبار النظام المحسن...', 'info');
            
            const statusDiv = document.getElementById('systemTestStatus');
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '🔄 جاري اختبار النظام المحسن...';
            
            // اختبار وجود الملفات الجديدة
            const newFiles = [
                'src/utils/auth-system-fixed.js',
                'src/utils/page-guard-fixed.js',
                'src/auth/login-fixed.html'
            ];
            
            let results = '';
            newFiles.forEach(file => {
                results += `📁 ${file}: متوقع وجوده<br>`;
                addLog(`📁 فحص ملف: ${file}`, 'info');
            });
            
            // اختبار localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                results += '✅ localStorage يعمل بشكل صحيح<br>';
                addLog('✅ localStorage يعمل بشكل صحيح', 'success');
            } catch (error) {
                results += '❌ مشكلة في localStorage<br>';
                addLog('❌ مشكلة في localStorage', 'error');
            }
            
            // اختبار الجلسة
            const session = localStorage.getItem(SESSION_KEY);
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    if (sessionData.isValid) {
                        results += '✅ الجلسة صالحة<br>';
                        addLog('✅ الجلسة صالحة', 'success');
                    } else {
                        results += '⚠️ الجلسة غير صالحة<br>';
                        addLog('⚠️ الجلسة غير صالحة', 'warning');
                    }
                } catch (error) {
                    results += '❌ خطأ في قراءة الجلسة<br>';
                    addLog('❌ خطأ في قراءة الجلسة', 'error');
                }
            } else {
                results += 'ℹ️ لا توجد جلسة<br>';
                addLog('ℹ️ لا توجد جلسة', 'info');
            }
            
            statusDiv.className = 'status success';
            statusDiv.innerHTML = results;
            addLog('✅ اكتمل اختبار النظام المحسن', 'success');
        }

        // اختبار نظام الحماية
        function testProtectionSystem() {
            addLog('🛡️ اختبار نظام الحماية...', 'info');
            
            const statusDiv = document.getElementById('systemTestStatus');
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '🔍 جاري اختبار نظام الحماية...';
            
            let results = '';
            
            // فحص الجلسة
            const session = localStorage.getItem(SESSION_KEY);
            if (session) {
                results += '✅ الجلسة موجودة - الحماية ستسمح بالوصول<br>';
                addLog('✅ الجلسة موجودة - الحماية ستسمح بالوصول', 'success');
            } else {
                results += '⚠️ لا توجد جلسة - الحماية ستمنع الوصول<br>';
                addLog('⚠️ لا توجد جلسة - الحماية ستمنع الوصول', 'warning');
            }
            
            // فحص الصفحة الحالية
            const currentPath = window.location.pathname;
            const excludedPages = ['test-fixed-login.html', 'diagnostic-tool.html', 'test-suite.html'];
            const isExcluded = excludedPages.some(page => currentPath.includes(page));
            
            if (isExcluded) {
                results += '✅ الصفحة الحالية مستثناة من الحماية<br>';
                addLog('✅ الصفحة الحالية مستثناة من الحماية', 'success');
            } else {
                results += '⚠️ الصفحة الحالية محمية<br>';
                addLog('⚠️ الصفحة الحالية محمية', 'warning');
            }
            
            statusDiv.className = 'status success';
            statusDiv.innerHTML = results;
            addLog('✅ اكتمل اختبار نظام الحماية', 'success');
        }

        // محاكاة تدفق تسجيل الدخول
        function simulateLoginFlow() {
            addLog('🎭 بدء محاكاة تدفق تسجيل الدخول...', 'info');
            
            const statusDiv = document.getElementById('systemTestStatus');
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '🔄 جاري محاكاة تدفق تسجيل الدخول...';
            
            // الخطوة 1: مسح الجلسة
            addLog('1️⃣ مسح الجلسة الحالية...', 'info');
            localStorage.removeItem(SESSION_KEY);
            
            setTimeout(() => {
                // الخطوة 2: إنشاء جلسة جديدة
                addLog('2️⃣ إنشاء جلسة جديدة...', 'info');
                createTestSession();
                
                setTimeout(() => {
                    // الخطوة 3: اختبار الوصول
                    addLog('3️⃣ اختبار الوصول للصفحة المحمية...', 'info');
                    
                    const session = JSON.parse(localStorage.getItem(SESSION_KEY));
                    if (session && session.isValid && session.userType === 'admin') {
                        statusDiv.className = 'status success';
                        statusDiv.innerHTML = '✅ تدفق تسجيل الدخول يعمل بشكل صحيح!';
                        addLog('✅ تدفق تسجيل الدخول يعمل بشكل صحيح!', 'success');
                    } else {
                        statusDiv.className = 'status error';
                        statusDiv.innerHTML = '❌ مشكلة في تدفق تسجيل الدخول!';
                        addLog('❌ مشكلة في تدفق تسجيل الدخول!', 'error');
                    }
                }, 1000);
            }, 1000);
        }

        // مسح السجل
        function clearLog() {
            document.getElementById('testLog').innerHTML = '<div class="log-entry info">🧪 تم مسح السجل...</div>';
            testCounter = 0;
        }

        // تهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 تم تحميل صفحة اختبار النظام المحسن', 'success');
            checkSession();
        });
    </script>
</body>
</html>
