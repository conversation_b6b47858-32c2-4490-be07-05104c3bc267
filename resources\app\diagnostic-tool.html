<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 أداة التشخيص الشاملة - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 2rem;
        }

        .control-panel {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-container {
            display: none;
            margin-top: 2rem;
        }

        .results-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .overall-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .stats {
            display: flex;
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 0.5rem;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .result-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            padding: 1rem;
            font-weight: 600;
            color: white;
        }

        .card-header.success { background: #27ae60; }
        .card-header.warning { background: #f39c12; }
        .card-header.error { background: #e74c3c; }
        .card-header.info { background: #3498db; }

        .card-body {
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .test-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }

        .test-item:last-child {
            border-bottom: none;
        }

        .test-name {
            font-weight: 500;
        }

        .test-status {
            padding: 0.2rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .test-status.success {
            background: #d4edda;
            color: #155724;
        }

        .test-status.warning {
            background: #fff3cd;
            color: #856404;
        }

        .test-status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 2rem;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #27ae60);
            width: 0%;
            transition: width 0.3s ease;
        }

        .quick-actions {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .quick-actions h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .action-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #3498db;
        }

        .support-info {
            margin-top: 2rem;
            padding: 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            text-align: center;
        }

        .support-contacts {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1rem;
        }

        .support-btn {
            padding: 0.8rem 1.5rem;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .support-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .control-panel {
                flex-direction: column;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
            
            .support-contacts {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-stethoscope"></i> أداة التشخيص الشاملة</h1>
            <p>فحص وتشخيص جميع مكونات نظام إدارة التراخيص</p>
        </div>

        <div class="main-content">
            <div class="control-panel">
                <button class="btn btn-primary" onclick="runFullDiagnostics()">
                    <i class="fas fa-play"></i>
                    تشغيل التشخيص الشامل
                </button>
                <button class="btn btn-success" onclick="runQuickCheck()">
                    <i class="fas fa-bolt"></i>
                    فحص سريع
                </button>
                <button class="btn btn-warning" onclick="testLoginSystem()">
                    <i class="fas fa-sign-in-alt"></i>
                    اختبار تسجيل الدخول
                </button>
                <button class="btn btn-danger" onclick="clearAllData()">
                    <i class="fas fa-trash"></i>
                    مسح البيانات
                </button>
            </div>

            <div id="loadingContainer" class="loading" style="display: none;">
                <div class="spinner"></div>
                <h3>جاري إجراء التشخيص...</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="currentTest">بدء التشخيص...</p>
            </div>

            <div id="resultsContainer" class="results-container">
                <div class="results-header">
                    <div class="overall-status" id="overallStatus">
                        <span id="statusEmoji">⚪</span>
                        <span id="statusText">غير محدد</span>
                    </div>
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-number" id="successCount">0</div>
                            <div class="stat-label">نجح</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="warningCount">0</div>
                            <div class="stat-label">تحذير</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="errorCount">0</div>
                            <div class="stat-label">خطأ</div>
                        </div>
                    </div>
                </div>

                <div class="results-grid" id="resultsGrid">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>

            <div class="quick-actions">
                <h3><i class="fas fa-tools"></i> إجراءات سريعة</h3>
                <div class="action-grid">
                    <div class="action-card" onclick="openLoginPage()">
                        <div class="action-icon"><i class="fas fa-sign-in-alt"></i></div>
                        <h4>تسجيل الدخول</h4>
                        <p>فتح صفحة تسجيل الدخول</p>
                    </div>
                    <div class="action-card" onclick="openActivationPage()">
                        <div class="action-icon"><i class="fas fa-key"></i></div>
                        <h4>طلب التفعيل</h4>
                        <p>فتح صفحة طلب التفعيل</p>
                    </div>
                    <div class="action-card" onclick="checkConsole()">
                        <div class="action-icon"><i class="fas fa-terminal"></i></div>
                        <h4>فحص Console</h4>
                        <p>عرض رسائل Console</p>
                    </div>
                    <div class="action-card" onclick="exportResults()">
                        <div class="action-icon"><i class="fas fa-download"></i></div>
                        <h4>تصدير النتائج</h4>
                        <p>حفظ نتائج التشخيص</p>
                    </div>
                </div>
            </div>

            <div class="support-info">
                <h3><i class="fas fa-headset"></i> الدعم الفني</h3>
                <p>إذا واجهت أي مشاكل، لا تتردد في التواصل معنا</p>
                <div class="support-contacts">
                    <a href="tel:0696924176" class="support-btn">
                        <i class="fas fa-phone"></i>
                        0696924176
                    </a>
                    <a href="https://wa.me/213696924176" target="_blank" class="support-btn">
                        <i class="fab fa-whatsapp"></i>
                        واتساب
                    </a>
                    <a href="mailto:<EMAIL>" class="support-btn">
                        <i class="fas fa-envelope"></i>
                        البريد الإلكتروني
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل نظام التشخيص -->
    <script src="src/utils/system-diagnostics.js"></script>
    <script src="diagnostic-tool.js"></script>
</body>
</html>
