// لوحة التحكم المتقدمة - JavaScript
// Advanced Dashboard JavaScript

class AdvancedDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.currentRequestId = null;
        this.currentLicenseId = null;
        this.charts = {};
        this.notifications = [];
        this.filters = {
            requests: { search: '', status: '', licenseType: '' },
            licenses: { search: '', status: '', licenseType: '' },
            logs: { search: '', type: '' }
        };
        this.pagination = {
            requests: { page: 1, limit: 10 },
            licenses: { page: 1, limit: 10 }
        };
    }

    // تهيئة لوحة التحكم
    initialize() {
        console.log('🚀 تهيئة لوحة التحكم المتقدمة...');
        
        try {
            // إعداد التنقل
            this.setupNavigation();
            
            // إعداد معالجات الأحداث
            this.setupEventHandlers();
            
            // تحميل البيانات الأولية
            this.loadInitialData();
            
            // إعداد التحديث التلقائي
            this.setupAutoRefresh();
            
            console.log('✅ تم تهيئة لوحة التحكم بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة لوحة التحكم:', error);
        }
    }

    // إعداد التنقل
    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.dataset.section;
                if (section) {
                    this.showSection(section);
                }
            });
        });
    }

    // إظهار قسم معين
    showSection(sectionName) {
        // إخفاء جميع الأقسام
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // إزالة الفئة النشطة من جميع عناصر التنقل
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // إظهار القسم المطلوب
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }
        
        // تفعيل عنصر التنقل
        const navItem = document.querySelector(`[data-section="${sectionName}"]`);
        if (navItem) {
            navItem.classList.add('active');
        }
        
        this.currentSection = sectionName;
        
        // تحميل بيانات القسم
        this.loadSectionData(sectionName);
    }

    // تحميل بيانات القسم
    loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'activation-requests':
                this.loadActivationRequests();
                break;
            case 'license-management':
                this.loadLicenses();
                break;
            case 'customers':
                this.loadCustomers();
                break;
            case 'statistics':
                this.loadStatistics();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'logs':
                this.loadLogs();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }

    // إعداد معالجات الأحداث
    setupEventHandlers() {
        // معالج النقر خارج القوائم المنسدلة
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-menu')) {
                document.getElementById('userDropdown').style.display = 'none';
            }
            if (!e.target.closest('.notification-bell')) {
                document.getElementById('notificationsPanel').style.display = 'none';
            }
        });

        // معالج تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // معالجات البحث والفلترة
        this.setupFilterHandlers();
    }

    // إعداد معالجات الفلترة
    setupFilterHandlers() {
        // فلترة طلبات التفعيل
        ['requestSearch', 'statusFilter', 'licenseTypeFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', () => this.filterRequests());
                element.addEventListener('change', () => this.filterRequests());
            }
        });

        // فلترة التراخيص
        ['licenseSearch', 'licenseStatusFilter', 'licenseTypeFilterLicenses'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', () => this.filterLicenses());
                element.addEventListener('change', () => this.filterLicenses());
            }
        });

        // فلترة السجلات
        ['logSearch', 'logTypeFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', () => this.filterLogs());
                element.addEventListener('change', () => this.filterLogs());
            }
        });
    }

    // تحميل البيانات الأولية
    loadInitialData() {
        this.updateStatisticsCards();
        this.loadDashboardData();
        this.updateNotifications();
    }

    // تحديث بطاقات الإحصائيات
    updateStatisticsCards() {
        if (!window.advancedDataManager) return;

        const stats = advancedDataManager.getStatistics();
        
        // تحديث البطاقات
        this.updateElement('totalRequests', stats.totalRequests || 0);
        this.updateElement('activeLicenses', stats.activeLicenses || 0);
        this.updateElement('totalCustomers', stats.totalLicenses || 0);
        
        // تحديث الشارات
        this.updateElement('requestsBadge', stats.pendingRequests || 0);
        this.updateElement('notificationBadge', this.notifications.length);
    }

    // تحميل بيانات الرئيسية
    loadDashboardData() {
        this.updateStatisticsCards();
        this.loadRecentActivities();
        this.createDashboardCharts();
    }

    // تحميل الأنشطة الأخيرة
    loadRecentActivities() {
        if (!window.advancedDataManager) return;

        const logs = advancedDataManager.getLogs(10);
        const container = document.getElementById('recentActivities');
        
        if (!container) return;

        container.innerHTML = '';
        
        if (logs.length === 0) {
            container.innerHTML = '<p class="no-data">لا توجد أنشطة حديثة</p>';
            return;
        }

        logs.forEach(log => {
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            activityItem.innerHTML = `
                <div class="activity-icon ${log.type}">
                    <i class="fas fa-${this.getLogIcon(log.type)}"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-message">${log.message}</p>
                    <span class="activity-time">${this.formatDate(log.timestamp)}</span>
                </div>
            `;
            container.appendChild(activityItem);
        });
    }

    // إنشاء الرسوم البيانية للرئيسية
    createDashboardCharts() {
        this.createRequestsChart();
        this.createLicensesChart();
    }

    // إنشاء رسم بياني للطلبات
    createRequestsChart() {
        const canvas = document.getElementById('requestsChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        if (this.charts.requests) {
            this.charts.requests.destroy();
        }

        const stats = advancedDataManager.getStatistics();
        
        this.charts.requests = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['في الانتظار', 'موافق عليه', 'مرفوض'],
                datasets: [{
                    data: [
                        stats.pendingRequests || 0,
                        stats.approvedRequests || 0,
                        stats.rejectedRequests || 0
                    ],
                    backgroundColor: ['#f39c12', '#27ae60', '#e74c3c'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // إنشاء رسم بياني للتراخيص
    createLicensesChart() {
        const canvas = document.getElementById('licensesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        if (this.charts.licenses) {
            this.charts.licenses.destroy();
        }

        const stats = advancedDataManager.getStatistics();
        
        this.charts.licenses = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['نشط', 'منتهي الصلاحية', 'معطل'],
                datasets: [{
                    data: [
                        stats.activeLicenses || 0,
                        stats.expiredLicenses || 0,
                        stats.deactivatedLicenses || 0
                    ],
                    backgroundColor: ['#27ae60', '#f39c12', '#e74c3c'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // تحميل طلبات التفعيل
    loadActivationRequests() {
        if (!window.advancedDataManager) return;

        const requests = advancedDataManager.getActivationRequests();
        this.displayRequests(requests);
    }

    // عرض طلبات التفعيل
    displayRequests(requests) {
        const tbody = document.getElementById('requestsTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (requests.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="no-data">لا توجد طلبات تفعيل</td></tr>';
            return;
        }

        requests.forEach(request => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <input type="checkbox" value="${request.id}" class="request-checkbox">
                </td>
                <td>${request.customerName}</td>
                <td>${request.phone}</td>
                <td>${request.state}</td>
                <td>
                    <span class="badge ${request.licenseType === 'lifetime' ? 'success' : 'warning'}">
                        ${request.licenseType === 'lifetime' ? 'مدى الحياة' : 'تجريبي'}
                    </span>
                </td>
                <td>${this.formatDate(request.requestDate)}</td>
                <td>
                    <span class="status-badge ${request.status}">
                        ${this.getStatusText(request.status)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-small btn-info" onclick="dashboard.viewRequestDetails('${request.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${request.status === 'pending' ? `
                            <button class="btn-small btn-success" onclick="dashboard.approveRequest('${request.id}')">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn-small btn-danger" onclick="dashboard.rejectRequest('${request.id}')">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // تحميل التراخيص
    loadLicenses() {
        if (!window.advancedDataManager) return;

        const licenses = advancedDataManager.getLicenses();
        this.displayLicenses(licenses);
    }

    // عرض التراخيص
    displayLicenses(licenses) {
        const tbody = document.getElementById('licensesTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (licenses.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="no-data">لا توجد تراخيص</td></tr>';
            return;
        }

        licenses.forEach(license => {
            const row = document.createElement('tr');
            const isExpired = advancedDataManager.isLicenseExpired(license);
            
            row.innerHTML = `
                <td>
                    <input type="checkbox" value="${license.id}" class="license-checkbox">
                </td>
                <td>
                    <code class="license-key">${license.licenseKey}</code>
                </td>
                <td>${license.customerName}</td>
                <td>${license.phone}</td>
                <td>
                    <span class="badge ${license.licenseType === 'lifetime' ? 'success' : 'warning'}">
                        ${license.licenseType === 'lifetime' ? 'مدى الحياة' : 'تجريبي'}
                    </span>
                </td>
                <td>${this.formatDate(license.createdDate)}</td>
                <td>${license.expiryDate ? this.formatDate(license.expiryDate) : 'مدى الحياة'}</td>
                <td>
                    <span class="status-badge ${isExpired ? 'expired' : license.status}">
                        ${isExpired ? 'منتهي الصلاحية' : this.getLicenseStatusText(license.status)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-small btn-info" onclick="dashboard.viewLicenseDetails('${license.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${license.status === 'active' ? `
                            <button class="btn-small btn-warning" onclick="dashboard.deactivateLicense('${license.id}')">
                                <i class="fas fa-ban"></i>
                            </button>
                        ` : ''}
                        <button class="btn-small btn-primary" onclick="dashboard.copyLicenseKey('${license.licenseKey}')">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // الموافقة على طلب تفعيل
    async approveRequest(requestId, licenseType = 'trial') {
        try {
            const result = advancedDataManager.approveActivationRequest(requestId, licenseType);
            
            this.showNotification('تم الموافقة على الطلب وإنشاء الترخيص بنجاح', 'success');
            this.loadActivationRequests();
            this.updateStatisticsCards();
            
            // إظهار تفاصيل الترخيص الجديد
            this.showLicenseCreatedModal(result.license);
            
        } catch (error) {
            console.error('خطأ في الموافقة على الطلب:', error);
            this.showNotification('حدث خطأ أثناء الموافقة على الطلب', 'error');
        }
    }

    // رفض طلب تفعيل
    async rejectRequest(requestId, reason = '') {
        if (!reason) {
            reason = prompt('يرجى إدخال سبب الرفض:');
            if (!reason) return;
        }

        try {
            advancedDataManager.rejectActivationRequest(requestId, reason);
            
            this.showNotification('تم رفض الطلب', 'warning');
            this.loadActivationRequests();
            this.updateStatisticsCards();
            
        } catch (error) {
            console.error('خطأ في رفض الطلب:', error);
            this.showNotification('حدث خطأ أثناء رفض الطلب', 'error');
        }
    }

    // إنشاء ترخيص جديد
    async createNewLicense() {
        const form = document.getElementById('createLicenseForm');
        const formData = new FormData(form);
        
        const licenseData = {
            customerName: document.getElementById('newLicenseCustomerName').value,
            phone: document.getElementById('newLicensePhone').value,
            state: document.getElementById('newLicenseState').value,
            licenseType: document.getElementById('newLicenseType').value
        };

        try {
            const license = advancedDataManager.createManualLicense(licenseData);
            
            this.showNotification('تم إنشاء الترخيص بنجاح', 'success');
            this.closeModal('createLicenseModal');
            this.loadLicenses();
            this.updateStatisticsCards();
            
            // إظهار تفاصيل الترخيص الجديد
            this.showLicenseCreatedModal(license);
            
        } catch (error) {
            console.error('خطأ في إنشاء الترخيص:', error);
            this.showNotification('حدث خطأ أثناء إنشاء الترخيص', 'error');
        }
    }

    // تعطيل ترخيص
    async deactivateLicense(licenseId, reason = '') {
        if (!reason) {
            reason = prompt('يرجى إدخال سبب التعطيل:');
            if (!reason) return;
        }

        try {
            advancedDataManager.deactivateLicense(licenseId, reason);
            
            this.showNotification('تم تعطيل الترخيص', 'warning');
            this.loadLicenses();
            this.updateStatisticsCards();
            
        } catch (error) {
            console.error('خطأ في تعطيل الترخيص:', error);
            this.showNotification('حدث خطأ أثناء تعطيل الترخيص', 'error');
        }
    }

    // نسخ مفتاح الترخيص
    copyLicenseKey(licenseKey) {
        navigator.clipboard.writeText(licenseKey).then(() => {
            this.showNotification('تم نسخ مفتاح الترخيص', 'success');
        }).catch(() => {
            // طريقة بديلة للنسخ
            const textArea = document.createElement('textarea');
            textArea.value = licenseKey;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('تم نسخ مفتاح الترخيص', 'success');
        });
    }

    // فلترة الطلبات
    filterRequests() {
        const search = document.getElementById('requestSearch')?.value.toLowerCase() || '';
        const status = document.getElementById('statusFilter')?.value || '';
        const licenseType = document.getElementById('licenseTypeFilter')?.value || '';

        const requests = advancedDataManager.getActivationRequests();
        const filtered = requests.filter(request => {
            const matchesSearch = !search || 
                request.customerName.toLowerCase().includes(search) ||
                request.phone.includes(search);
            const matchesStatus = !status || request.status === status;
            const matchesType = !licenseType || request.licenseType === licenseType;
            
            return matchesSearch && matchesStatus && matchesType;
        });

        this.displayRequests(filtered);
    }

    // فلترة التراخيص
    filterLicenses() {
        const search = document.getElementById('licenseSearch')?.value.toLowerCase() || '';
        const status = document.getElementById('licenseStatusFilter')?.value || '';
        const licenseType = document.getElementById('licenseTypeFilterLicenses')?.value || '';

        const licenses = advancedDataManager.getLicenses();
        const filtered = licenses.filter(license => {
            const matchesSearch = !search || 
                license.customerName.toLowerCase().includes(search) ||
                license.licenseKey.toLowerCase().includes(search) ||
                license.phone.includes(search);
            
            let matchesStatus = true;
            if (status) {
                if (status === 'expired') {
                    matchesStatus = advancedDataManager.isLicenseExpired(license);
                } else {
                    matchesStatus = license.status === status;
                }
            }
            
            const matchesType = !licenseType || license.licenseType === licenseType;
            
            return matchesSearch && matchesStatus && matchesType;
        });

        this.displayLicenses(filtered);
    }

    // === دوال مساعدة ===

    // تحديث عنصر
    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    // تنسيق التاريخ
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // الحصول على نص الحالة
    getStatusText(status) {
        const statusMap = {
            pending: 'في الانتظار',
            approved: 'موافق عليه',
            rejected: 'مرفوض'
        };
        return statusMap[status] || status;
    }

    // الحصول على نص حالة الترخيص
    getLicenseStatusText(status) {
        const statusMap = {
            active: 'نشط',
            deactivated: 'معطل',
            expired: 'منتهي الصلاحية'
        };
        return statusMap[status] || status;
    }

    // الحصول على أيقونة السجل
    getLogIcon(type) {
        const iconMap = {
            info: 'info-circle',
            success: 'check-circle',
            warning: 'exclamation-triangle',
            error: 'times-circle'
        };
        return iconMap[type] || 'circle';
    }

    // إظهار إشعار
    showNotification(message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-${this.getLogIcon(type)}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.remove()">&times;</button>
        `;

        // إضافة الإشعار للصفحة
        document.body.appendChild(notification);

        // إزالة الإشعار بعد 5 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    // إعداد التحديث التلقائي
    setupAutoRefresh() {
        setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.updateStatisticsCards();
                this.loadRecentActivities();
            }
        }, 30000); // كل 30 ثانية
    }

    // معالجة تغيير حجم النافذة
    handleResize() {
        // إعادة رسم الرسوم البيانية
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }

    // === وظائف إضافية ===

    // تحميل العملاء
    loadCustomers() {
        if (!window.advancedDataManager) return;

        const licenses = advancedDataManager.getLicenses();
        const customers = this.extractCustomersFromLicenses(licenses);
        this.displayCustomers(customers);
    }

    // استخراج العملاء من التراخيص
    extractCustomersFromLicenses(licenses) {
        const customersMap = new Map();

        licenses.forEach(license => {
            const key = `${license.customerName}-${license.phone}`;
            if (!customersMap.has(key)) {
                customersMap.set(key, {
                    name: license.customerName,
                    phone: license.phone,
                    state: license.state,
                    licenses: [],
                    totalLicenses: 0,
                    activeLicenses: 0,
                    firstLicense: license.createdDate
                });
            }

            const customer = customersMap.get(key);
            customer.licenses.push(license);
            customer.totalLicenses++;

            if (license.status === 'active' && !advancedDataManager.isLicenseExpired(license)) {
                customer.activeLicenses++;
            }
        });

        return Array.from(customersMap.values());
    }

    // عرض العملاء
    displayCustomers(customers) {
        const container = document.getElementById('customersGrid');
        if (!container) return;

        // تحديث إحصائيات العملاء
        this.updateElement('totalCustomersCount', customers.length);
        this.updateElement('activeCustomersCount', customers.filter(c => c.activeLicenses > 0).length);

        const thisMonth = new Date();
        thisMonth.setDate(1);
        const newThisMonth = customers.filter(c => new Date(c.firstLicense) >= thisMonth).length;
        this.updateElement('newCustomersThisMonth', newThisMonth);

        container.innerHTML = '';

        if (customers.length === 0) {
            container.innerHTML = '<p class="no-data">لا يوجد عملاء</p>';
            return;
        }

        customers.forEach(customer => {
            const customerCard = document.createElement('div');
            customerCard.className = 'customer-card';
            customerCard.innerHTML = `
                <div class="customer-header">
                    <div class="customer-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="customer-info">
                        <h4>${customer.name}</h4>
                        <p>${customer.phone}</p>
                        <span class="customer-state">${customer.state}</span>
                    </div>
                </div>
                <div class="customer-stats">
                    <div class="stat">
                        <span class="stat-value">${customer.totalLicenses}</span>
                        <span class="stat-label">إجمالي التراخيص</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">${customer.activeLicenses}</span>
                        <span class="stat-label">التراخيص النشطة</span>
                    </div>
                </div>
                <div class="customer-actions">
                    <button class="btn-small btn-primary" onclick="dashboard.viewCustomerDetails('${customer.phone}')">
                        <i class="fas fa-eye"></i> التفاصيل
                    </button>
                </div>
            `;
            container.appendChild(customerCard);
        });
    }

    // تحميل الإحصائيات المفصلة
    loadStatistics() {
        this.createDetailedCharts();
    }

    // إنشاء الرسوم البيانية المفصلة
    createDetailedCharts() {
        this.createTrendsChart();
        this.createStatesChart();
        this.createApprovalRateChart();
    }

    // رسم بياني للاتجاهات
    createTrendsChart() {
        const canvas = document.getElementById('trendsChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        if (this.charts.trends) {
            this.charts.trends.destroy();
        }

        // بيانات وهمية للاتجاهات (يمكن تطويرها لاحقاً)
        const data = this.generateTrendsData();

        this.charts.trends = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'طلبات التفعيل',
                    data: data.requests,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4
                }, {
                    label: 'التراخيص المنشأة',
                    data: data.licenses,
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // توليد بيانات الاتجاهات
    generateTrendsData() {
        const labels = [];
        const requests = [];
        const licenses = [];

        // آخر 7 أيام
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' }));

            // بيانات عشوائية للتوضيح
            requests.push(Math.floor(Math.random() * 10) + 1);
            licenses.push(Math.floor(Math.random() * 8) + 1);
        }

        return { labels, requests, licenses };
    }

    // رسم بياني للولايات
    createStatesChart() {
        const canvas = document.getElementById('statesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        if (this.charts.states) {
            this.charts.states.destroy();
        }

        const statesData = this.getStatesDistribution();

        this.charts.states = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: statesData.labels,
                datasets: [{
                    label: 'عدد العملاء',
                    data: statesData.values,
                    backgroundColor: [
                        '#3498db', '#27ae60', '#f39c12', '#e74c3c', '#9b59b6'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // الحصول على توزيع الولايات
    getStatesDistribution() {
        if (!window.advancedDataManager) {
            return { labels: [], values: [] };
        }

        const licenses = advancedDataManager.getLicenses();
        const statesMap = new Map();

        licenses.forEach(license => {
            const state = license.state || 'غير محدد';
            statesMap.set(state, (statesMap.get(state) || 0) + 1);
        });

        const sorted = Array.from(statesMap.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5); // أعلى 5 ولايات

        return {
            labels: sorted.map(item => item[0]),
            values: sorted.map(item => item[1])
        };
    }

    // رسم بياني لمعدل الموافقة
    createApprovalRateChart() {
        const canvas = document.getElementById('approvalRateChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        if (this.charts.approvalRate) {
            this.charts.approvalRate.destroy();
        }

        const stats = advancedDataManager.getStatistics();
        const total = stats.totalRequests || 1;
        const approvalRate = ((stats.approvedRequests || 0) / total * 100).toFixed(1);
        const rejectionRate = ((stats.rejectedRequests || 0) / total * 100).toFixed(1);
        const pendingRate = ((stats.pendingRequests || 0) / total * 100).toFixed(1);

        this.charts.approvalRate = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['معدل الموافقة', 'معدل الرفض', 'في الانتظار'],
                datasets: [{
                    data: [approvalRate, rejectionRate, pendingRate],
                    backgroundColor: ['#27ae60', '#e74c3c', '#f39c12'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // تحميل التقارير
    loadReports() {
        this.setupReportDates();
        this.generateReportPreview();
    }

    // إعداد تواريخ التقرير
    setupReportDates() {
        const today = new Date();
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

        const startDateInput = document.getElementById('reportStartDate');
        const endDateInput = document.getElementById('reportEndDate');

        if (startDateInput) {
            startDateInput.value = lastMonth.toISOString().split('T')[0];
        }
        if (endDateInput) {
            endDateInput.value = today.toISOString().split('T')[0];
        }
    }

    // إنشاء معاينة التقرير
    generateReportPreview() {
        const container = document.getElementById('reportPreview');
        if (!container) return;

        const stats = advancedDataManager.getStatistics();

        container.innerHTML = `
            <div class="report-summary">
                <h3>ملخص التقرير</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي الطلبات:</span>
                        <span class="summary-value">${stats.totalRequests || 0}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">الطلبات المعتمدة:</span>
                        <span class="summary-value">${stats.approvedRequests || 0}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">التراخيص النشطة:</span>
                        <span class="summary-value">${stats.activeLicenses || 0}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي العملاء:</span>
                        <span class="summary-value">${stats.totalLicenses || 0}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // تحميل السجلات
    loadLogs() {
        if (!window.advancedDataManager) return;

        const logs = advancedDataManager.getLogs(100);
        this.displayLogs(logs);
    }

    // عرض السجلات
    displayLogs(logs) {
        const container = document.getElementById('logsContainer');
        if (!container) return;

        container.innerHTML = '';

        if (logs.length === 0) {
            container.innerHTML = '<p class="no-data">لا توجد سجلات</p>';
            return;
        }

        logs.forEach(log => {
            const logItem = document.createElement('div');
            logItem.className = `log-item ${log.type}`;
            logItem.innerHTML = `
                <div class="log-icon">
                    <i class="fas fa-${this.getLogIcon(log.type)}"></i>
                </div>
                <div class="log-content">
                    <div class="log-message">${log.message}</div>
                    <div class="log-time">${this.formatDate(log.timestamp)}</div>
                </div>
            `;
            container.appendChild(logItem);
        });
    }

    // فلترة السجلات
    filterLogs() {
        const search = document.getElementById('logSearch')?.value.toLowerCase() || '';
        const type = document.getElementById('logTypeFilter')?.value || '';

        const logs = advancedDataManager.getLogs(100);
        const filtered = logs.filter(log => {
            const matchesSearch = !search || log.message.toLowerCase().includes(search);
            const matchesType = !type || log.type === type;

            return matchesSearch && matchesType;
        });

        this.displayLogs(filtered);
    }

    // تحميل الإعدادات
    loadSettings() {
        if (!window.advancedDataManager) return;

        const settings = advancedDataManager.loadSettings();

        // ملء النموذج بالإعدادات الحالية
        this.updateElement('licenseValidityDays', settings.licenseValidityDays);
        this.updateElement('maxDevicesPerLicense', settings.maxDevicesPerLicense);

        const autoApprovalCheckbox = document.getElementById('autoApproval');
        if (autoApprovalCheckbox) {
            autoApprovalCheckbox.checked = settings.autoApproval;
        }

        // معلومات الدعم
        this.updateInputValue('supportPhone', settings.supportInfo.phone);
        this.updateInputValue('supportWhatsapp', settings.supportInfo.whatsapp);
        this.updateInputValue('supportEmail', settings.supportInfo.email);

        // معلومات الشركة
        this.updateInputValue('companyName', settings.companyInfo.name);
        this.updateInputValue('companyNameEn', settings.companyInfo.nameEn);
        this.updateInputValue('companyAddress', settings.companyInfo.address);
        this.updateInputValue('companyWebsite', settings.companyInfo.website);
    }

    // تحديث قيمة حقل الإدخال
    updateInputValue(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.value = value || '';
        }
    }

    // حفظ الإعدادات
    saveSettings() {
        if (!window.advancedDataManager) return;

        const settings = {
            licenseValidityDays: parseInt(document.getElementById('licenseValidityDays')?.value) || 365,
            maxDevicesPerLicense: parseInt(document.getElementById('maxDevicesPerLicense')?.value) || 1,
            autoApproval: document.getElementById('autoApproval')?.checked || false,
            supportInfo: {
                phone: document.getElementById('supportPhone')?.value || '',
                whatsapp: document.getElementById('supportWhatsapp')?.value || '',
                email: document.getElementById('supportEmail')?.value || ''
            },
            companyInfo: {
                name: document.getElementById('companyName')?.value || '',
                nameEn: document.getElementById('companyNameEn')?.value || '',
                address: document.getElementById('companyAddress')?.value || '',
                website: document.getElementById('companyWebsite')?.value || ''
            }
        };

        try {
            advancedDataManager.saveSettings(settings);
            this.showNotification('تم حفظ الإعدادات بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            this.showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
        }
    }

    // تحديث الإشعارات
    updateNotifications() {
        if (!window.advancedDataManager) return;

        const stats = advancedDataManager.getStatistics();
        this.notifications = [];

        // إضافة إشعارات للطلبات المعلقة
        if (stats.pendingRequests > 0) {
            this.notifications.push({
                id: 'pending-requests',
                message: `لديك ${stats.pendingRequests} طلب تفعيل في الانتظار`,
                type: 'warning',
                timestamp: new Date().toISOString()
            });
        }

        // إضافة إشعارات للتراخيص المنتهية الصلاحية
        if (stats.expiredLicenses > 0) {
            this.notifications.push({
                id: 'expired-licenses',
                message: `${stats.expiredLicenses} ترخيص منتهي الصلاحية`,
                type: 'error',
                timestamp: new Date().toISOString()
            });
        }

        this.updateElement('notificationBadge', this.notifications.length);
    }
}

// إنشاء مثيل لوحة التحكم
const dashboard = new AdvancedDashboard();

// دوال عامة للواجهة
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('open');
}

function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
}

function toggleNotifications() {
    const panel = document.getElementById('notificationsPanel');
    panel.style.display = panel.style.display === 'block' ? 'none' : 'block';
}

function refreshDashboard() {
    dashboard.loadDashboardData();
    dashboard.showNotification('تم تحديث البيانات', 'success');
}

function refreshRequests() {
    dashboard.loadActivationRequests();
    dashboard.showNotification('تم تحديث طلبات التفعيل', 'success');
}

function refreshLicenses() {
    dashboard.loadLicenses();
    dashboard.showNotification('تم تحديث التراخيص', 'success');
}

function openCreateLicenseModal() {
    document.getElementById('createLicenseModal').style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('userSession');
        window.location.href = '../../auth/login-fixed.html';
    }
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل لوحة التحكم المتقدمة...');
    
    // انتظار تحميل نظام إدارة البيانات
    if (window.advancedDataManager) {
        dashboard.initialize();
    } else {
        // انتظار تحميل النظام
        setTimeout(() => {
            dashboard.initialize();
        }, 1000);
    }
});
