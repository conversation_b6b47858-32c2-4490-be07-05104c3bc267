// مجموعة الاختبارات الشاملة
// Comprehensive Test Suite

class TestSuite {
    constructor() {
        this.tests = [];
        this.results = [];
        this.isRunning = false;
        this.currentTestIndex = 0;
        this.categories = {
            auth: { name: 'المصادقة', tests: [], color: '#3498db' },
            license: { name: 'التراخيص', tests: [], color: '#27ae60' },
            security: { name: 'الأمان', tests: [], color: '#e74c3c' },
            ui: { name: 'واجهة المستخدم', tests: [], color: '#f39c12' },
            database: { name: 'قاعدة البيانات', tests: [], color: '#9b59b6' }
        };
        this.setupTests();
    }

    // إعداد جميع الاختبارات
    setupTests() {
        // اختبارات المصادقة
        this.addTest('auth', 'اختبار بيانات المدير', this.testAdminCredentials, true);
        this.addTest('auth', 'اختبار localStorage', this.testLocalStorage, true);
        this.addTest('auth', 'اختبار الجلسة الحالية', this.testCurrentSession, false);
        this.addTest('auth', 'اختبار دوال المصادقة', this.testAuthFunctions, false);

        // اختبارات التراخيص
        this.addTest('license', 'اختبار قراءة التراخيص', this.testLicenseReading, false);
        this.addTest('license', 'اختبار طلبات التفعيل', this.testActivationRequests, false);
        this.addTest('license', 'اختبار مدير التراخيص', this.testLicenseManager, false);
        this.addTest('license', 'اختبار التحقق من الجهاز', this.testDeviceVerification, false);

        // اختبارات الأمان
        this.addTest('security', 'اختبار نظام الحماية', this.testSecuritySystem, true);
        this.addTest('security', 'اختبار Auth Guard', this.testAuthGuard, false);
        this.addTest('security', 'اختبار Page Protector', this.testPageProtector, false);
        this.addTest('security', 'اختبار تسجيل الأخطاء', this.testErrorLogging, false);

        // اختبارات واجهة المستخدم
        this.addTest('ui', 'اختبار تحميل CSS', this.testCSSLoading, false);
        this.addTest('ui', 'اختبار Font Awesome', this.testFontAwesome, false);
        this.addTest('ui', 'اختبار العناصر التفاعلية', this.testInteractiveElements, false);
        this.addTest('ui', 'اختبار الاستجابة', this.testResponsiveness, false);

        // اختبارات قاعدة البيانات
        this.addTest('database', 'اختبار حفظ البيانات', this.testDataSaving, true);
        this.addTest('database', 'اختبار قراءة البيانات', this.testDataReading, true);
        this.addTest('database', 'اختبار تحديث البيانات', this.testDataUpdating, false);
        this.addTest('database', 'اختبار حذف البيانات', this.testDataDeletion, false);
    }

    // إضافة اختبار
    addTest(category, name, testFunction, critical = false) {
        const test = {
            id: this.generateId(),
            category: category,
            name: name,
            testFunction: testFunction,
            critical: critical,
            status: 'pending',
            result: null,
            error: null,
            duration: 0
        };

        this.tests.push(test);
        this.categories[category].tests.push(test);
    }

    // تشغيل جميع الاختبارات
    async runAllTests() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.results = [];
        this.currentTestIndex = 0;
        
        this.showProgress();
        this.logToConsole('info', '🧪 بدء تشغيل جميع الاختبارات...');
        
        try {
            for (let i = 0; i < this.tests.length; i++) {
                const test = this.tests[i];
                this.currentTestIndex = i;
                
                this.updateProgress(i, this.tests.length, `تشغيل: ${test.name}`);
                this.updateTestStatus(test.id, 'running');
                
                await this.runSingleTest(test);
                await this.sleep(100); // تأخير قصير بين الاختبارات
            }
            
            this.updateProgress(this.tests.length, this.tests.length, 'اكتملت جميع الاختبارات!');
            this.logToConsole('success', '✅ اكتملت جميع الاختبارات بنجاح');
            
        } catch (error) {
            this.logToConsole('error', `❌ خطأ في تشغيل الاختبارات: ${error.message}`);
        } finally {
            this.isRunning = false;
            this.displayResults();
        }
    }

    // تشغيل الاختبارات الحرجة فقط
    async runCriticalTests() {
        const criticalTests = this.tests.filter(test => test.critical);
        await this.runTestSubset(criticalTests, 'الاختبارات الحرجة');
    }

    // تشغيل اختبارات المصادقة
    async runAuthTests() {
        const authTests = this.tests.filter(test => test.category === 'auth');
        await this.runTestSubset(authTests, 'اختبارات المصادقة');
    }

    // تشغيل اختبارات التراخيص
    async runLicenseTests() {
        const licenseTests = this.tests.filter(test => test.category === 'license');
        await this.runTestSubset(licenseTests, 'اختبارات التراخيص');
    }

    // تشغيل مجموعة فرعية من الاختبارات
    async runTestSubset(tests, name) {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.results = [];
        this.currentTestIndex = 0;
        
        this.showProgress();
        this.logToConsole('info', `🧪 بدء تشغيل ${name}...`);
        
        try {
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                this.currentTestIndex = i;
                
                this.updateProgress(i, tests.length, `تشغيل: ${test.name}`);
                this.updateTestStatus(test.id, 'running');
                
                await this.runSingleTest(test);
                await this.sleep(100);
            }
            
            this.updateProgress(tests.length, tests.length, `اكتمل ${name}!`);
            this.logToConsole('success', `✅ اكتمل ${name} بنجاح`);
            
        } catch (error) {
            this.logToConsole('error', `❌ خطأ في ${name}: ${error.message}`);
        } finally {
            this.isRunning = false;
            this.displayResults();
        }
    }

    // تشغيل اختبار واحد
    async runSingleTest(test) {
        const startTime = performance.now();
        
        try {
            this.logToConsole('info', `🔍 تشغيل: ${test.name}`);
            
            const result = await test.testFunction.call(this);
            const endTime = performance.now();
            
            test.status = result.success ? 'passed' : 'failed';
            test.result = result;
            test.duration = Math.round(endTime - startTime);
            
            this.results.push(test);
            this.updateTestStatus(test.id, test.status, result);
            
            if (result.success) {
                this.logToConsole('success', `✅ ${test.name}: ${result.message || 'نجح'}`);
            } else {
                this.logToConsole('error', `❌ ${test.name}: ${result.message || 'فشل'}`);
            }
            
        } catch (error) {
            const endTime = performance.now();
            
            test.status = 'failed';
            test.error = error.message;
            test.duration = Math.round(endTime - startTime);
            
            this.results.push(test);
            this.updateTestStatus(test.id, 'failed', { message: error.message });
            
            this.logToConsole('error', `❌ ${test.name}: خطأ - ${error.message}`);
        }
    }

    // الاختبارات الفعلية
    async testAdminCredentials() {
        const result = window.testHelpers?.testAdminLogin('admin', 'admin123');
        return {
            success: result === true,
            message: result ? 'بيانات المدير صحيحة' : 'بيانات المدير غير صحيحة'
        };
    }

    async testLocalStorage() {
        const result = window.testHelpers?.testLocalStorage();
        return {
            success: result === true,
            message: result ? 'localStorage يعمل بشكل صحيح' : 'localStorage غير متوفر'
        };
    }

    async testCurrentSession() {
        const result = window.testHelpers?.testSession();
        return {
            success: result?.valid === true,
            message: result?.valid ? `جلسة صالحة: ${result.userType}` : result?.reason || 'جلسة غير صالحة'
        };
    }

    async testAuthFunctions() {
        const functions = ['handleAdminLogin', 'handleLicenseLogin'];
        const available = functions.filter(func => typeof window[func] === 'function');
        
        return {
            success: available.length > 0,
            message: `${available.length} من ${functions.length} دالة متاحة`
        };
    }

    async testLicenseReading() {
        const result = window.testHelpers?.testLicenses();
        return {
            success: result?.valid === true,
            message: result?.valid ? `${result.count} ترخيص محفوظ` : 'خطأ في قراءة التراخيص'
        };
    }

    async testActivationRequests() {
        const result = window.testHelpers?.testActivationRequests();
        return {
            success: result?.valid === true,
            message: result?.valid ? `${result.count} طلب تفعيل محفوظ` : 'خطأ في قراءة طلبات التفعيل'
        };
    }

    async testLicenseManager() {
        const available = typeof window.licenseManager === 'object';
        return {
            success: available,
            message: available ? 'مدير التراخيص متاح' : 'مدير التراخيص غير محمل'
        };
    }

    async testDeviceVerification() {
        const available = typeof window.deviceVerification === 'object';
        return {
            success: available,
            message: available ? 'نظام التحقق من الجهاز متاح' : 'نظام التحقق من الجهاز غير محمل'
        };
    }

    async testSecuritySystem() {
        const authGuard = typeof window.authGuard === 'object';
        const pageProtector = document.querySelector('script[src*="page-protector"]');
        
        return {
            success: authGuard || pageProtector,
            message: `Auth Guard: ${authGuard ? 'متاح' : 'غير متاح'}, Page Protector: ${pageProtector ? 'محمل' : 'غير محمل'}`
        };
    }

    async testAuthGuard() {
        const available = typeof window.authGuard === 'object';
        return {
            success: available,
            message: available ? 'Auth Guard متاح' : 'Auth Guard غير محمل'
        };
    }

    async testPageProtector() {
        const script = document.querySelector('script[src*="page-protector"]');
        return {
            success: !!script,
            message: script ? 'Page Protector محمل' : 'Page Protector غير محمل'
        };
    }

    async testErrorLogging() {
        const available = typeof window.errorLogger === 'object';
        return {
            success: available,
            message: available ? 'نظام تسجيل الأخطاء متاح' : 'نظام تسجيل الأخطاء غير محمل'
        };
    }

    async testCSSLoading() {
        const cssFiles = document.querySelectorAll('link[rel="stylesheet"]');
        return {
            success: cssFiles.length > 0,
            message: `${cssFiles.length} ملف CSS محمل`
        };
    }

    async testFontAwesome() {
        const fontAwesome = document.querySelector('link[href*="font-awesome"]');
        return {
            success: !!fontAwesome,
            message: fontAwesome ? 'Font Awesome محمل' : 'Font Awesome غير محمل'
        };
    }

    async testInteractiveElements() {
        const buttons = document.querySelectorAll('button');
        const forms = document.querySelectorAll('form');
        const inputs = document.querySelectorAll('input');
        
        const total = buttons.length + forms.length + inputs.length;
        return {
            success: total > 0,
            message: `${total} عنصر تفاعلي موجود`
        };
    }

    async testResponsiveness() {
        const viewport = document.querySelector('meta[name="viewport"]');
        return {
            success: !!viewport,
            message: viewport ? 'إعدادات الاستجابة موجودة' : 'إعدادات الاستجابة مفقودة'
        };
    }

    async testDataSaving() {
        try {
            const testData = { test: 'data', timestamp: Date.now() };
            localStorage.setItem('testSuite_test', JSON.stringify(testData));
            return {
                success: true,
                message: 'حفظ البيانات يعمل بشكل صحيح'
            };
        } catch (error) {
            return {
                success: false,
                message: `خطأ في حفظ البيانات: ${error.message}`
            };
        }
    }

    async testDataReading() {
        try {
            const data = localStorage.getItem('testSuite_test');
            const parsed = JSON.parse(data);
            return {
                success: !!parsed && parsed.test === 'data',
                message: parsed ? 'قراءة البيانات تعمل بشكل صحيح' : 'فشل في قراءة البيانات'
            };
        } catch (error) {
            return {
                success: false,
                message: `خطأ في قراءة البيانات: ${error.message}`
            };
        }
    }

    async testDataUpdating() {
        try {
            const data = JSON.parse(localStorage.getItem('testSuite_test') || '{}');
            data.updated = true;
            data.updateTime = Date.now();
            localStorage.setItem('testSuite_test', JSON.stringify(data));
            return {
                success: true,
                message: 'تحديث البيانات يعمل بشكل صحيح'
            };
        } catch (error) {
            return {
                success: false,
                message: `خطأ في تحديث البيانات: ${error.message}`
            };
        }
    }

    async testDataDeletion() {
        try {
            localStorage.removeItem('testSuite_test');
            const data = localStorage.getItem('testSuite_test');
            return {
                success: data === null,
                message: data === null ? 'حذف البيانات يعمل بشكل صحيح' : 'فشل في حذف البيانات'
            };
        } catch (error) {
            return {
                success: false,
                message: `خطأ في حذف البيانات: ${error.message}`
            };
        }
    }

    // دوال مساعدة
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showProgress() {
        document.getElementById('testProgress').style.display = 'block';
        document.getElementById('testResults').style.display = 'none';
    }

    updateProgress(current, total, message) {
        const percent = total > 0 ? (current / total) * 100 : 0;
        document.getElementById('progressFill').style.width = percent + '%';
        document.getElementById('currentTest').textContent = message;
        document.getElementById('testCounter').textContent = `${current} / ${total}`;
    }

    updateTestStatus(testId, status, result = null) {
        // سيتم تنفيذها في displayResults
    }

    displayResults() {
        document.getElementById('testProgress').style.display = 'none';
        document.getElementById('testResults').style.display = 'block';
        
        // تحديث الملخص
        const total = this.results.length;
        const passed = this.results.filter(r => r.status === 'passed').length;
        const failed = this.results.filter(r => r.status === 'failed').length;
        const skipped = total - passed - failed;
        
        document.getElementById('totalTests').textContent = total;
        document.getElementById('passedTests').textContent = passed;
        document.getElementById('failedTests').textContent = failed;
        document.getElementById('skippedTests').textContent = skipped;
        
        // عرض النتائج حسب الفئة
        this.displayCategoryResults();
    }

    displayCategoryResults() {
        const container = document.getElementById('testCategories');
        container.innerHTML = '';
        
        Object.keys(this.categories).forEach(categoryKey => {
            const category = this.categories[categoryKey];
            const categoryTests = this.results.filter(r => r.category === categoryKey);
            
            if (categoryTests.length === 0) return;
            
            const categoryCard = this.createCategoryCard(category, categoryTests);
            container.appendChild(categoryCard);
        });
    }

    createCategoryCard(category, tests) {
        const card = document.createElement('div');
        card.className = 'category-card';
        
        const passed = tests.filter(t => t.status === 'passed').length;
        const failed = tests.filter(t => t.status === 'failed').length;
        
        card.innerHTML = `
            <div class="category-header ${category.name.toLowerCase()}">
                <span>${category.name}</span>
                <span>${passed}/${tests.length} نجح</span>
            </div>
            <div class="category-body">
                ${tests.map(test => this.createTestItem(test)).join('')}
            </div>
        `;
        
        return card;
    }

    createTestItem(test) {
        const statusClass = test.status || 'pending';
        const statusText = {
            passed: 'نجح',
            failed: 'فشل',
            skipped: 'تخطي',
            running: 'جاري...',
            pending: 'انتظار'
        }[statusClass] || 'غير محدد';
        
        const message = test.result?.message || test.error || 'لا توجد تفاصيل';
        
        return `
            <div class="test-item" onclick="toggleTestDetails('${test.id}')">
                <div class="test-name">${test.name}</div>
                <div class="test-result ${statusClass}">${statusText}</div>
                <div class="test-details" id="details_${test.id}">
                    <strong>التفاصيل:</strong> ${message}<br>
                    <strong>المدة:</strong> ${test.duration}ms<br>
                    <strong>الفئة:</strong> ${this.categories[test.category].name}
                </div>
            </div>
        `;
    }

    logToConsole(type, message) {
        const console = document.getElementById('consoleOutput');
        const line = document.createElement('div');
        line.className = `console-line ${type}`;
        line.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        console.appendChild(line);
        console.scrollTop = console.scrollHeight;
    }

    clearResults() {
        this.results = [];
        document.getElementById('testResults').style.display = 'none';
        document.getElementById('testProgress').style.display = 'none';
        document.getElementById('consoleOutput').innerHTML = '<div class="console-line info">🧪 تم مسح النتائج</div>';
    }

    exportResults() {
        const exportData = {
            timestamp: new Date().toISOString(),
            summary: {
                total: this.results.length,
                passed: this.results.filter(r => r.status === 'passed').length,
                failed: this.results.filter(r => r.status === 'failed').length
            },
            results: this.results,
            systemInfo: {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language
            }
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `test-results-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// إنشاء مثيل واحد
const testSuite = new TestSuite();

// دوال عامة للواجهة
function runAllTests() {
    testSuite.runAllTests();
}

function runCriticalTests() {
    testSuite.runCriticalTests();
}

function runAuthTests() {
    testSuite.runAuthTests();
}

function runLicenseTests() {
    testSuite.runLicenseTests();
}

function clearResults() {
    testSuite.clearResults();
}

function exportTestResults() {
    if (testSuite.results.length === 0) {
        alert('لا توجد نتائج للتصدير. قم بتشغيل الاختبارات أولاً.');
        return;
    }
    testSuite.exportResults();
}

function toggleTestDetails(testId) {
    const details = document.getElementById(`details_${testId}`);
    const item = details.parentElement;
    
    if (item.classList.contains('expanded')) {
        item.classList.remove('expanded');
    } else {
        // إغلاق جميع التفاصيل الأخرى
        document.querySelectorAll('.test-item.expanded').forEach(el => {
            el.classList.remove('expanded');
        });
        item.classList.add('expanded');
    }
}

function openDiagnosticTool() {
    window.open('diagnostic-tool.html', '_blank');
}

function openLoginPage() {
    window.open('src/auth/new-login.html', '_blank');
}

function showConsole() {
    const console = document.getElementById('consoleOutput');
    console.style.display = console.style.display === 'none' ? 'block' : 'none';
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🧪 مجموعة الاختبارات جاهزة');
    testSuite.logToConsole('info', '🧪 مجموعة الاختبارات جاهزة للتشغيل');
});
