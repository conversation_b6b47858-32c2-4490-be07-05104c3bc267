// صفحة طلب التفعيل المحسنة - JavaScript
// Enhanced Activation Request JavaScript

class ActivationRequestForm {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.formData = {};
        this.deviceInfo = {};
        this.isSubmitting = false;
    }

    // تهيئة النموذج
    initialize() {
        console.log('🚀 تهيئة نموذج طلب التفعيل المحسن...');
        
        try {
            // جمع معلومات الجهاز
            this.collectDeviceInfo();
            
            // إعداد معالجات الأحداث
            this.setupEventHandlers();
            
            // تحديث شريط التقدم
            this.updateProgressBar();
            
            console.log('✅ تم تهيئة النموذج بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة النموذج:', error);
        }
    }

    // جمع معلومات الجهاز
    collectDeviceInfo() {
        // معلومات المتصفح
        const userAgent = navigator.userAgent;
        
        // نظام التشغيل
        let os = 'غير معروف';
        if (userAgent.includes('Windows')) os = 'Windows';
        else if (userAgent.includes('Mac')) os = 'macOS';
        else if (userAgent.includes('Linux')) os = 'Linux';
        else if (userAgent.includes('Android')) os = 'Android';
        else if (userAgent.includes('iOS')) os = 'iOS';

        // المتصفح
        let browser = 'غير معروف';
        if (userAgent.includes('Chrome')) browser = 'Google Chrome';
        else if (userAgent.includes('Firefox')) browser = 'Mozilla Firefox';
        else if (userAgent.includes('Safari')) browser = 'Safari';
        else if (userAgent.includes('Edge')) browser = 'Microsoft Edge';

        // معلومات الشاشة
        const screen = window.screen;
        const screenInfo = `${screen.width}x${screen.height}`;

        // معلومات الذاكرة (إذا كانت متوفرة)
        let memory = 'غير متوفر';
        if (navigator.deviceMemory) {
            memory = `${navigator.deviceMemory} GB`;
        }

        // معلومات المعالج (تقديرية)
        let processor = 'غير متوفر';
        if (navigator.hardwareConcurrency) {
            processor = `${navigator.hardwareConcurrency} cores`;
        }

        this.deviceInfo = {
            os: os,
            browser: browser,
            screen: screenInfo,
            memory: memory,
            processor: processor,
            userAgent: userAgent,
            language: navigator.language,
            platform: navigator.platform,
            timestamp: new Date().toISOString()
        };

        // تحديث واجهة المستخدم
        this.updateDeviceInfoDisplay();
    }

    // تحديث عرض معلومات الجهاز
    updateDeviceInfoDisplay() {
        document.getElementById('deviceOS').textContent = this.deviceInfo.os;
        document.getElementById('deviceProcessor').textContent = this.deviceInfo.processor;
        document.getElementById('deviceRAM').textContent = this.deviceInfo.memory;
        document.getElementById('deviceBrowser').textContent = this.deviceInfo.browser;
    }

    // إعداد معالجات الأحداث
    setupEventHandlers() {
        // معالج إرسال النموذج
        const form = document.getElementById('activationForm');
        form.addEventListener('submit', (e) => this.handleSubmit(e));

        // معالجات التحقق من صحة الحقول
        this.setupValidation();

        // معالج تغيير نوع الترخيص
        document.querySelectorAll('input[name="licenseType"]').forEach(radio => {
            radio.addEventListener('change', () => this.updateSummary());
        });

        // معالجات تحديث الملخص
        ['customerName', 'phone', 'state'].forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', () => this.updateSummary());
                field.addEventListener('change', () => this.updateSummary());
            }
        });
    }

    // إعداد التحقق من صحة الحقول
    setupValidation() {
        // التحقق من الاسم
        const nameField = document.getElementById('customerName');
        nameField.addEventListener('blur', () => this.validateName());
        nameField.addEventListener('input', () => this.clearError('customerName'));

        // التحقق من رقم الهاتف
        const phoneField = document.getElementById('phone');
        phoneField.addEventListener('blur', () => this.validatePhone());
        phoneField.addEventListener('input', () => {
            this.formatPhoneNumber();
            this.clearError('phone');
        });

        // التحقق من الولاية
        const stateField = document.getElementById('state');
        stateField.addEventListener('change', () => {
            this.validateState();
            this.clearError('state');
        });

        // التحقق من الموافقة على الشروط
        const agreeField = document.getElementById('agreeTerms');
        agreeField.addEventListener('change', () => this.validateAgreement());
    }

    // التحقق من صحة الاسم
    validateName() {
        const name = document.getElementById('customerName').value.trim();
        const nameGroup = document.getElementById('customerName').closest('.form-group');
        
        if (!name) {
            this.showError(nameGroup, 'يرجى إدخال الاسم الكامل');
            return false;
        }
        
        if (name.length < 3) {
            this.showError(nameGroup, 'الاسم قصير جداً');
            return false;
        }
        
        if (!/^[\u0600-\u06FF\s]+$/.test(name)) {
            this.showError(nameGroup, 'يرجى إدخال الاسم بالعربية فقط');
            return false;
        }
        
        this.clearError('customerName');
        return true;
    }

    // التحقق من صحة رقم الهاتف
    validatePhone() {
        const phone = document.getElementById('phone').value.trim();
        const phoneGroup = document.getElementById('phone').closest('.form-group');
        
        if (!phone) {
            this.showError(phoneGroup, 'يرجى إدخال رقم الهاتف');
            return false;
        }
        
        // تنسيق رقم الهاتف الجزائري
        const phoneRegex = /^(0[5-7]\d{8})$/;
        if (!phoneRegex.test(phone.replace(/\s/g, ''))) {
            this.showError(phoneGroup, 'رقم الهاتف غير صحيح (مثال: **********)');
            return false;
        }
        
        this.clearError('phone');
        return true;
    }

    // تنسيق رقم الهاتف
    formatPhoneNumber() {
        const phoneField = document.getElementById('phone');
        let value = phoneField.value.replace(/\D/g, '');
        
        if (value.length > 10) {
            value = value.substring(0, 10);
        }
        
        phoneField.value = value;
    }

    // التحقق من صحة الولاية
    validateState() {
        const state = document.getElementById('state').value;
        const stateGroup = document.getElementById('state').closest('.form-group');
        
        if (!state) {
            this.showError(stateGroup, 'يرجى اختيار الولاية');
            return false;
        }
        
        this.clearError('state');
        return true;
    }

    // التحقق من الموافقة على الشروط
    validateAgreement() {
        const agree = document.getElementById('agreeTerms').checked;
        const agreeGroup = document.getElementById('agreeTerms').closest('.form-group');
        
        if (!agree) {
            this.showError(agreeGroup, 'يجب الموافقة على الشروط والأحكام');
            return false;
        }
        
        this.clearError('agreeTerms');
        return true;
    }

    // إظهار خطأ
    showError(group, message) {
        group.classList.add('error');
        const errorElement = group.querySelector('.error-message');
        if (errorElement) {
            errorElement.textContent = message;
        }
    }

    // مسح الخطأ
    clearError(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
            const group = field.closest('.form-group');
            group.classList.remove('error');
        }
    }

    // الانتقال للخطوة التالية
    nextStep() {
        if (!this.validateCurrentStep()) {
            return;
        }
        
        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.showStep(this.currentStep);
            this.updateProgressBar();
            this.updateButtons();
            this.updateSummary();
        }
    }

    // الانتقال للخطوة السابقة
    previousStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.showStep(this.currentStep);
            this.updateProgressBar();
            this.updateButtons();
        }
    }

    // التحقق من صحة الخطوة الحالية
    validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                return this.validateName() && this.validatePhone() && this.validateState();
            case 2:
                const licenseType = document.querySelector('input[name="licenseType"]:checked');
                if (!licenseType) {
                    alert('يرجى اختيار نوع الترخيص');
                    return false;
                }
                return true;
            case 3:
                return this.validateAgreement();
            default:
                return true;
        }
    }

    // إظهار خطوة معينة
    showStep(stepNumber) {
        // إخفاء جميع الخطوات
        document.querySelectorAll('.form-step').forEach(step => {
            step.classList.remove('active');
        });
        
        // إظهار الخطوة المطلوبة
        const targetStep = document.querySelector(`[data-step="${stepNumber}"]`);
        if (targetStep) {
            targetStep.classList.add('active');
        }
    }

    // تحديث شريط التقدم
    updateProgressBar() {
        const steps = document.querySelectorAll('.step');
        const progressBar = document.getElementById('progressBar');
        
        steps.forEach((step, index) => {
            const stepNumber = index + 1;
            
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
                step.classList.remove('active');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
                step.classList.remove('completed');
            } else {
                step.classList.remove('active', 'completed');
            }
        });
        
        // تحديث شريط التقدم
        const progressPercentage = ((this.currentStep - 1) / (this.totalSteps - 1)) * 100;
        progressBar.style.setProperty('--progress', `${progressPercentage}%`);
        
        // تحديث CSS للشريط
        const style = document.createElement('style');
        style.textContent = `
            .progress-bar::after {
                width: ${progressPercentage}% !important;
            }
        `;
        document.head.appendChild(style);
    }

    // تحديث الأزرار
    updateButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const submitBtn = document.getElementById('submitBtn');
        
        // زر السابق
        prevBtn.style.display = this.currentStep > 1 ? 'flex' : 'none';
        
        // زر التالي
        nextBtn.style.display = this.currentStep < this.totalSteps ? 'flex' : 'none';
        
        // زر الإرسال
        submitBtn.style.display = this.currentStep === this.totalSteps ? 'flex' : 'none';
    }

    // تحديث الملخص
    updateSummary() {
        if (this.currentStep === 4) {
            const name = document.getElementById('customerName').value;
            const phone = document.getElementById('phone').value;
            const state = document.getElementById('state').value;
            const licenseType = document.querySelector('input[name="licenseType"]:checked');
            
            document.getElementById('summaryName').textContent = name || '-';
            document.getElementById('summaryPhone').textContent = phone || '-';
            document.getElementById('summaryState').textContent = state || '-';
            
            if (licenseType) {
                const licenseText = licenseType.value === 'trial' ? 'ترخيص تجريبي' : 'ترخيص مدى الحياة';
                document.getElementById('summaryLicenseType').textContent = licenseText;
            }
        }
    }

    // معالج إرسال النموذج
    async handleSubmit(e) {
        e.preventDefault();

        console.log('📝 بدء معالجة إرسال النموذج...');

        if (this.isSubmitting) {
            console.log('⏳ الطلب قيد المعالجة بالفعل...');
            return;
        }

        if (!this.validateCurrentStep()) {
            console.log('❌ فشل في التحقق من صحة البيانات');
            return;
        }

        this.isSubmitting = true;

        // تحديث واجهة المستخدم لإظهار حالة التحميل
        this.showLoadingState();

        try {
            console.log('📊 جمع بيانات النموذج...');
            // جمع بيانات النموذج
            this.collectFormData();

            console.log('📤 إرسال الطلب...');
            // إرسال الطلب
            await this.submitRequest();

            console.log('🎉 تم إرسال الطلب بنجاح');
            // إظهار رسالة النجاح
            this.showSuccessMessage();

        } catch (error) {
            console.error('❌ خطأ في إرسال الطلب:', error);

            // إظهار رسالة خطأ مفصلة
            this.showErrorMessage(error);

        } finally {
            this.isSubmitting = false;
            this.hideLoadingState();
        }
    }

    // إظهار حالة التحميل
    showLoadingState() {
        const submitBtn = document.getElementById('submitBtn');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                جاري الإرسال...
            `;
        }
    }

    // إخفاء حالة التحميل
    hideLoadingState() {
        const submitBtn = document.getElementById('submitBtn');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = `
                <i class="fas fa-paper-plane"></i>
                إرسال الطلب
            `;
        }
    }

    // إظهار رسالة خطأ
    showErrorMessage(error) {
        let errorMessage = 'حدث خطأ غير متوقع أثناء إرسال الطلب.';

        if (error.message) {
            errorMessage = error.message;
        }

        // إنشاء رسالة خطأ مفصلة
        const errorDialog = `
            ❌ فشل في إرسال الطلب

            السبب: ${errorMessage}

            الحلول المقترحة:
            1. تحقق من اتصال الإنترنت
            2. تأكد من صحة البيانات المدخلة
            3. حاول مرة أخرى بعد قليل
            4. اتصل بالدعم الفني: **********

            هل تريد المحاولة مرة أخرى؟
        `;

        if (confirm(errorDialog)) {
            // إعادة المحاولة
            setTimeout(() => {
                this.handleSubmit(new Event('submit'));
            }, 1000);
        }
    }

    // جمع بيانات النموذج
    collectFormData() {
        const licenseType = document.querySelector('input[name="licenseType"]:checked');
        
        this.formData = {
            customerName: document.getElementById('customerName').value.trim(),
            phone: document.getElementById('phone').value.trim(),
            state: document.getElementById('state').value,
            licenseType: licenseType ? licenseType.value : 'trial',
            deviceInfo: this.deviceInfo,
            requestDate: new Date().toISOString(),
            status: 'pending'
        };
    }

    // إرسال الطلب
    async submitRequest() {
        console.log('🚀 بدء إرسال طلب التفعيل...');
        console.log('📝 بيانات الطلب:', this.formData);

        // محاكاة تأخير الشبكة
        await this.sleep(1000);

        try {
            // التحقق من وجود نظام إدارة البيانات
            if (window.advancedDataManager && typeof advancedDataManager.addActivationRequest === 'function') {
                console.log('✅ نظام إدارة البيانات متوفر');

                // إضافة الطلب إلى نظام إدارة البيانات
                const request = advancedDataManager.addActivationRequest(this.formData);
                this.requestId = request.id;

                console.log('✅ تم إرسال الطلب بنجاح:', request);
                console.log('🆔 رقم الطلب:', this.requestId);

                // التحقق من حفظ الطلب
                const savedRequests = advancedDataManager.getActivationRequests();
                console.log('📊 إجمالي الطلبات المحفوظة:', savedRequests.length);

            } else {
                console.log('⚠️ نظام إدارة البيانات غير متوفر، استخدام الحفظ المحلي');

                // حفظ محلي كبديل
                this.requestId = this.generateRequestId();

                // إضافة معرف فريد ووقت الطلب
                const requestWithId = {
                    ...this.formData,
                    id: this.requestId,
                    requestDate: new Date().toISOString(),
                    status: 'pending'
                };

                // حفظ في localStorage
                const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                requests.push(requestWithId);
                localStorage.setItem('activationRequests', JSON.stringify(requests));

                console.log('✅ تم حفظ الطلب محلياً:', requestWithId);
                console.log('📊 إجمالي الطلبات المحفوظة محلياً:', requests.length);

                // محاولة تهيئة نظام إدارة البيانات إذا لم يكن متوفراً
                this.initializeDataManagerIfNeeded();
            }

        } catch (error) {
            console.error('❌ خطأ في إرسال الطلب:', error);

            // حفظ احتياطي في حالة الخطأ
            this.requestId = this.generateRequestId();
            const backupRequest = {
                ...this.formData,
                id: this.requestId,
                requestDate: new Date().toISOString(),
                status: 'pending',
                error: error.message
            };

            const backupRequests = JSON.parse(localStorage.getItem('backupRequests') || '[]');
            backupRequests.push(backupRequest);
            localStorage.setItem('backupRequests', JSON.stringify(backupRequests));

            console.log('💾 تم حفظ الطلب كنسخة احتياطية:', backupRequest);

            throw error; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
        }
    }

    // تهيئة نظام إدارة البيانات إذا لم يكن متوفراً
    initializeDataManagerIfNeeded() {
        try {
            if (!window.advancedDataManager && window.AdvancedDataManager) {
                console.log('🔧 محاولة تهيئة نظام إدارة البيانات...');
                window.advancedDataManager = new AdvancedDataManager();
                advancedDataManager.initialize();
                console.log('✅ تم تهيئة نظام إدارة البيانات بنجاح');
            }
        } catch (error) {
            console.error('❌ فشل في تهيئة نظام إدارة البيانات:', error);
        }
    }

    // إظهار رسالة النجاح
    showSuccessMessage() {
        // إخفاء جميع الخطوات
        document.querySelectorAll('.form-step').forEach(step => {
            step.style.display = 'none';
        });
        
        // إظهار رسالة النجاح
        const successStep = document.querySelector('[data-step="success"]');
        successStep.style.display = 'block';
        
        // تحديث رقم الطلب
        document.getElementById('requestIdDisplay').textContent = this.requestId;
        
        // إخفاء الأزرار
        document.querySelector('.form-actions').style.display = 'none';
        
        // تحديث شريط التقدم
        document.querySelectorAll('.step').forEach(step => {
            step.classList.add('completed');
            step.classList.remove('active');
        });
    }

    // توليد رقم طلب
    generateRequestId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `REQ-${timestamp}-${random}`.toUpperCase();
    }

    // دالة مساعدة للانتظار
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// إنشاء مثيل النموذج
const activationForm = new ActivationRequestForm();

// دوال عامة للواجهة
function selectLicense(type) {
    // إزالة التحديد من جميع الخيارات
    document.querySelectorAll('.license-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // تحديد الخيار المختار
    const selectedOption = document.querySelector(`#${type}`).closest('.license-option');
    selectedOption.classList.add('selected');
    
    // تحديد الراديو بوتن
    document.getElementById(type).checked = true;
    
    // تحديث الملخص
    activationForm.updateSummary();
}

function nextStep() {
    activationForm.nextStep();
}

function previousStep() {
    activationForm.previousStep();
}

function showTerms() {
    alert('شروط الاستخدام:\n\n1. يجب استخدام البرنامج للأغراض المشروعة فقط\n2. لا يسمح بنسخ أو توزيع البرنامج\n3. الشركة غير مسؤولة عن أي أضرار قد تنتج عن الاستخدام\n4. يحق للشركة إلغاء الترخيص في أي وقت\n\nللمزيد من التفاصيل، يرجى الاتصال بالدعم الفني.');
}

function showPrivacy() {
    alert('سياسة الخصوصية:\n\n1. نحن نحترم خصوصيتك ونحمي بياناتك الشخصية\n2. لا نشارك معلوماتك مع أطراف ثالثة\n3. نستخدم معلوماتك فقط لتقديم الخدمة\n4. يمكنك طلب حذف بياناتك في أي وقت\n\nللمزيد من التفاصيل، يرجى الاتصال بالدعم الفني.');
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل صفحة طلب التفعيل المحسنة...');

    // فحص حالة نظام إدارة البيانات
    checkDataManagerStatus();

    // تهيئة النموذج
    activationForm.initialize();

    // إعداد مراقبة تحميل نظام إدارة البيانات
    setupDataManagerMonitoring();
});

// فحص حالة نظام إدارة البيانات
function checkDataManagerStatus() {
    console.log('🔍 فحص حالة نظام إدارة البيانات...');

    if (window.advancedDataManager) {
        console.log('✅ نظام إدارة البيانات متوفر');
        console.log('📊 حالة النظام:', {
            initialized: advancedDataManager.initialized,
            storageKeys: Object.keys(advancedDataManager.storageKeys)
        });
    } else if (window.AdvancedDataManager) {
        console.log('⚠️ فئة نظام إدارة البيانات متوفرة لكن لم يتم إنشاء مثيل');
        try {
            window.advancedDataManager = new AdvancedDataManager();
            advancedDataManager.initialize();
            console.log('✅ تم إنشاء وتهيئة نظام إدارة البيانات');
        } catch (error) {
            console.error('❌ فشل في إنشاء نظام إدارة البيانات:', error);
        }
    } else {
        console.log('❌ نظام إدارة البيانات غير متوفر');
        console.log('🔄 سيتم استخدام الحفظ المحلي كبديل');
    }
}

// إعداد مراقبة تحميل نظام إدارة البيانات
function setupDataManagerMonitoring() {
    let attempts = 0;
    const maxAttempts = 10;

    const checkInterval = setInterval(() => {
        attempts++;

        if (window.advancedDataManager && advancedDataManager.initialized) {
            console.log('✅ نظام إدارة البيانات جاهز');
            clearInterval(checkInterval);
            return;
        }

        if (attempts >= maxAttempts) {
            console.log('⚠️ انتهت محاولات انتظار نظام إدارة البيانات');
            console.log('🔄 سيتم استخدام الحفظ المحلي');
            clearInterval(checkInterval);
            return;
        }

        console.log(`🔄 محاولة ${attempts}/${maxAttempts} - انتظار نظام إدارة البيانات...`);
    }, 500);
}

// دالة اختبار النظام
function testSystem() {
    console.log('🧪 اختبار النظام...');

    // اختبار localStorage
    try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        console.log('✅ localStorage يعمل بشكل صحيح');
    } catch (error) {
        console.error('❌ مشكلة في localStorage:', error);
    }

    // اختبار نظام إدارة البيانات
    if (window.advancedDataManager) {
        try {
            const testData = {
                customerName: 'اختبار',
                phone: '**********',
                state: 'الجزائر',
                licenseType: 'trial'
            };

            console.log('🧪 اختبار إضافة طلب تفعيل...');
            const request = advancedDataManager.addActivationRequest(testData);
            console.log('✅ تم إضافة طلب الاختبار:', request);

            // حذف طلب الاختبار
            const requests = advancedDataManager.getActivationRequests();
            const filteredRequests = requests.filter(r => r.id !== request.id);
            advancedDataManager.saveData('activationRequests', filteredRequests);
            console.log('🗑️ تم حذف طلب الاختبار');

        } catch (error) {
            console.error('❌ فشل اختبار نظام إدارة البيانات:', error);
        }
    }
}
