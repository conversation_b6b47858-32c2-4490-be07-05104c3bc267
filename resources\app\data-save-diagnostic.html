<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 أداة تشخيص حفظ البيانات - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .diagnostic-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .diagnostic-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }

        .test-form {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.8rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.3rem;
            border-radius: 4px;
        }

        .log-entry.success { background: rgba(39, 174, 96, 0.2); }
        .log-entry.error { background: rgba(231, 76, 60, 0.2); }
        .log-entry.warning { background: rgba(243, 156, 18, 0.2); }
        .log-entry.info { background: rgba(52, 152, 219, 0.2); }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-tools"></i> أداة تشخيص حفظ البيانات</h1>
            <p>تشخيص شامل لمشاكل حفظ بيانات الزبائن</p>
        </div>

        <div class="content">
            <!-- فحص النظام الأساسي -->
            <div class="diagnostic-section">
                <h3><i class="fas fa-search"></i> فحص النظام الأساسي</h3>
                <button class="btn btn-primary" onclick="checkBasicSystem()">
                    <i class="fas fa-play"></i> فحص النظام
                </button>
                <button class="btn btn-info" onclick="checkLocalStorage()">
                    <i class="fas fa-database"></i> فحص التخزين المحلي
                </button>
                <button class="btn btn-warning" onclick="checkDataManager()">
                    <i class="fas fa-cogs"></i> فحص مدير البيانات
                </button>
                <div id="basicSystemStatus" class="status info">
                    جاهز لفحص النظام الأساسي
                </div>
            </div>

            <!-- اختبار حفظ البيانات -->
            <div class="diagnostic-section">
                <h3><i class="fas fa-save"></i> اختبار حفظ البيانات</h3>
                <div class="test-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم الزبون:</label>
                            <input type="text" id="testCustomerName" value="أحمد محمد علي">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف:</label>
                            <input type="tel" id="testPhone" value="0555123456">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>الولاية:</label>
                            <select id="testState">
                                <option value="الجزائر">الجزائر</option>
                                <option value="وهران">وهران</option>
                                <option value="قسنطينة">قسنطينة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>نوع الترخيص:</label>
                            <select id="testLicenseType">
                                <option value="trial">تجريبي</option>
                                <option value="lifetime">مدى الحياة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <button class="btn btn-success" onclick="testSaveData()">
                    <i class="fas fa-save"></i> اختبار حفظ البيانات
                </button>
                <button class="btn btn-warning" onclick="testSaveMultiple()">
                    <i class="fas fa-layer-group"></i> اختبار حفظ متعدد
                </button>
                <button class="btn btn-danger" onclick="testSaveStress()">
                    <i class="fas fa-fire"></i> اختبار الضغط
                </button>

                <div id="saveTestStatus" class="status info">
                    جاهز لاختبار حفظ البيانات
                </div>
            </div>

            <!-- اختبار استرداد البيانات -->
            <div class="diagnostic-section">
                <h3><i class="fas fa-download"></i> اختبار استرداد البيانات</h3>
                <button class="btn btn-primary" onclick="testDataRetrieval()">
                    <i class="fas fa-search"></i> اختبار الاسترداد
                </button>
                <button class="btn btn-info" onclick="showStoredData()">
                    <i class="fas fa-eye"></i> عرض البيانات المحفوظة
                </button>
                <button class="btn btn-warning" onclick="checkBackupData()">
                    <i class="fas fa-shield-alt"></i> فحص النسخ الاحتياطية
                </button>

                <div id="retrievalTestStatus" class="status info">
                    جاهز لاختبار استرداد البيانات
                </div>
            </div>

            <!-- إصلاح المشاكل -->
            <div class="diagnostic-section">
                <h3><i class="fas fa-wrench"></i> إصلاح المشاكل</h3>
                <button class="btn btn-success" onclick="fixDataIssues()">
                    <i class="fas fa-magic"></i> إصلاح تلقائي
                </button>
                <button class="btn btn-warning" onclick="clearCorruptedData()">
                    <i class="fas fa-broom"></i> مسح البيانات التالفة
                </button>
                <button class="btn btn-danger" onclick="resetDataSystem()">
                    <i class="fas fa-redo"></i> إعادة تعيين النظام
                </button>

                <div id="fixStatus" class="status info">
                    جاهز لإصلاح المشاكل
                </div>
            </div>

            <!-- شريط التقدم -->
            <div class="diagnostic-section">
                <h3><i class="fas fa-chart-line"></i> تقدم التشخيص</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText">0% - جاهز للبدء</div>
            </div>

            <!-- سجل التشخيص -->
            <div class="diagnostic-section">
                <h3><i class="fas fa-list"></i> سجل التشخيص</h3>
                <button class="btn btn-warning" onclick="clearLog()">
                    <i class="fas fa-trash"></i> مسح السجل
                </button>
                <button class="btn btn-info" onclick="exportLog()">
                    <i class="fas fa-download"></i> تصدير السجل
                </button>

                <div id="diagnosticLog" class="log-container">
                    <div class="log-entry info">[INIT] أداة التشخيص جاهزة للاستخدام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="src/utils/advanced-data-manager.js"></script>
    <script>
        // متغيرات النظام
        let diagnosticProgress = 0;
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;

        // إضافة سجل
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('diagnosticLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // تحديث شريط التقدم
        function updateProgress(percentage, text) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressFill.style.width = percentage + '%';
            progressText.textContent = `${percentage}% - ${text}`;
        }

        // فحص النظام الأساسي
        async function checkBasicSystem() {
            addLog('🔍 بدء فحص النظام الأساسي...', 'info');
            updateProgress(10, 'فحص النظام الأساسي');

            const statusDiv = document.getElementById('basicSystemStatus');
            let results = '';
            let issues = [];

            // فحص المتصفح
            try {
                const browserInfo = {
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    cookieEnabled: navigator.cookieEnabled,
                    onLine: navigator.onLine
                };

                results += `✅ المتصفح: ${getBrowserName()}<br>`;
                results += `✅ اللغة: ${browserInfo.language}<br>`;
                results += `✅ الكوكيز: ${browserInfo.cookieEnabled ? 'مفعلة' : 'معطلة'}<br>`;
                results += `✅ الاتصال: ${browserInfo.onLine ? 'متصل' : 'غير متصل'}<br>`;

                addLog('✅ فحص المتصفح مكتمل', 'success');

            } catch (error) {
                results += `❌ خطأ في فحص المتصفح: ${error.message}<br>`;
                issues.push('مشكلة في معلومات المتصفح');
                addLog(`❌ خطأ في فحص المتصفح: ${error.message}`, 'error');
            }

            // فحص JavaScript
            try {
                const jsFeatures = {
                    localStorage: typeof Storage !== 'undefined',
                    JSON: typeof JSON !== 'undefined',
                    Promise: typeof Promise !== 'undefined',
                    fetch: typeof fetch !== 'undefined'
                };

                Object.entries(jsFeatures).forEach(([feature, supported]) => {
                    if (supported) {
                        results += `✅ ${feature}: مدعوم<br>`;
                    } else {
                        results += `❌ ${feature}: غير مدعوم<br>`;
                        issues.push(`${feature} غير مدعوم`);
                    }
                });

                addLog('✅ فحص JavaScript مكتمل', 'success');

            } catch (error) {
                results += `❌ خطأ في فحص JavaScript: ${error.message}<br>`;
                issues.push('مشكلة في JavaScript');
                addLog(`❌ خطأ في فحص JavaScript: ${error.message}`, 'error');
            }

            updateProgress(30, 'فحص النظام مكتمل');

            statusDiv.className = issues.length > 0 ? 'status warning' : 'status success';
            statusDiv.innerHTML = results + (issues.length > 0 ? `<br><strong>مشاكل محتملة:</strong><br>${issues.join('<br>')}` : '');

            addLog(`🎯 فحص النظام الأساسي مكتمل - ${issues.length} مشكلة`, issues.length > 0 ? 'warning' : 'success');
        }

        // فحص التخزين المحلي
        async function checkLocalStorage() {
            addLog('💾 بدء فحص التخزين المحلي...', 'info');
            updateProgress(40, 'فحص التخزين المحلي');

            const statusDiv = document.getElementById('basicSystemStatus');
            let results = '';
            let issues = [];

            try {
                // اختبار الكتابة
                const testKey = 'diagnostic_test_' + Date.now();
                const testData = { test: 'data', timestamp: new Date().toISOString() };

                localStorage.setItem(testKey, JSON.stringify(testData));
                results += '✅ الكتابة في localStorage: تعمل<br>';
                addLog('✅ اختبار الكتابة نجح', 'success');

                // اختبار القراءة
                const retrievedData = localStorage.getItem(testKey);
                if (retrievedData) {
                    const parsedData = JSON.parse(retrievedData);
                    if (parsedData.test === 'data') {
                        results += '✅ القراءة من localStorage: تعمل<br>';
                        addLog('✅ اختبار القراءة نجح', 'success');
                    } else {
                        results += '❌ القراءة من localStorage: بيانات تالفة<br>';
                        issues.push('بيانات تالفة في localStorage');
                        addLog('❌ بيانات تالفة في localStorage', 'error');
                    }
                } else {
                    results += '❌ القراءة من localStorage: فشلت<br>';
                    issues.push('فشل في قراءة localStorage');
                    addLog('❌ فشل في قراءة localStorage', 'error');
                }

                // اختبار الحذف
                localStorage.removeItem(testKey);
                const deletedData = localStorage.getItem(testKey);
                if (!deletedData) {
                    results += '✅ الحذف من localStorage: يعمل<br>';
                    addLog('✅ اختبار الحذف نجح', 'success');
                } else {
                    results += '❌ الحذف من localStorage: فشل<br>';
                    issues.push('فشل في حذف localStorage');
                    addLog('❌ فشل في حذف localStorage', 'error');
                }

                // فحص المساحة المتاحة
                const storageInfo = getStorageInfo();
                results += `📊 المساحة المستخدمة: ${storageInfo.used} KB<br>`;
                results += `📊 عدد العناصر: ${storageInfo.items}<br>`;

                if (storageInfo.used > 5000) { // أكثر من 5MB
                    results += '⚠️ تحذير: مساحة كبيرة مستخدمة<br>';
                    issues.push('مساحة تخزين كبيرة');
                    addLog('⚠️ مساحة تخزين كبيرة مستخدمة', 'warning');
                }

            } catch (error) {
                results += `❌ خطأ في localStorage: ${error.message}<br>`;
                issues.push(`خطأ في localStorage: ${error.message}`);
                addLog(`❌ خطأ في localStorage: ${error.message}`, 'error');
            }

            updateProgress(60, 'فحص التخزين مكتمل');

            statusDiv.className = issues.length > 0 ? 'status warning' : 'status success';
            statusDiv.innerHTML = results + (issues.length > 0 ? `<br><strong>مشاكل:</strong><br>${issues.join('<br>')}` : '');

            addLog(`💾 فحص التخزين المحلي مكتمل - ${issues.length} مشكلة`, issues.length > 0 ? 'warning' : 'success');
        }

        // فحص مدير البيانات
        async function checkDataManager() {
            addLog('🔧 بدء فحص مدير البيانات...', 'info');
            updateProgress(70, 'فحص مدير البيانات');

            const statusDiv = document.getElementById('basicSystemStatus');
            let results = '';
            let issues = [];

            try {
                // فحص وجود النظام
                if (typeof window.AdvancedDataManager !== 'undefined') {
                    results += '✅ فئة AdvancedDataManager: موجودة<br>';
                    addLog('✅ فئة AdvancedDataManager موجودة', 'success');
                } else {
                    results += '❌ فئة AdvancedDataManager: غير موجودة<br>';
                    issues.push('فئة AdvancedDataManager غير موجودة');
                    addLog('❌ فئة AdvancedDataManager غير موجودة', 'error');
                }

                // فحص المثيل
                if (typeof window.advancedDataManager !== 'undefined') {
                    results += '✅ مثيل advancedDataManager: موجود<br>';
                    addLog('✅ مثيل advancedDataManager موجود', 'success');

                    // فحص التهيئة
                    if (advancedDataManager.initialized) {
                        results += '✅ النظام: مُهيأ<br>';
                        addLog('✅ النظام مُهيأ', 'success');

                        // فحص الوظائف
                        const functions = ['addActivationRequest', 'getActivationRequests', 'saveData', 'getData'];
                        functions.forEach(func => {
                            if (typeof advancedDataManager[func] === 'function') {
                                results += `✅ دالة ${func}: موجودة<br>`;
                            } else {
                                results += `❌ دالة ${func}: غير موجودة<br>`;
                                issues.push(`دالة ${func} غير موجودة`);
                            }
                        });

                    } else {
                        results += '❌ النظام: غير مُهيأ<br>';
                        issues.push('النظام غير مُهيأ');
                        addLog('❌ النظام غير مُهيأ', 'error');
                    }

                } else {
                    results += '❌ مثيل advancedDataManager: غير موجود<br>';
                    issues.push('مثيل advancedDataManager غير موجود');
                    addLog('❌ مثيل advancedDataManager غير موجود', 'error');
                }

            } catch (error) {
                results += `❌ خطأ في فحص مدير البيانات: ${error.message}<br>`;
                issues.push(`خطأ في مدير البيانات: ${error.message}`);
                addLog(`❌ خطأ في فحص مدير البيانات: ${error.message}`, 'error');
            }

            updateProgress(90, 'فحص مدير البيانات مكتمل');

            statusDiv.className = issues.length > 0 ? 'status error' : 'status success';
            statusDiv.innerHTML = results + (issues.length > 0 ? `<br><strong>مشاكل:</strong><br>${issues.join('<br>')}` : '');

            addLog(`🔧 فحص مدير البيانات مكتمل - ${issues.length} مشكلة`, issues.length > 0 ? 'error' : 'success');
        }

        // دوال مساعدة
        function getBrowserName() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome')) return 'Google Chrome';
            if (userAgent.includes('Firefox')) return 'Mozilla Firefox';
            if (userAgent.includes('Safari')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Microsoft Edge';
            return 'غير معروف';
        }

        function getStorageInfo() {
            let used = 0;
            let items = localStorage.length;

            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    used += localStorage[key].length + key.length;
                }
            }

            return {
                used: Math.round(used / 1024), // KB
                items: items
            };
        }

        // مسح السجل
        function clearLog() {
            document.getElementById('diagnosticLog').innerHTML = '<div class="log-entry info">[CLEAR] تم مسح السجل</div>';
        }

        // اختبار حفظ البيانات
        async function testSaveData() {
            addLog('💾 بدء اختبار حفظ البيانات...', 'info');
            updateProgress(20, 'اختبار حفظ البيانات');

            const statusDiv = document.getElementById('saveTestStatus');
            let results = '';
            let success = true;

            try {
                // جمع البيانات من النموذج
                const customerData = {
                    customerName: document.getElementById('testCustomerName').value,
                    phone: document.getElementById('testPhone').value,
                    state: document.getElementById('testState').value,
                    licenseType: document.getElementById('testLicenseType').value,
                    deviceInfo: {
                        os: 'Test OS',
                        browser: 'Test Browser',
                        timestamp: new Date().toISOString()
                    }
                };

                addLog(`📝 بيانات الاختبار: ${customerData.customerName}`, 'info');

                // اختبار الحفظ باستخدام مدير البيانات
                if (window.advancedDataManager && typeof advancedDataManager.addActivationRequest === 'function') {
                    try {
                        const request = advancedDataManager.addActivationRequest(customerData);
                        results += `✅ حفظ بمدير البيانات: نجح (ID: ${request.id})<br>`;
                        addLog(`✅ تم حفظ الطلب بمدير البيانات: ${request.id}`, 'success');

                        // التحقق من الحفظ
                        const savedRequests = advancedDataManager.getActivationRequests();
                        const foundRequest = savedRequests.find(r => r.id === request.id);

                        if (foundRequest) {
                            results += '✅ التحقق من الحفظ: نجح<br>';
                            addLog('✅ تم التحقق من حفظ الطلب', 'success');
                        } else {
                            results += '❌ التحقق من الحفظ: فشل<br>';
                            success = false;
                            addLog('❌ فشل في التحقق من حفظ الطلب', 'error');
                        }

                    } catch (error) {
                        results += `❌ خطأ في مدير البيانات: ${error.message}<br>`;
                        success = false;
                        addLog(`❌ خطأ في مدير البيانات: ${error.message}`, 'error');
                    }
                } else {
                    results += '⚠️ مدير البيانات غير متوفر<br>';
                    addLog('⚠️ مدير البيانات غير متوفر', 'warning');
                }

                // اختبار الحفظ المباشر في localStorage
                try {
                    const directKey = 'direct_test_' + Date.now();
                    const directData = {
                        ...customerData,
                        id: directKey,
                        savedAt: new Date().toISOString()
                    };

                    localStorage.setItem(directKey, JSON.stringify(directData));

                    // التحقق من الحفظ المباشر
                    const savedDirectData = localStorage.getItem(directKey);
                    if (savedDirectData) {
                        const parsedData = JSON.parse(savedDirectData);
                        if (parsedData.customerName === customerData.customerName) {
                            results += '✅ حفظ مباشر في localStorage: نجح<br>';
                            addLog('✅ الحفظ المباشر في localStorage نجح', 'success');
                        } else {
                            results += '❌ حفظ مباشر في localStorage: بيانات تالفة<br>';
                            success = false;
                            addLog('❌ بيانات تالفة في الحفظ المباشر', 'error');
                        }
                    } else {
                        results += '❌ حفظ مباشر في localStorage: فشل<br>';
                        success = false;
                        addLog('❌ فشل في الحفظ المباشر', 'error');
                    }

                    // تنظيف
                    localStorage.removeItem(directKey);

                } catch (error) {
                    results += `❌ خطأ في الحفظ المباشر: ${error.message}<br>`;
                    success = false;
                    addLog(`❌ خطأ في الحفظ المباشر: ${error.message}`, 'error');
                }

            } catch (error) {
                results += `❌ خطأ عام في الاختبار: ${error.message}<br>`;
                success = false;
                addLog(`❌ خطأ عام في اختبار الحفظ: ${error.message}`, 'error');
            }

            updateProgress(50, 'اختبار الحفظ مكتمل');

            statusDiv.className = success ? 'status success' : 'status error';
            statusDiv.innerHTML = results;

            addLog(`💾 اختبار حفظ البيانات مكتمل - ${success ? 'نجح' : 'فشل'}`, success ? 'success' : 'error');
        }

        // اختبار حفظ متعدد
        async function testSaveMultiple() {
            addLog('📚 بدء اختبار الحفظ المتعدد...', 'info');
            updateProgress(30, 'اختبار الحفظ المتعدد');

            const statusDiv = document.getElementById('saveTestStatus');
            let results = '';
            let successCount = 0;
            let totalCount = 5;

            try {
                const names = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'علي محمود'];

                for (let i = 0; i < totalCount; i++) {
                    try {
                        const customerData = {
                            customerName: names[i],
                            phone: '055' + Math.floor(Math.random() * 10000000).toString().padStart(7, '0'),
                            state: 'الجزائر',
                            licenseType: Math.random() > 0.5 ? 'lifetime' : 'trial',
                            deviceInfo: {
                                os: 'Test OS',
                                browser: 'Test Browser',
                                timestamp: new Date().toISOString()
                            }
                        };

                        if (window.advancedDataManager) {
                            const request = advancedDataManager.addActivationRequest(customerData);
                            successCount++;
                            addLog(`✅ حفظ ${i + 1}: ${customerData.customerName}`, 'success');
                        } else {
                            // حفظ مباشر
                            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                            requests.push({
                                ...customerData,
                                id: Date.now().toString() + i,
                                requestDate: new Date().toISOString(),
                                status: 'pending'
                            });
                            localStorage.setItem('activationRequests', JSON.stringify(requests));
                            successCount++;
                            addLog(`✅ حفظ مباشر ${i + 1}: ${customerData.customerName}`, 'success');
                        }

                        // تأخير قصير
                        await new Promise(resolve => setTimeout(resolve, 100));

                    } catch (error) {
                        addLog(`❌ فشل في حفظ ${i + 1}: ${error.message}`, 'error');
                    }
                }

                results = `✅ تم حفظ ${successCount} من ${totalCount} طلب بنجاح<br>`;
                results += `📊 معدل النجاح: ${Math.round((successCount / totalCount) * 100)}%<br>`;

            } catch (error) {
                results += `❌ خطأ في الاختبار المتعدد: ${error.message}<br>`;
                addLog(`❌ خطأ في الاختبار المتعدد: ${error.message}`, 'error');
            }

            updateProgress(60, 'الاختبار المتعدد مكتمل');

            statusDiv.className = successCount === totalCount ? 'status success' : 'status warning';
            statusDiv.innerHTML = results;

            addLog(`📚 اختبار الحفظ المتعدد مكتمل - ${successCount}/${totalCount}`, successCount === totalCount ? 'success' : 'warning');
        }

        // اختبار الضغط
        async function testSaveStress() {
            addLog('🔥 بدء اختبار الضغط...', 'info');
            updateProgress(40, 'اختبار الضغط');

            const statusDiv = document.getElementById('saveTestStatus');
            let results = '';
            let successCount = 0;
            let totalCount = 20;
            let startTime = Date.now();

            try {
                for (let i = 0; i < totalCount; i++) {
                    try {
                        const customerData = {
                            customerName: `زبون اختبار ${i + 1}`,
                            phone: '055' + Math.floor(Math.random() * 10000000).toString().padStart(7, '0'),
                            state: ['الجزائر', 'وهران', 'قسنطينة'][Math.floor(Math.random() * 3)],
                            licenseType: Math.random() > 0.5 ? 'lifetime' : 'trial',
                            deviceInfo: {
                                os: 'Test OS',
                                browser: 'Test Browser',
                                timestamp: new Date().toISOString()
                            }
                        };

                        if (window.advancedDataManager) {
                            advancedDataManager.addActivationRequest(customerData);
                        } else {
                            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                            requests.push({
                                ...customerData,
                                id: Date.now().toString() + i,
                                requestDate: new Date().toISOString(),
                                status: 'pending'
                            });
                            localStorage.setItem('activationRequests', JSON.stringify(requests));
                        }

                        successCount++;

                    } catch (error) {
                        addLog(`❌ فشل في حفظ ${i + 1}: ${error.message}`, 'error');
                    }
                }

                const endTime = Date.now();
                const duration = endTime - startTime;

                results = `✅ تم حفظ ${successCount} من ${totalCount} طلب<br>`;
                results += `⏱️ الوقت المستغرق: ${duration} مللي ثانية<br>`;
                results += `📊 معدل النجاح: ${Math.round((successCount / totalCount) * 100)}%<br>`;
                results += `⚡ السرعة: ${Math.round(successCount / (duration / 1000))} طلب/ثانية<br>`;

            } catch (error) {
                results += `❌ خطأ في اختبار الضغط: ${error.message}<br>`;
                addLog(`❌ خطأ في اختبار الضغط: ${error.message}`, 'error');
            }

            updateProgress(70, 'اختبار الضغط مكتمل');

            statusDiv.className = successCount > totalCount * 0.8 ? 'status success' : 'status warning';
            statusDiv.innerHTML = results;

            addLog(`🔥 اختبار الضغط مكتمل - ${successCount}/${totalCount}`, successCount > totalCount * 0.8 ? 'success' : 'warning');
        }

        // تصدير السجل
        function exportLog() {
            const logContainer = document.getElementById('diagnosticLog');
            const logText = logContainer.innerText;

            const blob = new Blob([logText], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `diagnostic_log_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);
            addLog('📄 تم تصدير السجل', 'success');
        }

        // تهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 تم تحميل أداة تشخيص حفظ البيانات', 'success');
            updateProgress(0, 'جاهز للبدء');
        });
    </script>
</body>
</html>