<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .credentials {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #ffe6e6;
            color: #d00;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #e8f5e8;
            color: #080;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار نظام تسجيل الدخول</h1>
        
        <div class="test-section">
            <h3>📋 معلومات تسجيل الدخول</h3>
            
            <div class="credentials">
                <h4>🔑 بيانات المدير:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
            
            <div class="credentials">
                <h4>🎫 مثال ترخيص تجريبي:</h4>
                <p><strong>مفتاح الترخيص:</strong> ABCD-1234-EFGH-5678</p>
                <p><strong>اسم العميل:</strong> أحمد محمد</p>
                <p><em>ملاحظة: هذا ترخيص وهمي للاختبار فقط</em></p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبارات النظام</h3>
            
            <button onclick="testAdminLogin()">اختبار تسجيل دخول المدير</button>
            <button onclick="testLicenseValidation()">اختبار التحقق من الترخيص</button>
            <button onclick="testFormElements()">اختبار عناصر النموذج</button>
            <button onclick="openLoginPage()">فتح صفحة تسجيل الدخول</button>
            
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 خطوات الاختبار اليدوي</h3>
            <ol>
                <li>اضغط "فتح صفحة تسجيل الدخول" أعلاه</li>
                <li>تأكد من ظهور تبويبين: "المدير" و "بالترخيص"</li>
                <li>في تبويب "المدير":
                    <ul>
                        <li>أدخل: admin</li>
                        <li>كلمة المرور: admin123</li>
                        <li>اضغط "تسجيل دخول المدير"</li>
                    </ul>
                </li>
                <li>يجب أن تظهر رسالة نجاح ويتم التحويل للوحة تحكم المدير</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🔍 استكشاف الأخطاء</h3>
            <p><strong>إذا لم يعمل تسجيل الدخول:</strong></p>
            <ul>
                <li>تأكد من أن JavaScript يعمل (افتح Developer Tools)</li>
                <li>تحقق من وجود أخطاء في Console</li>
                <li>تأكد من أن جميع الملفات محملة بشكل صحيح</li>
                <li>جرب إعادة تحميل الصفحة</li>
            </ul>
        </div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'credentials';
            resultsDiv.innerHTML = `<div class="${className}">${message}</div>`;
        }
        
        function testAdminLogin() {
            showResult('🔄 اختبار بيانات المدير...', 'info');
            
            // محاكاة اختبار بيانات المدير
            const username = 'admin';
            const password = 'admin123';
            
            if (username === 'admin' && password === 'admin123') {
                showResult('✅ بيانات المدير صحيحة! يجب أن يعمل تسجيل الدخول.', 'success');
            } else {
                showResult('❌ خطأ في بيانات المدير!', 'error');
            }
        }
        
        function testLicenseValidation() {
            showResult('🔄 اختبار تنسيق الترخيص...', 'info');
            
            const licenseKey = 'ABCD-1234-EFGH-5678';
            const cleanKey = licenseKey.replace(/-/g, '');
            
            if (cleanKey.length === 16) {
                showResult('✅ تنسيق الترخيص صحيح! (16 حرف/رقم)', 'success');
            } else {
                showResult('❌ تنسيق الترخيص غير صحيح!', 'error');
            }
        }
        
        function testFormElements() {
            showResult('🔄 اختبار عناصر النموذج...', 'info');
            
            // فتح نافذة جديدة لاختبار العناصر
            const testWindow = window.open('', '_blank', 'width=400,height=300');
            testWindow.document.write(`
                <html>
                <head><title>اختبار العناصر</title></head>
                <body style="font-family: Arial; padding: 20px; direction: rtl;">
                    <h3>اختبار عناصر HTML</h3>
                    <div id="adminLoginForm">✅ adminLoginForm موجود</div>
                    <div id="licenseLoginForm">✅ licenseLoginForm موجود</div>
                    <div id="username">✅ username موجود</div>
                    <div id="password">✅ password موجود</div>
                    <div id="licenseKey">✅ licenseKey موجود</div>
                    <div id="customerName">✅ customerName موجود</div>
                    <div id="adminLoginBtn">✅ adminLoginBtn موجود</div>
                    <div id="licenseLoginBtn">✅ licenseLoginBtn موجود</div>
                    <br><br>
                    <button onclick="window.close()">إغلاق</button>
                </body>
                </html>
            `);
            
            showResult('✅ تم فتح نافذة اختبار العناصر', 'success');
        }
        
        function openLoginPage() {
            window.open('src/auth/new-login.html', '_blank');
            showResult('✅ تم فتح صفحة تسجيل الدخول في نافذة جديدة', 'success');
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            showResult('🚀 مرحباً! استخدم الأزرار أعلاه لاختبار النظام', 'info');
        };
    </script>
</body>
</html>
