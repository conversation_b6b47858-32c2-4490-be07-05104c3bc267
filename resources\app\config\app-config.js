// إعدادات التطبيق الرئيسية
const AppConfig = {
  // معلومات التطبيق
  app: {
    name: 'مؤسسة وقود المستقبل',
    version: '3.0.0',
    description: 'نظام إدارة مؤسسة وقود المستقبل المحدث'
  },

  // إعدادات الأمان
  security: {
    sessionTimeout: 8 * 60 * 60 * 1000, // 8 ساعات
    maxLoginAttempts: 3,
    lockoutDuration: 15 * 60 * 1000 // 15 دقيقة
  },

  // إعدادات قاعدة البيانات
  database: {
    path: './data/app.db',
    backupInterval: 24 * 60 * 60 * 1000 // 24 ساعة
  },

  // إعدادات الواجهة
  ui: {
    theme: 'light',
    language: 'ar',
    rtl: true
  },

  // معلومات الدعم
  support: {
    phone: '0696924176',
    whatsapp: '0696924176',
    email: '<EMAIL>'
  }
};

module.exports = AppConfig;
