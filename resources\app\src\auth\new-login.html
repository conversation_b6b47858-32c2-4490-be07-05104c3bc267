<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="new-login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <!-- خلفية متحركة -->
        <div class="background-animation">
            <div class="floating-shape shape-1"></div>
            <div class="floating-shape shape-2"></div>
            <div class="floating-shape shape-3"></div>
            <div class="floating-shape shape-4"></div>
        </div>

        <!-- بطاقة تسجيل الدخول -->
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-gas-pump"></i>
                </div>
                <h1>مؤسسة وقود المستقبل</h1>
                <p class="subtitle">نظام إدارة شامل ومتطور</p>
                <div class="version">الإصدار 3.0.0</div>
            </div>

            <!-- تبويبات تسجيل الدخول -->
            <div class="login-tabs">
                <button type="button" class="tab-btn active" onclick="switchTab('admin')" id="adminTab">
                    <i class="fas fa-user-shield"></i>
                    <span>المدير</span>
                </button>
                <button type="button" class="tab-btn" onclick="switchTab('license')" id="licenseTab">
                    <i class="fas fa-key"></i>
                    <span>بالترخيص</span>
                </button>
            </div>

            <!-- نموذج تسجيل دخول المدير -->
            <form id="adminLoginForm" class="login-form active">
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        اسم المستخدم
                    </label>
                    <input type="text" id="username" name="username" required
                           autocomplete="username" placeholder="أدخل اسم المستخدم">
                    <span class="error-message" id="usernameError"></span>
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        كلمة المرور
                    </label>
                    <div class="password-input">
                        <input type="password" id="password" name="password" required
                               autocomplete="current-password" placeholder="أدخل كلمة المرور">
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                    <span class="error-message" id="passwordError"></span>
                </div>

                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" class="forgot-password" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
                </div>

                <button type="submit" class="login-btn" id="adminLoginBtn">
                    <span class="btn-text">تسجيل دخول المدير</span>
                    <div class="loading-spinner" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>

                <div class="message-container">
                    <div class="error-message main-error" id="adminError" style="display: none;"></div>
                    <div class="success-message" id="adminSuccess" style="display: none;"></div>
                </div>
            </form>

            <!-- نموذج تسجيل دخول بالترخيص -->
            <form id="licenseLoginForm" class="login-form">
                <div class="form-group">
                    <label for="licenseKey">
                        <i class="fas fa-key"></i>
                        مفتاح الترخيص
                    </label>
                    <input type="text" id="licenseKey" name="licenseKey"
                           placeholder="XXXX-XXXX-XXXX-XXXX" required maxlength="19"
                           oninput="formatLicenseKey(this)">
                    <span class="error-message" id="licenseKeyError"></span>
                    <small class="input-hint">أدخل مفتاح الترخيص المكون من 16 رقم/حرف</small>
                </div>

                <div class="form-group">
                    <label for="customerName">
                        <i class="fas fa-user"></i>
                        اسم العميل
                    </label>
                    <input type="text" id="customerName" name="customerName"
                           placeholder="أدخل اسمك كما هو مسجل في الترخيص" required>
                    <span class="error-message" id="customerNameError"></span>
                </div>

                <button type="submit" class="login-btn license-login-btn" id="licenseLoginBtn">
                    <span class="btn-text">تسجيل الدخول بالترخيص</span>
                    <div class="loading-spinner" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>

                <div class="message-container">
                    <div class="error-message main-error" id="licenseError" style="display: none;"></div>
                    <div class="success-message" id="licenseSuccess" style="display: none;"></div>
                </div>

                <div class="license-info">
                    <div class="info-item">
                        <i class="fas fa-info-circle"></i>
                        <span>إذا لم يكن لديك ترخيص، يمكنك طلب تفعيل جديد أدناه</span>
                    </div>
                </div>
            </form>

            <!-- زر طلب التفعيل -->
            <div class="activation-section">
                <div class="divider">
                    <span>أو</span>
                </div>
                <button type="button" class="activation-btn" onclick="showActivationRequest()">
                    <i class="fas fa-plus-circle"></i>
                    <span>طلب تفعيل جديد</span>
                </button>
            </div>

            <div class="login-footer">
                <div class="theme-toggle">
                    <button onclick="toggleTheme()" class="theme-btn" title="تغيير المظهر">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                </div>
                <div class="quick-login">
                    <p>حسابات تجريبية:</p>
                    <div class="demo-accounts">
                        <button onclick="quickLogin('admin', 'admin123')" class="demo-btn">مدير</button>
                        <button onclick="quickLogin('user', 'user123')" class="demo-btn">مستخدم</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة نسيان كلمة المرور -->
    <div id="forgotPasswordModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="closeForgotPassword()">&times;</span>
            <h2><i class="fas fa-key"></i> استعادة كلمة المرور</h2>
            <p>يرجى التواصل مع المدير لاستعادة كلمة المرور</p>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>الهاتف: **********</span>
                </div>
                <div class="contact-item">
                    <i class="fab fa-whatsapp"></i>
                    <span>واتساب: **********</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>البريد: <EMAIL></span>
                </div>
            </div>
            <div class="support-actions">
                <a href="tel:**********" class="support-btn phone-btn">
                    <i class="fas fa-phone"></i>
                    اتصال مباشر
                </a>
                <a href="https://wa.me/213696924176" target="_blank" class="support-btn whatsapp-btn">
                    <i class="fab fa-whatsapp"></i>
                    واتساب
                </a>
            </div>
            <button onclick="closeForgotPassword()" class="modal-btn">إغلاق</button>
        </div>
    </div>

    <script src="../utils/validation.js"></script>
    <script src="new-login.js"></script>
</body>
</html>
