/* إعدادات عامة */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 12px;
    --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    /* ألوان الوضع الفاتح */
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-bg: rgba(255, 255, 255, 0.95);
    --text-color: #2c3e50;
    --input-bg: #ffffff;
    --input-border: #ddd;
    --button-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --button-text: #ffffff;
}

/* الوضع المظلم */
body.dark-mode {
    --bg-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --card-bg: rgba(22, 33, 62, 0.95);
    --text-color: #e6e6e6;
    --input-bg: #2d2d2d;
    --input-border: #555;
    --button-bg: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    overflow: hidden;
}

.login-container {
    min-height: 100vh;
    background: var(--bg-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* خلفية متحركة */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* بطاقة تسجيل الدخول */
.login-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2.5rem;
    width: 100%;
    max-width: 420px;
    position: relative;
    z-index: 2;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* هيدر تسجيل الدخول */
.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo {
    width: 80px;
    height: 80px;
    background: var(--button-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.logo i {
    font-size: 2rem;
    color: white;
}

.login-header h1 {
    color: var(--text-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.subtitle {
    color: var(--text-color);
    opacity: 0.7;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.version {
    background: var(--button-bg);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
}

/* تبويبات تسجيل الدخول */
.login-tabs {
    display: flex;
    margin-bottom: 2rem;
    background: var(--card-bg);
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
    color: var(--text-muted);
}

.tab-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.tab-btn i {
    font-size: 1.1rem;
}

.tab-btn span {
    font-size: 0.9rem;
}

/* نموذج تسجيل الدخول */
.login-form {
    margin-bottom: 1.5rem;
    display: none;
}

.login-form.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group label i {
    margin-left: 0.5rem;
    color: var(--secondary-color);
}

.form-group input {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 2px solid var(--input-border);
    border-radius: 8px;
    background: var(--input-bg);
    color: var(--text-color);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 0.5rem;
    opacity: 0.6;
    transition: var(--transition);
}

.toggle-password:hover {
    opacity: 1;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--text-color);
    font-size: 0.9rem;
}

.remember-me input {
    margin-left: 0.5rem;
}

.forgot-password {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    background: var(--button-bg);
    color: var(--button-text);
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.message-container {
    margin-top: 1rem;
}

.error-message, .success-message {
    padding: 0.8rem;
    border-radius: 6px;
    font-size: 0.9rem;
    text-align: center;
}

.error-message {
    background: rgba(231, 76, 60, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.success-message {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(39, 174, 96, 0.3);
}

/* فوتر تسجيل الدخول */
.login-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-btn {
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    color: var(--text-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-btn:hover {
    background: var(--secondary-color);
    color: white;
}

.quick-login {
    text-align: center;
}

.quick-login p {
    color: var(--text-color);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    opacity: 0.7;
}

.demo-accounts {
    display: flex;
    gap: 0.5rem;
}

.demo-btn {
    background: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(52, 152, 219, 0.3);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: var(--transition);
}

.demo-btn:hover {
    background: var(--secondary-color);
    color: white;
}

/* نافذة منبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--card-bg);
    padding: 2rem;
    border-radius: var(--border-radius);
    max-width: 400px;
    width: 90%;
    text-align: center;
    position: relative;
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
}

.modal-content h2 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.contact-info {
    margin: 1.5rem 0;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0.5rem 0;
    color: var(--text-color);
}

.contact-item i {
    margin-left: 0.5rem;
    color: var(--secondary-color);
}

.modal-btn {
    background: var(--button-bg);
    color: var(--button-text);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.modal-btn:hover {
    transform: translateY(-2px);
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 480px) {
    .login-card {
        margin: 1rem;
        padding: 2rem 1.5rem;
    }
    
    .login-header h1 {
        font-size: 1.5rem;
    }
    
    .demo-accounts {
        flex-direction: column;
    }
}

/* تنسيقات إضافية للتبويبات والترخيص */

/* زر تسجيل الدخول بالترخيص */
.license-login-btn {
    background: linear-gradient(135deg, #27ae60, #229954) !important;
}

.license-login-btn:hover {
    background: linear-gradient(135deg, #229954, #1e8449) !important;
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

/* تنسيق حقل الترخيص */
#licenseKey {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    letter-spacing: 2px;
    text-transform: uppercase;
}

/* تلميحات الإدخال */
.input-hint {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--text-muted);
    font-style: italic;
}

/* معلومات الترخيص */
.license-info {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 8px;
    border-right: 4px solid var(--primary-color);
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.info-item i {
    color: var(--primary-color);
    font-size: 1rem;
}

/* قسم طلب التفعيل */
.activation-section {
    margin-top: 2rem;
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.divider span {
    background: var(--card-bg);
    padding: 0 1rem;
    color: var(--text-muted);
    font-size: 0.9rem;
    position: relative;
    z-index: 1;
}

/* زر طلب التفعيل */
.activation-btn {
    width: 100%;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.activation-btn:hover {
    background: linear-gradient(135deg, #e67e22, #d35400);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
}

.activation-btn i {
    font-size: 1.1rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .tab-btn span {
        display: none;
    }

    .tab-btn {
        padding: 0.8rem;
    }

    .login-tabs {
        margin-bottom: 1.5rem;
    }
}

/* أزرار الدعم */
.support-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    justify-content: center;
}

.support-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.phone-btn {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.phone-btn:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.whatsapp-btn {
    background: linear-gradient(135deg, #25d366, #20ba5a);
    color: white;
}

.whatsapp-btn:hover {
    background: linear-gradient(135deg, #20ba5a, #1da851);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.support-btn i {
    font-size: 1.1rem;
}
