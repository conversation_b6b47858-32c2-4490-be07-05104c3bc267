<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار تدفق تسجيل الدخول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 10px;
        }

        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        .session-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .link-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
        }

        .link-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .link-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار تدفق تسجيل الدخول</h1>
            <p>أداة لاختبار وتشخيص مشاكل تسجيل الدخول</p>
        </div>

        <div class="content">
            <div class="test-section">
                <h3><i class="fas fa-info-circle"></i> معلومات الجلسة الحالية</h3>
                <button class="btn btn-primary" onclick="checkCurrentSession()">
                    <i class="fas fa-search"></i> فحص الجلسة
                </button>
                <button class="btn btn-danger" onclick="clearSession()">
                    <i class="fas fa-trash"></i> مسح الجلسة
                </button>
                <div id="sessionStatus" class="session-info">
                    اضغط "فحص الجلسة" لعرض المعلومات
                </div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-sign-in-alt"></i> اختبار تسجيل الدخول</h3>
                <button class="btn btn-success" onclick="simulateAdminLogin()">
                    <i class="fas fa-user-shield"></i> محاكاة تسجيل دخول المدير
                </button>
                <button class="btn btn-warning" onclick="testLoginFlow()">
                    <i class="fas fa-play"></i> اختبار التدفق الكامل
                </button>
                <div id="loginStatus"></div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-shield-alt"></i> اختبار نظام الحماية</h3>
                <button class="btn btn-primary" onclick="testProtectionSystem()">
                    <i class="fas fa-test"></i> اختبار الحماية
                </button>
                <div id="protectionStatus"></div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-tools"></i> أدوات التشخيص</h3>
                <button class="btn btn-primary" onclick="runDiagnostics()">
                    <i class="fas fa-stethoscope"></i> تشغيل التشخيص
                </button>
                <button class="btn btn-success" onclick="checkLocalStorage()">
                    <i class="fas fa-database"></i> فحص localStorage
                </button>
                <div id="diagnosticsStatus"></div>
            </div>

            <div class="quick-links">
                <a href="src/auth/new-login.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-sign-in-alt"></i></div>
                    <h4>صفحة تسجيل الدخول</h4>
                    <p>فتح صفحة تسجيل الدخول</p>
                </a>
                <a href="src/components/admin/license-management.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-cogs"></i></div>
                    <h4>لوحة تحكم المدير</h4>
                    <p>فتح لوحة تحكم المدير</p>
                </a>
                <a href="diagnostic-tool.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-stethoscope"></i></div>
                    <h4>أداة التشخيص</h4>
                    <p>فتح أداة التشخيص الشاملة</p>
                </a>
                <a href="test-suite.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-flask"></i></div>
                    <h4>مجموعة الاختبارات</h4>
                    <p>فتح مجموعة الاختبارات</p>
                </a>
            </div>
        </div>
    </div>

    <script>
        // فحص الجلسة الحالية
        function checkCurrentSession() {
            const sessionData = localStorage.getItem('userSession');
            const statusDiv = document.getElementById('sessionStatus');
            
            if (sessionData) {
                try {
                    const session = JSON.parse(sessionData);
                    statusDiv.innerHTML = `
                        <strong>✅ توجد جلسة نشطة:</strong><br>
                        <strong>نوع المستخدم:</strong> ${session.userType || 'غير محدد'}<br>
                        <strong>اسم المستخدم:</strong> ${session.username || session.customerName || 'غير محدد'}<br>
                        <strong>وقت تسجيل الدخول:</strong> ${session.loginTime || 'غير محدد'}<br>
                        <strong>صالحة:</strong> ${session.isValid ? 'نعم' : 'لا'}<br>
                        <strong>البيانات الكاملة:</strong><br>
                        <pre>${JSON.stringify(session, null, 2)}</pre>
                    `;
                    statusDiv.className = 'session-info';
                } catch (error) {
                    statusDiv.innerHTML = `❌ خطأ في قراءة الجلسة: ${error.message}`;
                    statusDiv.className = 'session-info';
                }
            } else {
                statusDiv.innerHTML = '❌ لا توجد جلسة محفوظة';
                statusDiv.className = 'session-info';
            }
        }

        // مسح الجلسة
        function clearSession() {
            localStorage.removeItem('userSession');
            document.getElementById('sessionStatus').innerHTML = '✅ تم مسح الجلسة';
            setTimeout(checkCurrentSession, 1000);
        }

        // محاكاة تسجيل دخول المدير
        function simulateAdminLogin() {
            const sessionData = {
                username: 'admin',
                userType: 'admin',
                loginTime: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                isValid: true,
                expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
            };
            
            localStorage.setItem('userSession', JSON.stringify(sessionData));
            
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.innerHTML = '<div class="status success">✅ تم إنشاء جلسة مدير تجريبية بنجاح!</div>';
            
            setTimeout(checkCurrentSession, 500);
        }

        // اختبار التدفق الكامل
        function testLoginFlow() {
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.innerHTML = '<div class="status info">🔄 جاري اختبار التدفق...</div>';
            
            // مسح الجلسة أولاً
            localStorage.removeItem('userSession');
            
            setTimeout(() => {
                // إنشاء جلسة جديدة
                simulateAdminLogin();
                
                setTimeout(() => {
                    // اختبار الوصول للصفحة المحمية
                    statusDiv.innerHTML += '<div class="status info">🔍 اختبار الوصول للصفحة المحمية...</div>';
                    
                    // محاكاة فحص الحماية
                    const session = JSON.parse(localStorage.getItem('userSession'));
                    if (session && session.isValid && session.userType === 'admin') {
                        statusDiv.innerHTML += '<div class="status success">✅ التدفق يعمل بشكل صحيح!</div>';
                    } else {
                        statusDiv.innerHTML += '<div class="status error">❌ مشكلة في التدفق!</div>';
                    }
                }, 1000);
            }, 1000);
        }

        // اختبار نظام الحماية
        function testProtectionSystem() {
            const statusDiv = document.getElementById('protectionStatus');
            statusDiv.innerHTML = '<div class="status info">🔍 جاري اختبار نظام الحماية...</div>';
            
            // فحص وجود ملفات الحماية
            const protectionFiles = [
                'src/utils/page-protector.js',
                'src/utils/auth-guard.js'
            ];
            
            let results = '';
            protectionFiles.forEach(file => {
                // محاكاة فحص الملف
                results += `<div class="status info">📁 ${file}: متوقع وجوده</div>`;
            });
            
            // فحص الجلسة
            const session = localStorage.getItem('userSession');
            if (session) {
                results += '<div class="status success">✅ الجلسة موجودة - الحماية ستسمح بالوصول</div>';
            } else {
                results += '<div class="status warning">⚠️ لا توجد جلسة - الحماية ستمنع الوصول</div>';
            }
            
            statusDiv.innerHTML = results;
        }

        // تشغيل التشخيص
        function runDiagnostics() {
            const statusDiv = document.getElementById('diagnosticsStatus');
            statusDiv.innerHTML = '<div class="status info">🔄 جاري تشغيل التشخيص...</div>';
            
            setTimeout(() => {
                let results = '';
                
                // فحص localStorage
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    results += '<div class="status success">✅ localStorage يعمل بشكل صحيح</div>';
                } catch (error) {
                    results += '<div class="status error">❌ مشكلة في localStorage</div>';
                }
                
                // فحص الجلسة
                const session = localStorage.getItem('userSession');
                if (session) {
                    try {
                        const sessionData = JSON.parse(session);
                        if (sessionData.isValid) {
                            results += '<div class="status success">✅ الجلسة صالحة</div>';
                        } else {
                            results += '<div class="status warning">⚠️ الجلسة غير صالحة</div>';
                        }
                    } catch (error) {
                        results += '<div class="status error">❌ خطأ في قراءة الجلسة</div>';
                    }
                } else {
                    results += '<div class="status info">ℹ️ لا توجد جلسة</div>';
                }
                
                // فحص المتصفح
                results += `<div class="status info">🌐 المتصفح: ${navigator.userAgent.split(' ')[0]}</div>`;
                
                statusDiv.innerHTML = results;
            }, 1000);
        }

        // فحص localStorage
        function checkLocalStorage() {
            const statusDiv = document.getElementById('diagnosticsStatus');
            
            const keys = Object.keys(localStorage);
            let results = `<div class="status info">📊 عدد العناصر في localStorage: ${keys.length}</div>`;
            
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                const size = new Blob([value]).size;
                results += `<div class="status info">🔑 ${key}: ${size} بايت</div>`;
            });
            
            statusDiv.innerHTML = results;
        }

        // تشغيل فحص أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkCurrentSession();
        });
    </script>
</body>
</html>
