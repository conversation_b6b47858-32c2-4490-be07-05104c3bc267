/* إعدادات عامة */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    /* ألوان الوضع الفاتح */
    --bg-color: #f8f9fa;
    --card-bg: #ffffff;
    --text-color: #2c3e50;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --sidebar-bg: #2c3e50;
    --sidebar-text: #ffffff;
    --header-bg: #ffffff;
    --header-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* الوضع المظلم */
body.dark-mode {
    --bg-color: #1a1a2e;
    --card-bg: #16213e;
    --text-color: #e6e6e6;
    --text-muted: #a0a0a0;
    --border-color: #444;
    --sidebar-bg: #0f3460;
    --sidebar-text: #ffffff;
    --header-bg: #16213e;
    --header-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    direction: rtl;
    overflow-x: hidden;
}

/* حاوي لوحة التحكم */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    transition: var(--transition);
    overflow-y: auto;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 700;
}

.logo i {
    font-size: 1.5rem;
    margin-left: 0.5rem;
    color: var(--secondary-color);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--sidebar-text);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* التنقل في الشريط الجانبي */
.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    margin: 0.2rem 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition);
    border-right: 3px solid transparent;
}

.nav-item a:hover,
.nav-item.active a {
    background: rgba(255, 255, 255, 0.1);
    border-right-color: var(--secondary-color);
}

.nav-item i {
    font-size: 1.1rem;
    margin-left: 0.8rem;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed .nav-item span {
    display: none;
}

/* فوتر الشريط الجانبي */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.8rem;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.7;
}

.logout-btn {
    width: 100%;
    background: rgba(231, 76, 60, 0.2);
    color: var(--accent-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
    padding: 0.8rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logout-btn:hover {
    background: var(--accent-color);
    color: white;
}

.logout-btn i {
    margin-left: 0.5rem;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    transition: var(--transition);
}

.sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* الهيدر العلوي */
.top-header {
    background: var(--header-bg);
    padding: 1rem 2rem;
    box-shadow: var(--header-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    margin-left: 1rem;
    border-radius: 4px;
    transition: var(--transition);
    display: none;
}

.menu-toggle:hover {
    background: var(--border-color);
}

#pageTitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* صندوق البحث */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    padding: 0.8rem 1rem 0.8rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--card-bg);
    color: var(--text-color);
    width: 300px;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-box i {
    position: absolute;
    right: 1rem;
    color: var(--text-muted);
}

/* أزرار الهيدر */
.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.action-btn:hover {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.notification-badge {
    position: absolute;
    top: -5px;
    left: -5px;
    background: var(--accent-color);
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* منطقة المحتوى */
.content-area {
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.customers { background: var(--secondary-color); }
.stat-icon.vehicles { background: var(--success-color); }
.stat-icon.certificates { background: var(--warning-color); }
.stat-icon.revenue { background: var(--info-color); }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.stat-content p {
    color: var(--text-muted);
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
}

.stat-change.positive {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.stat-change.negative {
    background: rgba(231, 76, 60, 0.1);
    color: var(--accent-color);
}

/* شبكة الرسوم البيانية */
.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.chart-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-header h3 {
    color: var(--text-color);
    font-weight: 600;
}

.chart-controls select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--card-bg);
    color: var(--text-color);
}

.chart-content {
    padding: 1.5rem;
    height: 300px;
}

/* قسم الأنشطة */
.activity-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
}

.activity-card,
.quick-actions {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

.activity-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.activity-header h3 {
    color: var(--text-color);
    font-weight: 600;
}

.view-all {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.view-all:hover {
    text-decoration: underline;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 0.9rem;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.2rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* الإجراءات السريعة */
.quick-actions h3 {
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.actions-grid {
    display: grid;
    gap: 0.8rem;
}

.quick-action-btn {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    text-align: right;
    color: var(--text-color);
}

.quick-action-btn:hover {
    border-color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.05);
}

.quick-action-btn i {
    font-size: 1.2rem;
    margin-left: 0.8rem;
    color: var(--secondary-color);
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    color: var(--text-color);
    font-weight: 600;
}

.close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0.5rem;
}

.close:hover {
    color: var(--text-color);
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .search-box input {
        width: 200px;
    }
    
    .charts-grid,
    .activity-section {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .header-right {
        gap: 0.5rem;
    }
    
    .search-box {
        display: none;
    }
}
