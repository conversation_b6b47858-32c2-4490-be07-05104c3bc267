// حماية شاملة للصفحات - Page Protector
// يطبق على جميع الصفحات تلقائياً

(function() {
    'use strict';
    
    // معلومات الدعم
    const SUPPORT_INFO = {
        phone: '0696924176',
        whatsapp: '213696924176',
        email: '<EMAIL>'
    };
    
    // الصفحات المستثناة من الحماية
    const EXCLUDED_PAGES = [
        'new-login.html',
        'activation-request.html',
        'test-login.html'
    ];
    
    // التحقق من الحاجة لتطبيق الحماية
    function shouldApplyProtection() {
        const currentPath = window.location.pathname;
        return !EXCLUDED_PAGES.some(page => currentPath.includes(page));
    }
    
    // التحقق من صحة الجلسة
    function isValidSession() {
        try {
            const sessionData = localStorage.getItem('userSession');
            if (!sessionData) {
                console.log('🔍 لا توجد جلسة محفوظة');
                return false;
            }

            const session = JSON.parse(sessionData);
            console.log('🔍 فحص الجلسة:', session);

            // التحقق من البيانات الأساسية
            if (!session.isValid || !session.loginTime) {
                console.log('❌ جلسة غير صالحة - بيانات مفقودة');
                return false;
            }

            // التحقق من نوع المستخدم
            if (!session.userType || (session.userType !== 'admin' && session.userType !== 'customer')) {
                console.log('❌ نوع مستخدم غير صالح:', session.userType);
                return false;
            }

            // التحقق من انتهاء الصلاحية (24 ساعة)
            const now = new Date().getTime();
            const loginTime = new Date(session.loginTime).getTime();
            const maxAge = 24 * 60 * 60 * 1000;

            if ((now - loginTime) > maxAge) {
                console.log('❌ انتهت صلاحية الجلسة');
                localStorage.removeItem('userSession');
                return false;
            }

            console.log('✅ الجلسة صالحة:', session.userType);
            return true;

        } catch (error) {
            console.error('❌ خطأ في التحقق من الجلسة:', error);
            localStorage.removeItem('userSession');
            return false;
        }
    }
    
    // إعادة التوجيه لتسجيل الدخول
    function redirectToLogin() {
        const currentPath = window.location.pathname;
        let loginPath = '';
        
        if (currentPath.includes('/components/admin/')) {
            loginPath = '../../auth/new-login.html';
        } else if (currentPath.includes('/components/')) {
            loginPath = '../auth/new-login.html';
        } else if (currentPath.includes('/utils/')) {
            loginPath = '../auth/new-login.html';
        } else {
            loginPath = 'src/auth/new-login.html';
        }
        
        console.log('🔄 إعادة توجيه لتسجيل الدخول');
        window.location.href = loginPath;
    }
    
    // إظهار رسالة الحماية
    function showProtectionMessage() {
        // إنشاء overlay
        const overlay = document.createElement('div');
        overlay.id = 'protection-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
            direction: rtl;
        `;
        
        // إنشاء محتوى الرسالة
        const messageBox = document.createElement('div');
        messageBox.style.cssText = `
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-width: 500px;
            text-align: center;
            animation: slideIn 0.5s ease;
        `;
        
        messageBox.innerHTML = `
            <div style="color: #e74c3c; font-size: 4rem; margin-bottom: 1rem;">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h2 style="color: #2c3e50; margin-bottom: 1rem;">🔒 صفحة محمية</h2>
            <p style="color: #7f8c8d; margin-bottom: 2rem; line-height: 1.6;">
                هذه الصفحة محمية وتتطلب تسجيل الدخول أولاً.<br>
                سيتم إعادة توجيهك لصفحة تسجيل الدخول.
            </p>
            
            <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
                <h4 style="color: #2c3e50; margin-bottom: 1rem;">📞 للدعم الفني:</h4>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="tel:${SUPPORT_INFO.phone}" style="
                        display: flex; align-items: center; gap: 0.5rem;
                        background: #27ae60; color: white; padding: 0.8rem 1.2rem;
                        border-radius: 8px; text-decoration: none; font-weight: 600;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='#229954'" onmouseout="this.style.background='#27ae60'">
                        <i class="fas fa-phone"></i>
                        ${SUPPORT_INFO.phone}
                    </a>
                    <a href="https://wa.me/${SUPPORT_INFO.whatsapp}" target="_blank" style="
                        display: flex; align-items: center; gap: 0.5rem;
                        background: #25d366; color: white; padding: 0.8rem 1.2rem;
                        border-radius: 8px; text-decoration: none; font-weight: 600;
                        transition: all 0.3s ease;
                    " onmouseover="this.style.background='#20ba5a'" onmouseout="this.style.background='#25d366'">
                        <i class="fab fa-whatsapp"></i>
                        واتساب
                    </a>
                </div>
            </div>
            
            <div style="display: flex; gap: 1rem; justify-content: center;">
                <button onclick="window.location.reload()" style="
                    background: #3498db; color: white; border: none;
                    padding: 1rem 2rem; border-radius: 8px; cursor: pointer;
                    font-weight: 600; transition: all 0.3s ease;
                " onmouseover="this.style.background='#2980b9'" onmouseout="this.style.background='#3498db'">
                    <i class="fas fa-sync-alt"></i>
                    إعادة المحاولة
                </button>
                <button onclick="redirectToLogin()" style="
                    background: #e74c3c; color: white; border: none;
                    padding: 1rem 2rem; border-radius: 8px; cursor: pointer;
                    font-weight: 600; transition: all 0.3s ease;
                " onmouseover="this.style.background='#c0392b'" onmouseout="this.style.background='#e74c3c'">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </div>
        `;
        
        // إضافة الأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateY(-50px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
        
        overlay.appendChild(messageBox);
        document.body.appendChild(overlay);
        
        // إعادة التوجيه التلقائي بعد 5 ثوان
        setTimeout(() => {
            redirectToLogin();
        }, 5000);
        
        // إضافة دالة إعادة التوجيه للنافذة
        window.redirectToLogin = redirectToLogin;
    }
    
    // حماية المحتوى
    function protectContent() {
        // إخفاء المحتوى
        document.body.style.visibility = 'hidden';
        
        // منع النقر بالزر الأيمن
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            return false;
        });
        
        // منع اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u') ||
                (e.ctrlKey && e.key === 's')) {
                e.preventDefault();
                return false;
            }
        });
        
        // منع تحديد النص
        document.addEventListener('selectstart', (e) => {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                return false;
            }
        });
    }
    
    // تطبيق الحماية
    function applyProtection() {
        if (!shouldApplyProtection()) {
            console.log('🟢 صفحة مستثناة من الحماية');
            document.body.style.visibility = 'visible';
            return;
        }

        console.log('🛡️ تطبيق حماية الصفحة...');

        // حماية المحتوى فوراً
        protectContent();

        // التحقق من الجلسة مع تأخير للسماح بحفظ الجلسة
        setTimeout(() => {
            console.log('🔍 بدء فحص الجلسة...');
            if (!isValidSession()) {
                console.log('❌ جلسة غير صالحة، إظهار رسالة الحماية');
                showProtectionMessage();
            } else {
                console.log('✅ جلسة صالحة، إظهار المحتوى');
                document.body.style.visibility = 'visible';

                // مراقبة تغييرات الجلسة
                setInterval(() => {
                    if (!isValidSession()) {
                        console.log('⚠️ انتهت صلاحية الجلسة');
                        showProtectionMessage();
                    }
                }, 30000); // فحص كل 30 ثانية
            }
        }, 500); // تأخير 500ms للسماح بحفظ الجلسة
    }
    
    // مراقبة تغييرات localStorage من نوافذ أخرى
    window.addEventListener('storage', function(e) {
        if (e.key === 'userSession') {
            console.log('🔄 تم تحديث الجلسة من نافذة أخرى');
            if (e.newValue) {
                // تم إنشاء أو تحديث جلسة
                setTimeout(() => {
                    if (isValidSession()) {
                        console.log('✅ جلسة جديدة صالحة، إظهار المحتوى');
                        document.body.style.visibility = 'visible';
                    }
                }, 100);
            } else {
                // تم حذف الجلسة
                console.log('❌ تم حذف الجلسة');
                showProtectionMessage();
            }
        }
    });

    // تطبيق الحماية عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyProtection);
    } else {
        applyProtection();
    }

    // تطبيق الحماية فوراً أيضاً
    applyProtection();
    
})();
