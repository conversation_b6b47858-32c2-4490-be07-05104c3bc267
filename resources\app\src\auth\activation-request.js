// متغيرات عامة
let isLoading = false;
let machineInfo = null;

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeActivationPage();
    loadMachineInfo();
    populateStates();
    setupEventListeners();
});

// تهيئة صفحة طلب التفعيل
async function initializeActivationPage() {
    try {
        // تهيئة نظام إدارة التراخيص
        if (window.licenseManager) {
            await window.licenseManager.initialize();
        }
        
        // إضافة تأثيرات بصرية
        addVisualEffects();
        
        console.log('تم تهيئة صفحة طلب التفعيل بنجاح');
    } catch (error) {
        console.error('خطأ في تهيئة صفحة طلب التفعيل:', error);
        showError('حدث خطأ في تهيئة الصفحة');
    }
}

// تحميل معلومات الجهاز
async function loadMachineInfo() {
    try {
        if (window.licenseManager) {
            machineInfo = await window.licenseManager.getMachineInfo();
            
            document.getElementById('machineId').textContent = machineInfo.machineId || 'غير متوفر';
            document.getElementById('platform').textContent = machineInfo.platform || 'غير متوفر';
        } else {
            // معلومات أساسية للمتصفح
            document.getElementById('machineId').textContent = 'BROWSER-' + Date.now().toString(36);
            document.getElementById('platform').textContent = navigator.platform || 'غير متوفر';
        }
    } catch (error) {
        console.error('خطأ في تحميل معلومات الجهاز:', error);
        document.getElementById('machineId').textContent = 'خطأ في التحميل';
        document.getElementById('platform').textContent = 'خطأ في التحميل';
    }
}

// ملء قائمة الولايات
function populateStates() {
    const stateSelect = document.getElementById('state');
    
    // مسح الخيارات الموجودة (عدا الخيار الافتراضي)
    while (stateSelect.children.length > 1) {
        stateSelect.removeChild(stateSelect.lastChild);
    }
    
    // إضافة الولايات
    Object.entries(ALGERIAN_STATES).forEach(([code, name]) => {
        const option = document.createElement('option');
        option.value = code;
        option.textContent = `${code} - ${name}`;
        stateSelect.appendChild(option);
    });
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    const form = document.getElementById('activationForm');
    const inputs = form.querySelectorAll('input, select, textarea');
    
    // معالج إرسال النموذج
    form.addEventListener('submit', handleFormSubmit);
    
    // إزالة رسائل الخطأ عند الكتابة
    inputs.forEach(input => {
        input.addEventListener('input', clearFieldError);
        input.addEventListener('change', clearFieldError);
    });
    
    // التحقق من رقم الهاتف أثناء الكتابة
    const phoneInput = document.getElementById('phoneNumber');
    phoneInput.addEventListener('input', formatPhoneNumber);
    
    // إغلاق النوافذ المنبثقة بالضغط خارجها
    window.addEventListener('click', handleModalClose);
}

// معالج إرسال النموذج
async function handleFormSubmit(e) {
    e.preventDefault();
    
    if (isLoading) return;
    
    // جمع بيانات النموذج
    const formData = collectFormData();
    
    // التحقق من صحة البيانات
    if (!validateFormData(formData)) {
        return;
    }
    
    // بدء عملية الإرسال
    setLoading(true);
    clearMessages();
    
    try {
        // إرسال طلب التفعيل
        const result = await submitActivationRequest(formData);
        
        if (result.success) {
            showSuccess(`تم إرسال طلب التفعيل بنجاح!<br>رقم الطلب: ${result.requestId}<br>سيتم التواصل معك قريباً.`);
            
            // إعادة تعيين النموذج بعد 3 ثوان
            setTimeout(() => {
                document.getElementById('activationForm').reset();
                populateStates(); // إعادة ملء الولايات
            }, 3000);
        } else {
            showError(result.error || 'فشل في إرسال طلب التفعيل');
        }
    } catch (error) {
        console.error('خطأ في إرسال طلب التفعيل:', error);
        showError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
    } finally {
        setLoading(false);
    }
}

// جمع بيانات النموذج
function collectFormData() {
    return {
        customerName: document.getElementById('customerName').value.trim(),
        phoneNumber: document.getElementById('phoneNumber').value.trim(),
        state: document.getElementById('state').value,
        licenseType: document.querySelector('input[name="licenseType"]:checked').value,
        notes: document.getElementById('notes').value.trim(),
        acceptTerms: document.getElementById('acceptTerms').checked,
        machineId: machineInfo ? machineInfo.machineId : 'unknown'
    };
}

// التحقق من صحة بيانات النموذج
function validateFormData(data) {
    let isValid = true;
    
    // التحقق من الاسم
    if (!data.customerName) {
        showFieldError('customerNameError', 'يرجى إدخال الاسم الكامل');
        isValid = false;
    } else if (data.customerName.length < 3) {
        showFieldError('customerNameError', 'الاسم يجب أن يكون 3 أحرف على الأقل');
        isValid = false;
    }
    
    // التحقق من رقم الهاتف
    if (!data.phoneNumber) {
        showFieldError('phoneNumberError', 'يرجى إدخال رقم الهاتف');
        isValid = false;
    } else if (!isValidAlgerianPhone(data.phoneNumber)) {
        showFieldError('phoneNumberError', 'رقم الهاتف غير صحيح (مثال: 0555123456)');
        isValid = false;
    }
    
    // التحقق من الولاية
    if (!data.state) {
        showFieldError('stateError', 'يرجى اختيار الولاية');
        isValid = false;
    }
    
    // التحقق من قبول الشروط
    if (!data.acceptTerms) {
        showFieldError('termsError', 'يجب الموافقة على شروط الاستخدام');
        isValid = false;
    }
    
    return isValid;
}

// التحقق من صحة رقم الهاتف الجزائري
function isValidAlgerianPhone(phone) {
    // إزالة المسافات والرموز
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    
    // التحقق من الأنماط المختلفة
    const patterns = [
        /^0[5-7]\d{8}$/,           // 0XXXXXXXX (10 أرقام)
        /^\+213[5-7]\d{8}$/,      // +213XXXXXXXX
        /^213[5-7]\d{8}$/,        // 213XXXXXXXX
        /^[5-7]\d{8}$/            // XXXXXXXX (9 أرقام)
    ];
    
    return patterns.some(pattern => pattern.test(cleanPhone));
}

// تنسيق رقم الهاتف أثناء الكتابة
function formatPhoneNumber(e) {
    let value = e.target.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام
    
    // تنسيق الرقم
    if (value.length > 0) {
        if (value.startsWith('0')) {
            // تنسيق للأرقام التي تبدأ بـ 0
            if (value.length > 4) {
                value = value.substring(0, 4) + ' ' + value.substring(4);
            }
            if (value.length > 8) {
                value = value.substring(0, 8) + ' ' + value.substring(8);
            }
        }
    }
    
    e.target.value = value;
}

// إرسال طلب التفعيل
async function submitActivationRequest(formData) {
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    try {
        if (window.licenseManager) {
            return await window.licenseManager.createActivationRequest(formData);
        } else {
            // حفظ محلي كبديل
            const request = {
                id: 'REQ-' + Date.now().toString(36),
                ...formData,
                status: 'pending',
                createdAt: new Date().toISOString()
            };
            
            // حفظ في localStorage
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            requests.push(request);
            localStorage.setItem('activationRequests', JSON.stringify(requests));
            
            return { success: true, requestId: request.id };
        }
    } catch (error) {
        console.error('خطأ في إرسال الطلب:', error);
        return { success: false, error: 'فشل في إرسال الطلب' };
    }
}

// إدارة حالة التحميل
function setLoading(loading) {
    isLoading = loading;
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const spinner = submitBtn.querySelector('.loading-spinner');
    
    if (loading) {
        btnText.style.opacity = '0';
        spinner.style.display = 'block';
        submitBtn.disabled = true;
    } else {
        btnText.style.opacity = '1';
        spinner.style.display = 'none';
        submitBtn.disabled = false;
    }
}

// إظهار رسالة خطأ
function showError(message) {
    const errorElement = document.getElementById('mainError');
    errorElement.innerHTML = message;
    errorElement.style.display = 'block';
    
    // التمرير إلى الرسالة
    errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// إظهار رسالة نجاح
function showSuccess(message) {
    const successElement = document.getElementById('successMessage');
    successElement.innerHTML = message;
    successElement.style.display = 'block';
    
    // التمرير إلى الرسالة
    successElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// إظهار خطأ في حقل معين
function showFieldError(fieldId, message) {
    const errorElement = document.getElementById(fieldId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
}

// مسح الرسائل
function clearMessages() {
    document.getElementById('mainError').style.display = 'none';
    document.getElementById('successMessage').style.display = 'none';
}

// مسح خطأ حقل معين
function clearFieldError(e) {
    const fieldName = e.target.name || e.target.id;
    const errorElement = document.getElementById(fieldName + 'Error');
    if (errorElement) {
        errorElement.style.display = 'none';
    }
}

// إظهار شروط الاستخدام
function showTerms() {
    document.getElementById('termsModal').style.display = 'flex';
}

// إغلاق شروط الاستخدام
function closeTerms() {
    document.getElementById('termsModal').style.display = 'none';
}

// إظهار سياسة الخصوصية
function showPrivacy() {
    document.getElementById('privacyModal').style.display = 'flex';
}

// إغلاق سياسة الخصوصية
function closePrivacy() {
    document.getElementById('privacyModal').style.display = 'none';
}

// إغلاق النوافذ المنبثقة بالضغط خارجها
function handleModalClose(e) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// إضافة تأثيرات بصرية
function addVisualEffects() {
    // تأثير الظهور التدريجي
    const activationCard = document.querySelector('.activation-card');
    activationCard.style.opacity = '0';
    activationCard.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        activationCard.style.transition = 'all 0.6s ease';
        activationCard.style.opacity = '1';
        activationCard.style.transform = 'translateY(0)';
    }, 100);
    
    // تأثير التركيز على الحقول
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
    
    // تأثير على خيارات الترخيص
    const licenseLabels = document.querySelectorAll('.license-label');
    licenseLabels.forEach(label => {
        label.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
        });
        
        label.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
}

// دالة مساعدة للحصول على اسم الولاية
function getStateName(stateCode) {
    return ALGERIAN_STATES[stateCode] || 'غير محدد';
}

// دالة مساعدة لتنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}
