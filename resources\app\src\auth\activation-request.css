/* إعدادات عامة */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 12px;
    --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    /* ألوان الوضع الفاتح */
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-bg: rgba(255, 255, 255, 0.95);
    --text-color: #2c3e50;
    --input-bg: #ffffff;
    --input-border: #ddd;
    --button-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --button-text: #ffffff;
}

/* الوضع المظلم */
body.dark-mode {
    --bg-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --card-bg: rgba(22, 33, 62, 0.95);
    --text-color: #e6e6e6;
    --input-bg: #2d2d2d;
    --input-border: #555;
    --button-bg: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    overflow-x: hidden;
    min-height: 100vh;
}

.activation-container {
    min-height: 100vh;
    background: var(--bg-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 2rem 1rem;
}

/* خلفية متحركة */
.background-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* بطاقة طلب التفعيل */
.activation-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    width: 100%;
    max-width: 600px;
    position: relative;
    z-index: 2;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-height: 90vh;
    overflow-y: auto;
}

/* هيدر طلب التفعيل */
.activation-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo {
    width: 80px;
    height: 80px;
    background: var(--button-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.logo i {
    font-size: 2rem;
    color: white;
}

.activation-header h1 {
    color: var(--text-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.subtitle {
    color: var(--text-color);
    opacity: 0.7;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.version {
    background: var(--button-bg);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
}

/* معلومات الجهاز */
.machine-info {
    background: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 2rem;
}

.info-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
    color: var(--secondary-color);
    font-weight: 600;
}

.info-header i {
    margin-left: 0.5rem;
}

.info-content {
    display: grid;
    gap: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item .label {
    color: var(--text-color);
    font-weight: 500;
}

.info-item .value {
    color: var(--text-color);
    font-family: monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

/* أقسام النموذج */
.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    color: var(--text-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.form-section h3 i {
    margin-left: 0.5rem;
    color: var(--secondary-color);
}

/* مجموعات النموذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group label i {
    margin-left: 0.5rem;
    color: var(--secondary-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 2px solid var(--input-border);
    border-radius: 8px;
    background: var(--input-bg);
    color: var(--text-color);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* خيارات الترخيص */
.license-options {
    display: grid;
    gap: 1rem;
}

.license-option {
    position: relative;
}

.license-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.license-label {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 2px solid var(--input-border);
    border-radius: var(--border-radius);
    background: var(--input-bg);
    cursor: pointer;
    transition: var(--transition);
}

.license-option input[type="radio"]:checked + .license-label {
    border-color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.1);
}

.license-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.2rem;
    color: white;
}

.license-icon.trial {
    background: var(--warning-color);
}

.license-icon.lifetime {
    background: var(--success-color);
}

.license-details {
    flex: 1;
}

.license-details h4 {
    color: var(--text-color);
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
}

.license-details p {
    color: var(--text-color);
    opacity: 0.7;
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.license-price {
    color: var(--secondary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

/* قسم الشروط */
.terms-section {
    margin: 1.5rem 0;
}

.terms-checkbox {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.4;
}

.terms-checkbox input {
    margin-left: 0.5rem;
    margin-top: 0.2rem;
}

.terms-checkbox a {
    color: var(--secondary-color);
    text-decoration: none;
}

.terms-checkbox a:hover {
    text-decoration: underline;
}

/* زر الإرسال */
.submit-btn {
    width: 100%;
    background: var(--button-bg);
    color: var(--button-text);
    border: none;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    margin-bottom: 1rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* رسائل الحالة */
.message-container {
    margin-top: 1rem;
}

.error-message, .success-message {
    padding: 0.8rem;
    border-radius: 6px;
    font-size: 0.9rem;
    text-align: center;
    margin-bottom: 0.5rem;
}

.error-message {
    background: rgba(231, 76, 60, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.success-message {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(39, 174, 96, 0.3);
}

/* معلومات الاتصال */
.contact-info {
    background: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.3);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 2rem;
}

.contact-info h4 {
    color: var(--info-color);
    margin-bottom: 0.8rem;
    display: flex;
    align-items: center;
}

.contact-info h4 i {
    margin-left: 0.5rem;
}

.contact-details {
    display: grid;
    gap: 0.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-size: 0.9rem;
}

.contact-item i {
    margin-left: 0.5rem;
    color: var(--info-color);
    width: 16px;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--input-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    color: var(--text-color);
    font-weight: 600;
}

.close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0.5rem;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 1.5rem;
}

.terms-content h4,
.privacy-content h4 {
    color: var(--text-color);
    margin: 1rem 0 0.5rem 0;
    font-size: 1rem;
}

.terms-content p,
.privacy-content p {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--input-border);
    text-align: center;
}

.modal-btn {
    background: var(--button-bg);
    color: var(--button-text);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.modal-btn:hover {
    transform: translateY(-2px);
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .activation-card {
        margin: 1rem;
        padding: 1.5rem;
        max-height: 95vh;
    }
    
    .activation-header h1 {
        font-size: 1.5rem;
    }
    
    .license-options {
        gap: 0.8rem;
    }
    
    .license-label {
        padding: 0.8rem;
    }
    
    .license-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.3rem;
    }
    
    .info-item .value {
        width: 100%;
        text-align: center;
    }
}

/* أزرار الاتصال السريع */
.quick-contact {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

.contact-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.phone-btn {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.phone-btn:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.whatsapp-btn {
    background: linear-gradient(135deg, #25d366, #20ba5a);
    color: white;
}

.whatsapp-btn:hover {
    background: linear-gradient(135deg, #20ba5a, #1da851);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.contact-btn i {
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .quick-contact {
        flex-direction: column;
        gap: 0.8rem;
    }

    .contact-btn {
        justify-content: center;
    }
}
