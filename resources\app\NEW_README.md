# مؤسسة وقود المستقبل - نظام الإدارة المحدث

## 🚀 نظرة عامة

نظام إدارة شامل ومتطور لمحطة الغاز يشمل إدارة العملاء، المركبات، الشهادات، والمخزون. تم تطويره باستخدام تقنيات حديثة لضمان الأداء والأمان.

### ✨ الميزات الجديدة في الإصدار 3.0.0

- 🎨 **واجهة مستخدم محدثة**: تصميم عصري وسهل الاستخدام
- 🔐 **نظام أمان محسن**: تشفير البيانات وإدارة الجلسات
- 📊 **لوحة تحكم تفاعلية**: إحصائيات ورسوم بيانية في الوقت الفعلي
- 🗄️ **نظام قاعدة بيانات محسن**: أداء أفضل وموثوقية عالية
- 🌙 **الوضع المظلم**: دعم كامل للمظهر المظلم
- 📱 **تصميم متجاوب**: يعمل على جميع أحجام الشاشات
- 🔄 **نسخ احتياطية تلقائية**: حماية البيانات المتقدمة

## 🏗️ هيكل المشروع الجديد

```
resources/app/
├── src/
│   ├── auth/                    # نظام المصادقة
│   │   ├── new-login.html       # واجهة تسجيل الدخول الجديدة
│   │   ├── new-login.css        # تنسيقات تسجيل الدخول
│   │   └── new-login.js         # منطق تسجيل الدخول
│   ├── components/              # مكونات الواجهة
│   │   └── dashboard/           # لوحة التحكم
│   │       ├── dashboard.html   # واجهة لوحة التحكم
│   │       ├── dashboard.css    # تنسيقات لوحة التحكم
│   │       └── dashboard.js     # منطق لوحة التحكم
│   ├── utils/                   # أدوات مساعدة
│   │   ├── database.js          # نظام إدارة البيانات
│   │   ├── validation.js        # التحقق من صحة البيانات
│   │   └── constants.js         # الثوابت والإعدادات
│   └── assets/                  # الأصول الثابتة
├── main.js                      # الملف الرئيسي المحسن
├── package.json                 # إعدادات المشروع المحدثة
└── README.md                    # هذا الملف
```

## 🛠️ التقنيات المستخدمة

- **Electron 32+**: لتطوير تطبيق سطح المكتب
- **HTML5/CSS3**: لبناء الواجهة
- **JavaScript ES6+**: للمنطق والتفاعل
- **Chart.js**: للرسوم البيانية
- **Font Awesome 6**: للأيقونات
- **Better SQLite3**: لقاعدة البيانات (قريباً)

## 🚀 التثبيت والتشغيل

### 1. تثبيت التبعيات

```bash
npm install
```

### 2. تشغيل التطبيق

```bash
npm start
```

### 3. بناء التطبيق

```bash
npm run build-win
```

## 🔐 بيانات تسجيل الدخول

| اسم المستخدم | كلمة المرور | الدور |
|---------------|-------------|-------|
| admin | admin123 | مدير النظام |
| user | user123 | مستخدم |
| manager | manager123 | مدير فرع |

## 📊 الميزات الرئيسية

### 1. واجهة تسجيل الدخول الجديدة
- تصميم حديث وجذاب
- دعم الوضع المظلم
- تأثيرات بصرية متقدمة
- نظام أمان محسن

### 2. لوحة التحكم التفاعلية
- إحصائيات في الوقت الفعلي
- رسوم بيانية تفاعلية
- إجراءات سريعة
- تنبيهات ذكية

### 3. نظام إدارة البيانات المحسن
- أداء أفضل
- نسخ احتياطية تلقائية
- تشفير البيانات
- استعادة سريعة

## 🎨 المظاهر

- **المظهر الفاتح**: للاستخدام النهاري
- **المظهر المظلم**: مريح للعينين

## 🔧 التحسينات المنجزة

### ✅ تم إنجازه:

1. **تنظيف الملفات**: حذف الملفات المتكررة والغير ضرورية
2. **هيكل مشروع جديد**: تنظيم أفضل للملفات والمجلدات
3. **واجهة تسجيل دخول جديدة**: تصميم عصري مع تأثيرات بصرية
4. **لوحة تحكم شاملة**: إحصائيات ورسوم بيانية تفاعلية
5. **نظام قاعدة بيانات محسن**: إدارة أفضل للبيانات
6. **نظام التحقق من البيانات**: مكتبة شاملة للتحقق
7. **ملف الثوابت**: تنظيم جميع الإعدادات والثوابت
8. **تحسين main.js**: أداء أفضل وميزات جديدة

### 🔄 قيد التطوير:

- إضافة المزيد من الأقسام للوحة التحكم
- تطوير نظام إدارة العملاء
- إضافة نظام التقارير المتقدم
- تحسين نظام النسخ الاحتياطية

## 🚀 الخطوات التالية

1. **إضافة قاعدة بيانات SQLite**: لأداء أفضل
2. **تطوير أقسام إضافية**: العملاء، المركبات، الشهادات
3. **نظام التقارير**: تقارير متقدمة وتصدير
4. **تحسينات الأمان**: تشفير متقدم ومصادقة ثنائية
5. **اختبارات شاملة**: ضمان الجودة والاستقرار

## 📞 الدعم

- **البريد**: <EMAIL>
- **الهاتف**: +966 XX XXX XXXX

## 📄 الترخيص

MIT License

---

**تم تطوير هذا النظام بعناية فائقة لضمان أفضل تجربة مستخدم** 🚀
