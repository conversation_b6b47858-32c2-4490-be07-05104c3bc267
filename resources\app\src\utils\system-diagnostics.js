// نظام التشخيص الشامل للنظام
// System Diagnostics - فحص وتشخيص جميع مكونات النظام

class SystemDiagnostics {
    constructor() {
        this.results = {
            overall: 'unknown',
            tests: [],
            errors: [],
            warnings: [],
            recommendations: []
        };
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;
    }

    // تشغيل التشخيص الشامل
    async runFullDiagnostics() {
        console.log('🔍 بدء التشخيص الشامل للنظام...');
        
        this.results = {
            overall: 'unknown',
            tests: [],
            errors: [],
            warnings: [],
            recommendations: []
        };

        try {
            // فحص البيئة
            await this.checkEnvironment();
            
            // فحص الملفات الأساسية
            await this.checkCoreFiles();
            
            // فحص نظام المصادقة
            await this.checkAuthSystem();
            
            // فحص نظام التراخيص
            await this.checkLicenseSystem();
            
            // فحص قاعدة البيانات
            await this.checkDatabase();
            
            // فحص نظام الحماية
            await this.checkSecuritySystem();
            
            // فحص الواجهات
            await this.checkUserInterfaces();
            
            // تحديد النتيجة الإجمالية
            this.calculateOverallResult();
            
            console.log('✅ اكتمل التشخيص الشامل');
            return this.results;
            
        } catch (error) {
            console.error('❌ خطأ في التشخيص:', error);
            this.addError('فشل في إجراء التشخيص الشامل', error.message);
            this.results.overall = 'critical';
            return this.results;
        }
    }

    // فحص البيئة
    async checkEnvironment() {
        console.log('🌍 فحص البيئة...');
        
        // فحص المتصفح
        if (typeof window === 'undefined') {
            this.addError('بيئة غير صالحة', 'لا يمكن الوصول لكائن window');
            return;
        }
        
        this.addTest('فحص البيئة', 'success', 'البيئة صالحة');
        
        // فحص localStorage
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            this.addTest('localStorage', 'success', 'يعمل بشكل صحيح');
        } catch (error) {
            this.addError('localStorage', 'غير متوفر أو معطل');
        }
        
        // فحص Electron
        if (this.isElectron) {
            this.addTest('Electron', 'success', 'يعمل في بيئة Electron');
        } else {
            this.addWarning('Electron', 'يعمل في المتصفح العادي');
        }
        
        // فحص Font Awesome
        const fontAwesome = document.querySelector('link[href*="font-awesome"]');
        if (fontAwesome) {
            this.addTest('Font Awesome', 'success', 'محمل بشكل صحيح');
        } else {
            this.addWarning('Font Awesome', 'قد لا يكون محمل');
        }
    }

    // فحص الملفات الأساسية
    async checkCoreFiles() {
        console.log('📁 فحص الملفات الأساسية...');
        
        const coreFiles = [
            'src/auth/new-login.html',
            'src/auth/activation-request.html',
            'src/components/dashboard/dashboard.html',
            'src/components/admin/license-management.html'
        ];
        
        for (const file of coreFiles) {
            try {
                const response = await fetch(file);
                if (response.ok) {
                    this.addTest(`ملف ${file}`, 'success', 'موجود ويمكن الوصول إليه');
                } else {
                    this.addError(`ملف ${file}`, `غير متوفر (${response.status})`);
                }
            } catch (error) {
                this.addError(`ملف ${file}`, 'لا يمكن الوصول إليه');
            }
        }
    }

    // فحص نظام المصادقة
    async checkAuthSystem() {
        console.log('🔐 فحص نظام المصادقة...');
        
        // فحص وجود دوال المصادقة
        const authFunctions = [
            'handleAdminLogin',
            'handleLicenseLogin',
            'validateAdminLogin',
            'validateLicenseLogin'
        ];
        
        for (const func of authFunctions) {
            if (typeof window[func] === 'function') {
                this.addTest(`دالة ${func}`, 'success', 'موجودة ومتاحة');
            } else {
                this.addWarning(`دالة ${func}`, 'غير موجودة أو غير محملة');
            }
        }
        
        // فحص عناصر تسجيل الدخول
        const loginElements = [
            'adminLoginForm',
            'licenseLoginForm',
            'username',
            'password',
            'licenseKey',
            'customerName'
        ];
        
        for (const elementId of loginElements) {
            const element = document.getElementById(elementId);
            if (element) {
                this.addTest(`عنصر ${elementId}`, 'success', 'موجود في الصفحة');
            } else {
                this.addWarning(`عنصر ${elementId}`, 'غير موجود في الصفحة الحالية');
            }
        }
        
        // اختبار تسجيل دخول وهمي
        try {
            const testResult = this.testAdminCredentials('admin', 'admin123');
            if (testResult) {
                this.addTest('بيانات المدير', 'success', 'صحيحة ومطابقة');
            } else {
                this.addError('بيانات المدير', 'غير صحيحة أو متغيرة');
            }
        } catch (error) {
            this.addWarning('اختبار بيانات المدير', 'لا يمكن إجراء الاختبار');
        }
    }

    // فحص نظام التراخيص
    async checkLicenseSystem() {
        console.log('🎫 فحص نظام التراخيص...');
        
        // فحص وجود مدير التراخيص
        if (typeof window.licenseManager === 'object') {
            this.addTest('مدير التراخيص', 'success', 'محمل ومتاح');
            
            // فحص دوال مدير التراخيص
            const licenseManagerFunctions = [
                'initialize',
                'validateLicense',
                'generateLicense',
                'getMachineInfo'
            ];
            
            for (const func of licenseManagerFunctions) {
                if (typeof window.licenseManager[func] === 'function') {
                    this.addTest(`دالة licenseManager.${func}`, 'success', 'متاحة');
                } else {
                    this.addError(`دالة licenseManager.${func}`, 'غير متاحة');
                }
            }
        } else {
            this.addWarning('مدير التراخيص', 'غير محمل أو غير متاح');
        }
        
        // فحص نظام التحقق من الجهاز
        if (typeof window.deviceVerification === 'object') {
            this.addTest('نظام التحقق من الجهاز', 'success', 'محمل ومتاح');
        } else {
            this.addWarning('نظام التحقق من الجهاز', 'غير محمل');
        }
        
        // فحص التراخيص المحفوظة
        try {
            const licenses = JSON.parse(localStorage.getItem('licenses') || '[]');
            this.addTest('التراخيص المحفوظة', 'success', `${licenses.length} ترخيص محفوظ`);
        } catch (error) {
            this.addError('التراخيص المحفوظة', 'خطأ في قراءة البيانات');
        }
    }

    // فحص قاعدة البيانات
    async checkDatabase() {
        console.log('💾 فحص قاعدة البيانات...');
        
        // فحص قاعدة البيانات المحسنة
        if (typeof window.licenseDatabase === 'object') {
            this.addTest('قاعدة البيانات المحسنة', 'success', 'محملة ومتاحة');
        } else {
            this.addWarning('قاعدة البيانات المحسنة', 'غير محملة');
        }
        
        // فحص البيانات المحفوظة
        const dataKeys = ['userSession', 'licenses', 'activationRequests', 'registeredDevices'];
        
        for (const key of dataKeys) {
            try {
                const data = localStorage.getItem(key);
                if (data) {
                    const parsed = JSON.parse(data);
                    const count = Array.isArray(parsed) ? parsed.length : 1;
                    this.addTest(`بيانات ${key}`, 'success', `${count} عنصر محفوظ`);
                } else {
                    this.addWarning(`بيانات ${key}`, 'لا توجد بيانات محفوظة');
                }
            } catch (error) {
                this.addError(`بيانات ${key}`, 'خطأ في قراءة البيانات');
            }
        }
    }

    // فحص نظام الحماية
    async checkSecuritySystem() {
        console.log('🛡️ فحص نظام الحماية...');
        
        // فحص Auth Guard
        if (typeof window.authGuard === 'object') {
            this.addTest('Auth Guard', 'success', 'محمل ومتاح');
        } else {
            this.addWarning('Auth Guard', 'غير محمل');
        }
        
        // فحص Page Protector
        const protectorScript = document.querySelector('script[src*="page-protector"]');
        if (protectorScript) {
            this.addTest('Page Protector', 'success', 'محمل في الصفحة');
        } else {
            this.addWarning('Page Protector', 'غير محمل في الصفحة الحالية');
        }
        
        // فحص الجلسة الحالية
        try {
            const session = localStorage.getItem('userSession');
            if (session) {
                const sessionData = JSON.parse(session);
                if (sessionData.isValid) {
                    this.addTest('الجلسة الحالية', 'success', `مستخدم: ${sessionData.userType}`);
                } else {
                    this.addWarning('الجلسة الحالية', 'جلسة غير صالحة');
                }
            } else {
                this.addWarning('الجلسة الحالية', 'لا توجد جلسة نشطة');
            }
        } catch (error) {
            this.addError('الجلسة الحالية', 'خطأ في قراءة الجلسة');
        }
    }

    // فحص الواجهات
    async checkUserInterfaces() {
        console.log('🎨 فحص الواجهات...');
        
        // فحص CSS
        const cssFiles = document.querySelectorAll('link[rel="stylesheet"]');
        this.addTest('ملفات CSS', 'success', `${cssFiles.length} ملف محمل`);
        
        // فحص JavaScript
        const jsFiles = document.querySelectorAll('script[src]');
        this.addTest('ملفات JavaScript', 'success', `${jsFiles.length} ملف محمل`);
        
        // فحص العناصر التفاعلية
        const buttons = document.querySelectorAll('button');
        const forms = document.querySelectorAll('form');
        const inputs = document.querySelectorAll('input');
        
        this.addTest('العناصر التفاعلية', 'success', 
            `${buttons.length} زر، ${forms.length} نموذج، ${inputs.length} حقل إدخال`);
    }

    // اختبار بيانات المدير
    testAdminCredentials(username, password) {
        return username === 'admin' && password === 'admin123';
    }

    // إضافة نتيجة اختبار
    addTest(name, status, message) {
        this.results.tests.push({
            name: name,
            status: status,
            message: message,
            timestamp: new Date().toISOString()
        });
    }

    // إضافة خطأ
    addError(component, message) {
        this.results.errors.push({
            component: component,
            message: message,
            timestamp: new Date().toISOString()
        });
    }

    // إضافة تحذير
    addWarning(component, message) {
        this.results.warnings.push({
            component: component,
            message: message,
            timestamp: new Date().toISOString()
        });
    }

    // إضافة توصية
    addRecommendation(title, description) {
        this.results.recommendations.push({
            title: title,
            description: description,
            timestamp: new Date().toISOString()
        });
    }

    // حساب النتيجة الإجمالية
    calculateOverallResult() {
        const errorCount = this.results.errors.length;
        const warningCount = this.results.warnings.length;
        const successCount = this.results.tests.filter(t => t.status === 'success').length;
        
        if (errorCount > 5) {
            this.results.overall = 'critical';
        } else if (errorCount > 2) {
            this.results.overall = 'poor';
        } else if (warningCount > 5) {
            this.results.overall = 'fair';
        } else if (successCount > 10) {
            this.results.overall = 'excellent';
        } else {
            this.results.overall = 'good';
        }
        
        // إضافة توصيات
        if (errorCount > 0) {
            this.addRecommendation('إصلاح الأخطاء', 'يوجد أخطاء تحتاج لإصلاح فوري');
        }
        
        if (warningCount > 3) {
            this.addRecommendation('مراجعة التحذيرات', 'يوجد تحذيرات تحتاج لمراجعة');
        }
        
        if (!this.isElectron) {
            this.addRecommendation('تشغيل في Electron', 'للحصول على أفضل أداء');
        }
    }

    // تصدير النتائج
    exportResults() {
        return {
            ...this.results,
            exportedAt: new Date().toISOString(),
            systemInfo: {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                isElectron: this.isElectron
            }
        };
    }

    // عرض النتائج في Console
    displayResults() {
        console.group('📊 نتائج التشخيص الشامل');
        
        console.log(`🎯 النتيجة الإجمالية: ${this.getOverallStatusEmoji()} ${this.results.overall}`);
        console.log(`✅ اختبارات ناجحة: ${this.results.tests.filter(t => t.status === 'success').length}`);
        console.log(`❌ أخطاء: ${this.results.errors.length}`);
        console.log(`⚠️ تحذيرات: ${this.results.warnings.length}`);
        
        if (this.results.errors.length > 0) {
            console.group('❌ الأخطاء:');
            this.results.errors.forEach(error => {
                console.error(`${error.component}: ${error.message}`);
            });
            console.groupEnd();
        }
        
        if (this.results.warnings.length > 0) {
            console.group('⚠️ التحذيرات:');
            this.results.warnings.forEach(warning => {
                console.warn(`${warning.component}: ${warning.message}`);
            });
            console.groupEnd();
        }
        
        if (this.results.recommendations.length > 0) {
            console.group('💡 التوصيات:');
            this.results.recommendations.forEach(rec => {
                console.info(`${rec.title}: ${rec.description}`);
            });
            console.groupEnd();
        }
        
        console.groupEnd();
    }

    // الحصول على رمز الحالة
    getOverallStatusEmoji() {
        const emojis = {
            excellent: '🟢',
            good: '🔵',
            fair: '🟡',
            poor: '🟠',
            critical: '🔴'
        };
        return emojis[this.results.overall] || '⚪';
    }
}

// إنشاء مثيل واحد للاستخدام العام
const systemDiagnostics = new SystemDiagnostics();

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SystemDiagnostics, systemDiagnostics };
} else {
    window.SystemDiagnostics = SystemDiagnostics;
    window.systemDiagnostics = systemDiagnostics;
}

// إضافة دوال مساعدة للاختبار
window.testHelpers = {
    // اختبار بيانات المدير
    testAdminLogin: function(username, password) {
        return username === 'admin' && password === 'admin123';
    },

    // اختبار localStorage
    testLocalStorage: function() {
        try {
            localStorage.setItem('test', 'test');
            const result = localStorage.getItem('test');
            localStorage.removeItem('test');
            return result === 'test';
        } catch (error) {
            return false;
        }
    },

    // اختبار الجلسة
    testSession: function() {
        try {
            const session = localStorage.getItem('userSession');
            if (!session) return { valid: false, reason: 'لا توجد جلسة' };

            const sessionData = JSON.parse(session);
            if (!sessionData.isValid) return { valid: false, reason: 'جلسة غير صالحة' };

            const now = new Date().getTime();
            const loginTime = new Date(sessionData.loginTime).getTime();
            const maxAge = 24 * 60 * 60 * 1000;

            if ((now - loginTime) > maxAge) {
                return { valid: false, reason: 'انتهت صلاحية الجلسة' };
            }

            return { valid: true, userType: sessionData.userType };
        } catch (error) {
            return { valid: false, reason: 'خطأ في قراءة الجلسة' };
        }
    },

    // اختبار التراخيص
    testLicenses: function() {
        try {
            const licenses = JSON.parse(localStorage.getItem('licenses') || '[]');
            return { count: licenses.length, valid: true };
        } catch (error) {
            return { count: 0, valid: false, error: error.message };
        }
    },

    // اختبار طلبات التفعيل
    testActivationRequests: function() {
        try {
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            return { count: requests.length, valid: true };
        } catch (error) {
            return { count: 0, valid: false, error: error.message };
        }
    }
};
