# دليل نظام إدارة التراخيص - مؤسسة وقود المستقبل

## 🎯 نظرة عامة

تم تطوير نظام إدارة التراخيص المتقدم لمؤسسة وقود المستقبل ليوفر حلاً شاملاً لإدارة تراخيص البرنامج عن بُعد. النظام يضمن أن كل ترخيص يعمل على جهاز واحد فقط مع إمكانيات إدارة متقدمة.

## 🏗️ مكونات النظام

### 1. واجهة العميل (طلب التفعيل)
- **الملف**: `src/auth/activation-request.html`
- **الوظيفة**: يسمح للعملاء بطلب تفعيل البرنامج
- **الميزات**:
  - نموذج شامل لبيانات العميل
  - اختيار نوع الترخيص (تجريبي/مدى الحياة)
  - دعم جميع الولايات الجزائرية (58 ولاية)
  - تحديد تلقائي لمعرف الجهاز

### 2. لوحة تحكم المدير
- **الملف**: `src/components/admin/license-management.html`
- **الوظيفة**: إدارة شاملة للتراخيص وطلبات التفعيل
- **الميزات**:
  - عرض وإدارة طلبات التفعيل
  - إنشاء وإدارة التراخيص
  - إحصائيات ورسوم بيانية
  - تصدير البيانات

### 3. نظام التحقق من الجهاز
- **الملف**: `src/utils/device-verification.js`
- **الوظيفة**: ضمان أن الترخيص يعمل على جهاز واحد فقط
- **التقنيات**:
  - بصمة الجهاز المتقدمة
  - Canvas و WebGL fingerprinting
  - معلومات الأجهزة والشبكة
  - تشفير البيانات

### 4. إدارة التراخيص
- **الملف**: `src/utils/license-manager.js`
- **الوظيفة**: إنشاء وإدارة التراخيص
- **الميزات**:
  - إنشاء مفاتيح ترخيص فريدة
  - التحقق من صحة التراخيص
  - إدارة تواريخ انتهاء الصلاحية

### 5. قاعدة البيانات المحسنة
- **الملف**: `src/utils/license-database.js`
- **الوظيفة**: حفظ وإدارة البيانات
- **الميزات**:
  - تشفير البيانات
  - نسخ احتياطية تلقائية
  - تسجيل العمليات
  - إحصائيات شاملة

## 🚀 كيفية الاستخدام

### للعملاء:

1. **تشغيل البرنامج**:
   ```bash
   npm start
   # أو تشغيل start-license-system.bat
   ```

2. **طلب التفعيل**:
   - ستظهر واجهة طلب التفعيل تلقائياً
   - أدخل بياناتك الشخصية
   - اختر الولاية من القائمة
   - حدد نوع الترخيص المطلوب
   - اضغط "إرسال طلب التفعيل"

3. **انتظار الموافقة**:
   - سيتم إرسال الطلب للمدير
   - ستحصل على رقم طلب للمتابعة
   - انتظر التواصل من فريق الدعم

### للمدير:

1. **تسجيل الدخول**:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

2. **إدارة الطلبات**:
   - عرض جميع طلبات التفعيل
   - فلترة الطلبات حسب الحالة
   - الموافقة أو رفض الطلبات
   - إنشاء تراخيص تلقائياً

3. **إدارة التراخيص**:
   - عرض جميع التراخيص النشطة
   - تعديل أو إلغاء التراخيص
   - مراقبة تواريخ انتهاء الصلاحية
   - إنشاء تراخيص يدوياً

## 🔐 أنواع التراخيص

### 1. تجريبي (30 يوم)
- **المدة**: 30 يوم
- **السعر**: مجاني
- **الاستخدام**: للتجربة والتقييم

### 2. مدى الحياة
- **المدة**: دائم
- **السعر**: حسب الاتفاق
- **الاستخدام**: للاستخدام الدائم

### 3. شهري
- **المدة**: 30 يوم
- **السعر**: حسب الاتفاق
- **التجديد**: شهرياً

### 4. سنوي
- **المدة**: 365 يوم
- **السعر**: حسب الاتفاق
- **التجديد**: سنوياً

## 🛡️ أمان النظام

### 1. التحقق من الجهاز
- بصمة فريدة لكل جهاز
- منع استخدام الترخيص على أجهزة متعددة
- تتبع محاولات الاستخدام غير المصرح بها

### 2. تشفير البيانات
- تشفير جميع البيانات الحساسة
- حماية مفاتيح التراخيص
- تأمين قاعدة البيانات

### 3. النسخ الاحتياطية
- نسخ احتياطية تلقائية يومية
- حفظ في مجلد منفصل
- إمكانية الاستعادة السريعة

## 📊 الإحصائيات والتقارير

### 1. إحصائيات عامة
- إجمالي الطلبات والتراخيص
- التراخيص النشطة والمنتهية
- توزيع أنواع التراخيص

### 2. التقارير
- تقارير شهرية وسنوية
- تحليل استخدام التراخيص
- إحصائيات العملاء حسب الولاية

### 3. التصدير
- تصدير البيانات بصيغة JSON
- تصدير التقارير بصيغة CSV
- نسخ احتياطية شاملة

## 🗂️ هيكل الملفات

```
src/
├── auth/
│   ├── activation-request.html    # واجهة طلب التفعيل
│   ├── activation-request.css     # تنسيقات طلب التفعيل
│   ├── activation-request.js      # منطق طلب التفعيل
│   ├── new-login.html            # واجهة تسجيل الدخول
│   ├── new-login.css             # تنسيقات تسجيل الدخول
│   └── new-login.js              # منطق تسجيل الدخول
├── components/
│   ├── admin/
│   │   ├── license-management.html # لوحة تحكم المدير
│   │   ├── license-management.css  # تنسيقات لوحة التحكم
│   │   └── license-management.js   # منطق لوحة التحكم
│   └── dashboard/
│       ├── dashboard.html         # لوحة التحكم العادية
│       ├── dashboard.css          # تنسيقات لوحة التحكم
│       └── dashboard.js           # منطق لوحة التحكم
└── utils/
    ├── constants.js               # الثوابت والإعدادات
    ├── database.js                # قاعدة البيانات العامة
    ├── device-verification.js     # نظام التحقق من الجهاز
    ├── license-database.js        # قاعدة بيانات التراخيص
    ├── license-manager.js          # إدارة التراخيص
    └── validation.js              # التحقق من البيانات
```

## 🔧 الإعدادات والتخصيص

### 1. إعدادات التراخيص
- تغيير مدة الترخيص التجريبي
- تفعيل/إلغاء التحديث التلقائي
- إعدادات تنبيهات انتهاء الصلاحية

### 2. إعدادات الأمان
- تغيير مفتاح التشفير
- إعدادات النسخ الاحتياطية
- مستوى التحقق من الجهاز

### 3. إعدادات الواجهة
- تغيير المظهر (فاتح/مظلم)
- تخصيص الألوان
- إعدادات اللغة

## 📞 الدعم والصيانة

### 1. ملفات السجلات
- **الموقع**: `%USERPROFILE%\FutureFuelData\license-logs.json`
- **المحتوى**: سجل جميع العمليات
- **الاستخدام**: تتبع المشاكل والأخطاء

### 2. النسخ الاحتياطية
- **الموقع**: `%USERPROFILE%\FutureFuelData\backups\`
- **التكرار**: يومياً تلقائياً
- **الاستعادة**: يدوياً عند الحاجة

### 3. استكشاف الأخطاء
- فحص ملفات السجلات
- التحقق من صحة البيانات
- إعادة تهيئة قاعدة البيانات

## 🚀 التطوير المستقبلي

### 1. ميزات مخططة
- واجهة ويب للإدارة عن بُعد
- تطبيق موبايل للمدير
- تكامل مع أنظمة الدفع
- إشعارات بالبريد الإلكتروني

### 2. تحسينات تقنية
- قاعدة بيانات SQLite
- تشفير متقدم AES
- مصادقة ثنائية
- API للتكامل مع أنظمة أخرى

### 3. تحسينات الأمان
- مراقبة الشبكة
- كشف التلاعب
- تسجيل مفصل للأنشطة
- تنبيهات أمنية فورية

---

**تم تطوير هذا النظام بعناية فائقة لضمان أقصى درجات الأمان والموثوقية** 🔒
