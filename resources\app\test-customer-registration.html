<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار تسجيل الزبون - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .content {
            padding: 2rem;
        }

        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }

        .test-form {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .test-results {
            max-height: 400px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
        }

        .log-entry {
            padding: 0.5rem;
            border-bottom: 1px solid #eee;
            font-family: monospace;
            font-size: 0.8rem;
        }

        .log-entry:last-child {
            border-bottom: none;
        }

        .log-entry.success { color: #27ae60; }
        .log-entry.error { color: #e74c3c; }
        .log-entry.warning { color: #f39c12; }
        .log-entry.info { color: #3498db; }

        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .link-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
            border: 2px solid #e9ecef;
        }

        .link-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
        }

        .link-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #3498db;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار تسجيل الزبون</h1>
            <p>اختبار شامل لنظام تسجيل وإضافة الزبائن</p>
        </div>

        <div class="content">
            <!-- اختبار النظام -->
            <div class="test-section">
                <h3><i class="fas fa-cogs"></i> اختبار النظام</h3>
                <button class="btn btn-primary" onclick="testSystem()">
                    <i class="fas fa-play"></i> اختبار النظام
                </button>
                <button class="btn btn-info" onclick="checkDataManager()">
                    <i class="fas fa-search"></i> فحص نظام البيانات
                </button>
                <button class="btn btn-warning" onclick="clearAllData()">
                    <i class="fas fa-trash"></i> مسح جميع البيانات
                </button>
                <div id="systemStatus" class="status info">
                    جاهز لاختبار النظام
                </div>
            </div>

            <!-- نموذج اختبار تسجيل زبون -->
            <div class="test-section">
                <h3><i class="fas fa-user-plus"></i> اختبار تسجيل زبون جديد</h3>
                <div class="test-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم الزبون:</label>
                            <input type="text" id="testCustomerName" value="أحمد محمد علي">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف:</label>
                            <input type="tel" id="testPhone" value="0555123456">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>الولاية:</label>
                            <select id="testState">
                                <option value="الجزائر">الجزائر</option>
                                <option value="وهران">وهران</option>
                                <option value="قسنطينة">قسنطينة</option>
                                <option value="عنابة">عنابة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>نوع الترخيص:</label>
                            <select id="testLicenseType">
                                <option value="trial">تجريبي</option>
                                <option value="lifetime">مدى الحياة</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn btn-success" onclick="testAddCustomer()">
                        <i class="fas fa-plus"></i> إضافة زبون تجريبي
                    </button>
                    <button class="btn btn-info" onclick="generateRandomCustomer()">
                        <i class="fas fa-random"></i> توليد بيانات عشوائية
                    </button>
                </div>
            </div>

            <!-- اختبار متعدد -->
            <div class="test-section">
                <h3><i class="fas fa-users"></i> اختبار إضافة متعددة</h3>
                <button class="btn btn-success" onclick="addMultipleCustomers(5)">
                    <i class="fas fa-plus-circle"></i> إضافة 5 زبائن
                </button>
                <button class="btn btn-warning" onclick="addMultipleCustomers(10)">
                    <i class="fas fa-plus-circle"></i> إضافة 10 زبائن
                </button>
                <button class="btn btn-danger" onclick="addMultipleCustomers(20)">
                    <i class="fas fa-plus-circle"></i> إضافة 20 زبون
                </button>
            </div>

            <!-- عرض البيانات -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> عرض البيانات المحفوظة</h3>
                <button class="btn btn-primary" onclick="showSavedData()">
                    <i class="fas fa-eye"></i> عرض الطلبات
                </button>
                <button class="btn btn-info" onclick="showStatistics()">
                    <i class="fas fa-chart-bar"></i> عرض الإحصائيات
                </button>
                <div id="dataDisplay" class="status info">
                    اضغط "عرض الطلبات" لرؤية البيانات المحفوظة
                </div>
            </div>

            <!-- سجل الاختبارات -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> سجل الاختبارات</h3>
                <button class="btn btn-warning" onclick="clearLog()">
                    <i class="fas fa-broom"></i> مسح السجل
                </button>
                <div id="testLog" class="test-results">
                    <div class="log-entry info">🧪 جاهز لبدء الاختبارات...</div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="quick-links">
                <a href="src/auth/activation-request-enhanced.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-user-plus"></i></div>
                    <h4>صفحة تسجيل الزبون المحسنة</h4>
                    <p>النموذج الجديد المطور</p>
                </a>
                <a href="src/components/admin/advanced-dashboard.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-tachometer-alt"></i></div>
                    <h4>لوحة التحكم المتقدمة</h4>
                    <p>إدارة الطلبات والتراخيص</p>
                </a>
                <a href="src/auth/login-fixed.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-sign-in-alt"></i></div>
                    <h4>تسجيل دخول المدير</h4>
                    <p>النظام المحسن</p>
                </a>
                <a href="test-fixed-login.html" class="link-card" target="_blank">
                    <div class="link-icon"><i class="fas fa-vial"></i></div>
                    <h4>اختبار النظام الكامل</h4>
                    <p>اختبارات شاملة</p>
                </a>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="src/utils/advanced-data-manager.js"></script>
    <script>
        // متغيرات النظام
        let testCounter = 0;

        // إضافة سجل
        function addLog(message, type = 'info') {
            testCounter++;
            const logContainer = document.getElementById('testLog');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${testCounter}. ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // اختبار النظام
        function testSystem() {
            addLog('🧪 بدء اختبار النظام الشامل...', 'info');
            
            const statusDiv = document.getElementById('systemStatus');
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '🔄 جاري اختبار النظام...';
            
            let results = '';
            
            // اختبار localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                results += '✅ localStorage يعمل بشكل صحيح<br>';
                addLog('✅ localStorage يعمل بشكل صحيح', 'success');
            } catch (error) {
                results += '❌ مشكلة في localStorage<br>';
                addLog('❌ مشكلة في localStorage: ' + error.message, 'error');
            }
            
            // اختبار نظام إدارة البيانات
            if (window.advancedDataManager) {
                results += '✅ نظام إدارة البيانات متوفر<br>';
                addLog('✅ نظام إدارة البيانات متوفر', 'success');
                
                try {
                    const stats = advancedDataManager.getStatistics();
                    results += `📊 الإحصائيات: ${stats.totalRequests} طلب<br>`;
                    addLog(`📊 إجمالي الطلبات: ${stats.totalRequests}`, 'info');
                } catch (error) {
                    results += '⚠️ مشكلة في قراءة الإحصائيات<br>';
                    addLog('⚠️ مشكلة في قراءة الإحصائيات: ' + error.message, 'warning');
                }
            } else {
                results += '❌ نظام إدارة البيانات غير متوفر<br>';
                addLog('❌ نظام إدارة البيانات غير متوفر', 'error');
            }
            
            statusDiv.className = 'status success';
            statusDiv.innerHTML = results;
            addLog('✅ اكتمل اختبار النظام', 'success');
        }

        // فحص نظام البيانات
        function checkDataManager() {
            addLog('🔍 فحص نظام إدارة البيانات...', 'info');
            
            const statusDiv = document.getElementById('systemStatus');
            
            if (window.advancedDataManager) {
                const info = {
                    initialized: advancedDataManager.initialized,
                    storageKeys: Object.keys(advancedDataManager.storageKeys),
                    requests: advancedDataManager.getActivationRequests().length,
                    licenses: advancedDataManager.getLicenses().length
                };
                
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    <strong>✅ نظام إدارة البيانات:</strong><br>
                    مُهيأ: ${info.initialized ? 'نعم' : 'لا'}<br>
                    الطلبات: ${info.requests}<br>
                    التراخيص: ${info.licenses}<br>
                    مفاتيح التخزين: ${info.storageKeys.length}
                `;
                
                addLog(`✅ النظام مُهيأ: ${info.requests} طلب، ${info.licenses} ترخيص`, 'success');
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ نظام إدارة البيانات غير متوفر';
                addLog('❌ نظام إدارة البيانات غير متوفر', 'error');
            }
        }

        // اختبار إضافة زبون
        function testAddCustomer() {
            addLog('👤 اختبار إضافة زبون جديد...', 'info');
            
            const customerData = {
                customerName: document.getElementById('testCustomerName').value,
                phone: document.getElementById('testPhone').value,
                state: document.getElementById('testState').value,
                licenseType: document.getElementById('testLicenseType').value,
                deviceInfo: {
                    os: 'Test OS',
                    browser: 'Test Browser',
                    timestamp: new Date().toISOString()
                }
            };
            
            try {
                if (window.advancedDataManager) {
                    const request = advancedDataManager.addActivationRequest(customerData);
                    addLog(`✅ تم إضافة الزبون: ${customerData.customerName} (ID: ${request.id})`, 'success');
                    
                    // تحديث العرض
                    showSavedData();
                } else {
                    // حفظ محلي
                    const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                    const newRequest = {
                        ...customerData,
                        id: Date.now().toString(),
                        requestDate: new Date().toISOString(),
                        status: 'pending'
                    };
                    requests.push(newRequest);
                    localStorage.setItem('activationRequests', JSON.stringify(requests));
                    
                    addLog(`✅ تم حفظ الزبون محلياً: ${customerData.customerName}`, 'success');
                }
            } catch (error) {
                addLog(`❌ فشل في إضافة الزبون: ${error.message}`, 'error');
            }
        }

        // توليد بيانات عشوائية
        function generateRandomCustomer() {
            const names = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'علي محمود', 'خديجة سالم'];
            const states = ['الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'باتنة', 'سطيف'];
            
            const randomName = names[Math.floor(Math.random() * names.length)];
            const randomPhone = '055' + Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
            const randomState = states[Math.floor(Math.random() * states.length)];
            const randomLicense = Math.random() > 0.5 ? 'lifetime' : 'trial';
            
            document.getElementById('testCustomerName').value = randomName;
            document.getElementById('testPhone').value = randomPhone;
            document.getElementById('testState').value = randomState;
            document.getElementById('testLicenseType').value = randomLicense;
            
            addLog(`🎲 تم توليد بيانات عشوائية: ${randomName}`, 'info');
        }

        // إضافة زبائن متعددين
        function addMultipleCustomers(count) {
            addLog(`👥 بدء إضافة ${count} زبون...`, 'info');
            
            const names = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'علي محمود', 'خديجة سالم', 'يوسف إبراهيم', 'مريم عبدالله'];
            const states = ['الجزائر', 'وهران', 'قسنطينة', 'عنابة', 'باتنة', 'سطيف', 'البليدة', 'تلمسان'];
            
            let successCount = 0;
            let errorCount = 0;
            
            for (let i = 0; i < count; i++) {
                try {
                    const customerData = {
                        customerName: names[Math.floor(Math.random() * names.length)] + ' ' + (i + 1),
                        phone: '055' + Math.floor(Math.random() * 10000000).toString().padStart(7, '0'),
                        state: states[Math.floor(Math.random() * states.length)],
                        licenseType: Math.random() > 0.5 ? 'lifetime' : 'trial',
                        deviceInfo: {
                            os: 'Test OS',
                            browser: 'Test Browser',
                            timestamp: new Date().toISOString()
                        }
                    };
                    
                    if (window.advancedDataManager) {
                        advancedDataManager.addActivationRequest(customerData);
                    } else {
                        const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                        requests.push({
                            ...customerData,
                            id: Date.now().toString() + i,
                            requestDate: new Date().toISOString(),
                            status: 'pending'
                        });
                        localStorage.setItem('activationRequests', JSON.stringify(requests));
                    }
                    
                    successCount++;
                } catch (error) {
                    errorCount++;
                }
            }
            
            addLog(`✅ تم إضافة ${successCount} زبون بنجاح`, 'success');
            if (errorCount > 0) {
                addLog(`❌ فشل في إضافة ${errorCount} زبون`, 'error');
            }
            
            // تحديث العرض
            showSavedData();
        }

        // عرض البيانات المحفوظة
        function showSavedData() {
            addLog('📊 عرض البيانات المحفوظة...', 'info');
            
            const displayDiv = document.getElementById('dataDisplay');
            
            try {
                let requests = [];
                
                if (window.advancedDataManager) {
                    requests = advancedDataManager.getActivationRequests();
                } else {
                    requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                }
                
                if (requests.length === 0) {
                    displayDiv.className = 'status warning';
                    displayDiv.innerHTML = '⚠️ لا توجد طلبات محفوظة';
                    return;
                }
                
                let html = `<strong>📋 الطلبات المحفوظة (${requests.length}):</strong><br><br>`;
                
                requests.slice(-5).forEach((request, index) => {
                    html += `
                        ${index + 1}. <strong>${request.customerName}</strong><br>
                        📞 ${request.phone} | 📍 ${request.state}<br>
                        🎫 ${request.licenseType === 'lifetime' ? 'مدى الحياة' : 'تجريبي'}<br>
                        📅 ${new Date(request.requestDate).toLocaleString('ar-SA')}<br><br>
                    `;
                });
                
                if (requests.length > 5) {
                    html += `<em>... و ${requests.length - 5} طلب آخر</em>`;
                }
                
                displayDiv.className = 'status success';
                displayDiv.innerHTML = html;
                
                addLog(`📊 تم عرض ${requests.length} طلب`, 'success');
                
            } catch (error) {
                displayDiv.className = 'status error';
                displayDiv.innerHTML = `❌ خطأ في قراءة البيانات: ${error.message}`;
                addLog(`❌ خطأ في قراءة البيانات: ${error.message}`, 'error');
            }
        }

        // عرض الإحصائيات
        function showStatistics() {
            addLog('📈 عرض الإحصائيات...', 'info');
            
            const displayDiv = document.getElementById('dataDisplay');
            
            try {
                let stats;
                
                if (window.advancedDataManager) {
                    stats = advancedDataManager.getStatistics();
                } else {
                    const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
                    stats = {
                        totalRequests: requests.length,
                        pendingRequests: requests.filter(r => r.status === 'pending').length,
                        approvedRequests: requests.filter(r => r.status === 'approved').length,
                        rejectedRequests: requests.filter(r => r.status === 'rejected').length
                    };
                }
                
                const html = `
                    <strong>📈 إحصائيات النظام:</strong><br><br>
                    📋 إجمالي الطلبات: ${stats.totalRequests || 0}<br>
                    ⏳ في الانتظار: ${stats.pendingRequests || 0}<br>
                    ✅ موافق عليها: ${stats.approvedRequests || 0}<br>
                    ❌ مرفوضة: ${stats.rejectedRequests || 0}<br>
                    🔑 التراخيص النشطة: ${stats.activeLicenses || 0}<br>
                    📅 آخر تحديث: ${new Date().toLocaleString('ar-SA')}
                `;
                
                displayDiv.className = 'status info';
                displayDiv.innerHTML = html;
                
                addLog('📈 تم عرض الإحصائيات', 'success');
                
            } catch (error) {
                displayDiv.className = 'status error';
                displayDiv.innerHTML = `❌ خطأ في قراءة الإحصائيات: ${error.message}`;
                addLog(`❌ خطأ في قراءة الإحصائيات: ${error.message}`, 'error');
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                addLog('🗑️ مسح جميع البيانات...', 'warning');
                
                try {
                    if (window.advancedDataManager) {
                        advancedDataManager.saveData('activationRequests', []);
                        advancedDataManager.saveData('licenses', []);
                        advancedDataManager.updateStatistics();
                    }
                    
                    localStorage.removeItem('activationRequests');
                    localStorage.removeItem('licenses');
                    localStorage.removeItem('backupRequests');
                    
                    addLog('✅ تم مسح جميع البيانات', 'success');
                    showSavedData();
                    
                } catch (error) {
                    addLog(`❌ خطأ في مسح البيانات: ${error.message}`, 'error');
                }
            }
        }

        // مسح السجل
        function clearLog() {
            document.getElementById('testLog').innerHTML = '<div class="log-entry info">🧪 تم مسح السجل...</div>';
            testCounter = 0;
        }

        // تهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 تم تحميل صفحة اختبار تسجيل الزبون', 'success');
            
            // فحص النظام تلقائياً
            setTimeout(() => {
                checkDataManager();
            }, 1000);
        });
    </script>
</body>
</html>
