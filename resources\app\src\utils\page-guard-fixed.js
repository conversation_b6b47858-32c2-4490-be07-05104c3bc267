// نظام حماية الصفحات المحسن
// Fixed Page Guard System

(function() {
    'use strict';
    
    console.log('🛡️ تحميل نظام الحماية المحسن...');
    
    // إعدادات النظام
    const CONFIG = {
        sessionKey: 'userSession',
        checkInterval: 30000, // 30 ثانية
        sessionTimeout: 24 * 60 * 60 * 1000, // 24 ساعة
        protectionDelay: 1000, // تأخير الحماية 1 ثانية
        supportInfo: {
            phone: '0696924176',
            whatsapp: '213696924176',
            email: '<EMAIL>'
        }
    };
    
    // الصفحات المستثناة من الحماية
    const EXCLUDED_PAGES = [
        'new-login.html',
        'activation-request.html',
        'test-login.html',
        'test-login-flow.html',
        'diagnostic-tool.html',
        'test-suite.html'
    ];
    
    // متغيرات النظام
    let isProtectionActive = false;
    let sessionCheckInterval = null;
    let currentSession = null;
    
    // التحقق من الحاجة لتطبيق الحماية
    function shouldApplyProtection() {
        const currentPath = window.location.pathname;
        const isExcluded = EXCLUDED_PAGES.some(page => currentPath.includes(page));
        
        console.log('🔍 فحص الحاجة للحماية:', {
            path: currentPath,
            excluded: isExcluded
        });
        
        return !isExcluded;
    }
    
    // التحقق من صحة الجلسة
    function isValidSession() {
        try {
            const sessionData = localStorage.getItem(CONFIG.sessionKey);
            
            if (!sessionData) {
                console.log('❌ لا توجد جلسة محفوظة');
                return false;
            }
            
            const session = JSON.parse(sessionData);
            console.log('🔍 فحص الجلسة:', {
                userType: session.userType,
                isValid: session.isValid,
                hasLoginTime: !!session.loginTime
            });
            
            // التحقق من البيانات الأساسية
            if (!session.isValid || !session.loginTime || !session.userType) {
                console.log('❌ بيانات الجلسة غير مكتملة');
                return false;
            }
            
            // التحقق من نوع المستخدم
            if (!['admin', 'customer'].includes(session.userType)) {
                console.log('❌ نوع مستخدم غير صالح:', session.userType);
                return false;
            }
            
            // التحقق من انتهاء الصلاحية
            const now = new Date().getTime();
            const loginTime = new Date(session.loginTime).getTime();
            
            if ((now - loginTime) > CONFIG.sessionTimeout) {
                console.log('❌ انتهت صلاحية الجلسة');
                localStorage.removeItem(CONFIG.sessionKey);
                return false;
            }
            
            // تحديث النشاط
            session.lastActivity = new Date().toISOString();
            localStorage.setItem(CONFIG.sessionKey, JSON.stringify(session));
            
            console.log('✅ الجلسة صالحة:', session.userType);
            currentSession = session;
            return true;
            
        } catch (error) {
            console.error('❌ خطأ في فحص الجلسة:', error);
            localStorage.removeItem(CONFIG.sessionKey);
            return false;
        }
    }
    
    // إظهار رسالة الحماية
    function showProtectionMessage() {
        console.log('🚫 إظهار رسالة الحماية');
        
        // إخفاء المحتوى
        document.body.style.visibility = 'hidden';
        
        // إزالة رسالة سابقة إن وجدت
        const existingOverlay = document.getElementById('protectionOverlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }
        
        // إنشاء overlay الحماية
        const overlay = document.createElement('div');
        overlay.id = 'protectionOverlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
            direction: rtl;
        `;
        
        overlay.innerHTML = `
            <div style="
                background: white;
                padding: 3rem;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                max-width: 500px;
                text-align: center;
                animation: slideIn 0.5s ease;
            ">
                <div style="color: #e74c3c; font-size: 4rem; margin-bottom: 1rem;">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2 style="color: #2c3e50; margin-bottom: 1rem; font-size: 1.8rem;">
                    🔒 صفحة محمية
                </h2>
                <p style="color: #7f8c8d; margin-bottom: 2rem; line-height: 1.6; font-size: 1.1rem;">
                    هذه الصفحة تتطلب تسجيل دخول صحيح.<br>
                    سيتم إعادة توجيهك لصفحة تسجيل الدخول.
                </p>
                
                <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
                    <h4 style="color: #2c3e50; margin-bottom: 1rem;">📞 للدعم الفني:</h4>
                    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                        <a href="tel:${CONFIG.supportInfo.phone}" style="
                            display: flex; align-items: center; gap: 0.5rem;
                            background: #27ae60; color: white; padding: 0.8rem 1.2rem;
                            border-radius: 8px; text-decoration: none; font-weight: 600;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#229954'" onmouseout="this.style.background='#27ae60'">
                            <i class="fas fa-phone"></i>
                            ${CONFIG.supportInfo.phone}
                        </a>
                        <a href="https://wa.me/${CONFIG.supportInfo.whatsapp}" target="_blank" style="
                            display: flex; align-items: center; gap: 0.5rem;
                            background: #25d366; color: white; padding: 0.8rem 1.2rem;
                            border-radius: 8px; text-decoration: none; font-weight: 600;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='#20ba5a'" onmouseout="this.style.background='#25d366'">
                            <i class="fab fa-whatsapp"></i>
                            واتساب
                        </a>
                    </div>
                </div>
                
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <button onclick="window.location.reload()" style="
                        background: #3498db; color: white; border: none;
                        padding: 1rem 2rem; border-radius: 8px; cursor: pointer;
                        font-weight: 600; transition: all 0.3s ease;
                    " onmouseover="this.style.background='#2980b9'" onmouseout="this.style.background='#3498db'">
                        <i class="fas fa-sync-alt"></i>
                        إعادة المحاولة
                    </button>
                    <button onclick="redirectToLogin()" style="
                        background: #e74c3c; color: white; border: none;
                        padding: 1rem 2rem; border-radius: 8px; cursor: pointer;
                        font-weight: 600; transition: all 0.3s ease;
                    " onmouseover="this.style.background='#c0392b'" onmouseout="this.style.background='#e74c3c'">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                </div>
                
                <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #eee;">
                    <p style="color: #95a5a6; font-size: 0.9rem;">
                        سيتم إعادة التوجيه تلقائياً خلال <span id="countdown">5</span> ثوان
                    </p>
                </div>
            </div>
        `;
        
        // إضافة CSS للأنيميشن
        if (!document.querySelector('#protectionStyles')) {
            const style = document.createElement('style');
            style.id = 'protectionStyles';
            style.textContent = `
                @keyframes slideIn {
                    from {
                        opacity: 0;
                        transform: translateY(-50px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(overlay);
        
        // إضافة دالة إعادة التوجيه للنافذة
        window.redirectToLogin = redirectToLogin;
        
        // العد التنازلي وإعادة التوجيه التلقائي
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        const countdownInterval = setInterval(() => {
            countdown--;
            if (countdownElement) {
                countdownElement.textContent = countdown;
            }
            
            if (countdown <= 0) {
                clearInterval(countdownInterval);
                redirectToLogin();
            }
        }, 1000);
    }
    
    // إعادة التوجيه لتسجيل الدخول
    function redirectToLogin() {
        console.log('🔄 إعادة التوجيه لتسجيل الدخول');
        
        const currentPath = window.location.pathname;
        let loginPath = '';
        
        if (currentPath.includes('/components/admin/')) {
            loginPath = '../../auth/new-login.html';
        } else if (currentPath.includes('/components/')) {
            loginPath = '../auth/new-login.html';
        } else if (currentPath.includes('/utils/')) {
            loginPath = '../auth/new-login.html';
        } else {
            loginPath = 'src/auth/new-login.html';
        }
        
        console.log('🎯 مسار تسجيل الدخول:', loginPath);
        window.location.href = loginPath;
    }
    
    // إظهار المحتوى
    function showContent() {
        console.log('✅ إظهار محتوى الصفحة');
        document.body.style.visibility = 'visible';
        
        // إزالة overlay الحماية إن وجد
        const overlay = document.getElementById('protectionOverlay');
        if (overlay) {
            overlay.remove();
        }
    }
    
    // بدء مراقبة الجلسة
    function startSessionMonitoring() {
        if (sessionCheckInterval) {
            clearInterval(sessionCheckInterval);
        }
        
        sessionCheckInterval = setInterval(() => {
            if (!isValidSession()) {
                console.log('⚠️ انتهت صلاحية الجلسة أثناء المراقبة');
                stopSessionMonitoring();
                showProtectionMessage();
            }
        }, CONFIG.checkInterval);
        
        console.log('👁️ بدء مراقبة الجلسة');
    }
    
    // إيقاف مراقبة الجلسة
    function stopSessionMonitoring() {
        if (sessionCheckInterval) {
            clearInterval(sessionCheckInterval);
            sessionCheckInterval = null;
        }
        console.log('🛑 إيقاف مراقبة الجلسة');
    }
    
    // تطبيق الحماية
    function applyProtection() {
        if (isProtectionActive) {
            console.log('🔒 الحماية نشطة بالفعل');
            return;
        }
        
        if (!shouldApplyProtection()) {
            console.log('🟢 صفحة مستثناة من الحماية');
            showContent();
            return;
        }
        
        console.log('🛡️ تطبيق حماية الصفحة...');
        isProtectionActive = true;
        
        // إخفاء المحتوى فوراً
        document.body.style.visibility = 'hidden';
        
        // التحقق من الجلسة مع تأخير
        setTimeout(() => {
            console.log('🔍 بدء فحص الجلسة...');
            
            if (isValidSession()) {
                console.log('✅ جلسة صالحة، إظهار المحتوى');
                showContent();
                startSessionMonitoring();
            } else {
                console.log('❌ جلسة غير صالحة، إظهار رسالة الحماية');
                showProtectionMessage();
            }
        }, CONFIG.protectionDelay);
    }
    
    // مراقبة تغييرات localStorage
    window.addEventListener('storage', function(e) {
        if (e.key === CONFIG.sessionKey) {
            console.log('🔄 تم تحديث الجلسة من نافذة أخرى');
            
            if (e.newValue) {
                // تم إنشاء أو تحديث جلسة
                setTimeout(() => {
                    if (isValidSession()) {
                        console.log('✅ جلسة جديدة صالحة، إظهار المحتوى');
                        showContent();
                        if (!sessionCheckInterval) {
                            startSessionMonitoring();
                        }
                    }
                }, 100);
            } else {
                // تم حذف الجلسة
                console.log('❌ تم حذف الجلسة');
                stopSessionMonitoring();
                showProtectionMessage();
            }
        }
    });
    
    // مراقبة أحداث الجلسة المخصصة
    window.addEventListener('sessionUpdated', function(e) {
        console.log('🔄 تم تحديث الجلسة:', e.detail);
        
        setTimeout(() => {
            if (isValidSession()) {
                showContent();
                if (!sessionCheckInterval) {
                    startSessionMonitoring();
                }
            }
        }, 100);
    });
    
    // تطبيق الحماية عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyProtection);
    } else {
        applyProtection();
    }
    
    // تطبيق الحماية فوراً أيضاً
    applyProtection();
    
    console.log('🛡️ تم تحميل نظام الحماية المحسن');
    
})();
