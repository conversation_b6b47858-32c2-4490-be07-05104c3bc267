// نظام التحقق من الجهاز

class DeviceVerification {
    constructor() {
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;
        this.deviceFingerprint = null;
        this.initialized = false;
    }

    // تهيئة نظام التحقق من الجهاز
    async initialize() {
        if (this.initialized) return;

        try {
            this.deviceFingerprint = await this.generateDeviceFingerprint();
            this.initialized = true;
            console.log('تم تهيئة نظام التحقق من الجهاز بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة نظام التحقق من الجهاز:', error);
            throw error;
        }
    }

    // إنشاء بصمة فريدة للجهاز
    async generateDeviceFingerprint() {
        try {
            if (this.isElectron) {
                return await this.generateElectronFingerprint();
            } else {
                return await this.generateBrowserFingerprint();
            }
        } catch (error) {
            console.error('خطأ في إنشاء بصمة الجهاز:', error);
            return 'fallback-' + Date.now().toString(36);
        }
    }

    // إنشاء بصمة للتطبيق في Electron
    async generateElectronFingerprint() {
        const os = require('os');
        const crypto = require('crypto');
        const fs = require('fs');
        const path = require('path');

        // جمع معلومات الجهاز
        const deviceInfo = {
            // معلومات النظام
            hostname: os.hostname(),
            platform: os.platform(),
            arch: os.arch(),
            release: os.release(),
            
            // معلومات المعالج
            cpus: os.cpus().map(cpu => ({
                model: cpu.model,
                speed: cpu.speed
            })),
            
            // معلومات الذاكرة
            totalmem: os.totalmem(),
            
            // معلومات الشبكة
            networkInterfaces: this.getNetworkMacs(),
            
            // معلومات القرص الصلب
            diskInfo: await this.getDiskInfo(),
            
            // معرف اللوحة الأم (إن أمكن)
            motherboardId: await this.getMotherboardId()
        };

        // إنشاء hash فريد
        const hash = crypto.createHash('sha256');
        hash.update(JSON.stringify(deviceInfo, null, 0));
        return hash.digest('hex');
    }

    // إنشاء بصمة للمتصفح
    async generateBrowserFingerprint() {
        const fingerprint = {
            // معلومات المتصفح
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages,
            platform: navigator.platform,
            
            // معلومات الشاشة
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth,
                pixelDepth: screen.pixelDepth
            },
            
            // معلومات المنطقة الزمنية
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezoneOffset: new Date().getTimezoneOffset(),
            
            // Canvas fingerprinting
            canvas: await this.getCanvasFingerprint(),
            
            // WebGL fingerprinting
            webgl: await this.getWebGLFingerprint(),
            
            // Audio fingerprinting
            audio: await this.getAudioFingerprint(),
            
            // معلومات الخطوط
            fonts: await this.getAvailableFonts(),
            
            // معلومات الإضافات
            plugins: this.getPluginsList()
        };

        // إنشاء hash
        const jsonString = JSON.stringify(fingerprint, null, 0);
        const encoder = new TextEncoder();
        const data = encoder.encode(jsonString);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // الحصول على عناوين MAC للشبكة
    getNetworkMacs() {
        if (!this.isElectron) return [];
        
        const os = require('os');
        const interfaces = os.networkInterfaces();
        const macs = [];
        
        for (const name of Object.keys(interfaces)) {
            for (const iface of interfaces[name]) {
                if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
                    macs.push(iface.mac);
                }
            }
        }
        
        return macs.sort();
    }

    // الحصول على معلومات القرص الصلب
    async getDiskInfo() {
        if (!this.isElectron) return null;
        
        try {
            const fs = require('fs');
            const path = require('path');
            const os = require('os');
            
            // محاولة الحصول على الرقم التسلسلي للقرص
            if (os.platform() === 'win32') {
                return await this.getWindowsDiskInfo();
            } else if (os.platform() === 'darwin') {
                return await this.getMacDiskInfo();
            } else {
                return await this.getLinuxDiskInfo();
            }
        } catch (error) {
            console.error('خطأ في الحصول على معلومات القرص:', error);
            return null;
        }
    }

    // معلومات القرص في Windows
    async getWindowsDiskInfo() {
        try {
            const { exec } = require('child_process');
            return new Promise((resolve) => {
                exec('wmic diskdrive get serialnumber', (error, stdout) => {
                    if (error) {
                        resolve(null);
                        return;
                    }
                    const lines = stdout.split('\n').filter(line => line.trim());
                    const serials = lines.slice(1).map(line => line.trim()).filter(line => line);
                    resolve(serials.length > 0 ? serials[0] : null);
                });
            });
        } catch {
            return null;
        }
    }

    // معلومات القرص في macOS
    async getMacDiskInfo() {
        try {
            const { exec } = require('child_process');
            return new Promise((resolve) => {
                exec('system_profiler SPSerialATADataType | grep "Serial Number"', (error, stdout) => {
                    if (error) {
                        resolve(null);
                        return;
                    }
                    const match = stdout.match(/Serial Number:\s*(.+)/);
                    resolve(match ? match[1].trim() : null);
                });
            });
        } catch {
            return null;
        }
    }

    // معلومات القرص في Linux
    async getLinuxDiskInfo() {
        try {
            const { exec } = require('child_process');
            return new Promise((resolve) => {
                exec('lsblk -o SERIAL -n | head -1', (error, stdout) => {
                    if (error) {
                        resolve(null);
                        return;
                    }
                    const serial = stdout.trim();
                    resolve(serial || null);
                });
            });
        } catch {
            return null;
        }
    }

    // الحصول على معرف اللوحة الأم
    async getMotherboardId() {
        if (!this.isElectron) return null;
        
        try {
            const os = require('os');
            const { exec } = require('child_process');
            
            if (os.platform() === 'win32') {
                return new Promise((resolve) => {
                    exec('wmic baseboard get serialnumber', (error, stdout) => {
                        if (error) {
                            resolve(null);
                            return;
                        }
                        const lines = stdout.split('\n').filter(line => line.trim());
                        const serials = lines.slice(1).map(line => line.trim()).filter(line => line);
                        resolve(serials.length > 0 ? serials[0] : null);
                    });
                });
            }
            
            return null;
        } catch {
            return null;
        }
    }

    // Canvas fingerprinting
    async getCanvasFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // رسم نص وأشكال معقدة
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f60';
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = '#069';
            ctx.fillText('Device Fingerprint 🔒', 2, 15);
            ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
            ctx.fillText('Device Fingerprint 🔒', 4, 17);
            
            // إضافة أشكال هندسية
            ctx.globalCompositeOperation = 'multiply';
            ctx.fillStyle = 'rgb(255,0,255)';
            ctx.beginPath();
            ctx.arc(50, 50, 50, 0, Math.PI * 2, true);
            ctx.closePath();
            ctx.fill();
            
            return canvas.toDataURL();
        } catch {
            return 'canvas-not-supported';
        }
    }

    // WebGL fingerprinting
    async getWebGLFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) return 'webgl-not-supported';
            
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
            
            return `${vendor}~${renderer}`;
        } catch {
            return 'webgl-error';
        }
    }

    // Audio fingerprinting
    async getAudioFingerprint() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const analyser = audioContext.createAnalyser();
            const gainNode = audioContext.createGain();
            
            oscillator.type = 'triangle';
            oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);
            
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            
            oscillator.connect(analyser);
            analyser.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.start(0);
            
            const frequencyData = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(frequencyData);
            
            oscillator.stop();
            audioContext.close();
            
            return Array.from(frequencyData).slice(0, 30).join(',');
        } catch {
            return 'audio-not-supported';
        }
    }

    // الحصول على قائمة الخطوط المتاحة
    async getAvailableFonts() {
        const fonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact', 'Tahoma', 'Geneva'
        ];
        
        const availableFonts = [];
        const testString = 'mmmmmmmmmmlli';
        const testSize = '72px';
        const h = document.getElementsByTagName('body')[0];
        
        // إنشاء عنصر مرجعي
        const s = document.createElement('span');
        s.style.fontSize = testSize;
        s.innerHTML = testString;
        const defaultWidth = s.offsetWidth;
        const defaultHeight = s.offsetHeight;
        
        for (const font of fonts) {
            s.style.fontFamily = font;
            h.appendChild(s);
            
            if (s.offsetWidth !== defaultWidth || s.offsetHeight !== defaultHeight) {
                availableFonts.push(font);
            }
            
            h.removeChild(s);
        }
        
        return availableFonts;
    }

    // الحصول على قائمة الإضافات
    getPluginsList() {
        const plugins = [];
        for (let i = 0; i < navigator.plugins.length; i++) {
            plugins.push(navigator.plugins[i].name);
        }
        return plugins.sort();
    }

    // التحقق من تطابق بصمة الجهاز
    async verifyDevice(storedFingerprint) {
        if (!this.initialized) {
            await this.initialize();
        }
        
        return this.deviceFingerprint === storedFingerprint;
    }

    // الحصول على بصمة الجهاز الحالية
    async getCurrentFingerprint() {
        if (!this.initialized) {
            await this.initialize();
        }
        
        return this.deviceFingerprint;
    }

    // حفظ بصمة الجهاز
    async saveDeviceFingerprint(licenseId) {
        if (!this.initialized) {
            await this.initialize();
        }
        
        const deviceData = {
            licenseId: licenseId,
            fingerprint: this.deviceFingerprint,
            registeredAt: new Date().toISOString(),
            lastVerified: new Date().toISOString()
        };
        
        // حفظ في التخزين المحلي
        const devices = this.getStoredDevices();
        devices[licenseId] = deviceData;
        localStorage.setItem('registeredDevices', JSON.stringify(devices));
        
        return deviceData;
    }

    // الحصول على الأجهزة المحفوظة
    getStoredDevices() {
        try {
            const stored = localStorage.getItem('registeredDevices');
            return stored ? JSON.parse(stored) : {};
        } catch {
            return {};
        }
    }

    // التحقق من ترخيص الجهاز
    async verifyLicenseDevice(licenseId, licenseFingerprint) {
        if (!this.initialized) {
            await this.initialize();
        }
        
        // التحقق من تطابق بصمة الجهاز
        const isValid = await this.verifyDevice(licenseFingerprint);
        
        if (isValid) {
            // تحديث آخر تحقق
            const devices = this.getStoredDevices();
            if (devices[licenseId]) {
                devices[licenseId].lastVerified = new Date().toISOString();
                localStorage.setItem('registeredDevices', JSON.stringify(devices));
            }
        }
        
        return {
            isValid: isValid,
            currentFingerprint: this.deviceFingerprint,
            message: isValid ? 'الجهاز مطابق للترخيص' : 'هذا الترخيص مخصص لجهاز آخر'
        };
    }
}

// إنشاء مثيل واحد للاستخدام العام
const deviceVerification = new DeviceVerification();

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DeviceVerification, deviceVerification };
} else {
    window.DeviceVerification = DeviceVerification;
    window.deviceVerification = deviceVerification;
}
