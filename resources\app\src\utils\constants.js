// ثوابت وإعدادات التطبيق

// معلومات التطبيق
export const APP_INFO = {
    NAME: 'مؤسسة وقود المستقبل',
    NAME_EN: 'Future Fuel Corporation',
    VERSION: '3.0.0',
    DESCRIPTION: 'نظام إدارة شامل لمحطة الغاز',
    AUTHOR: 'Future Fuel Corporation',
    EMAIL: '<EMAIL>',
    WEBSITE: 'https://futurefuel.sa'
};

// أنواع المستخدمين
export const USER_ROLES = {
    ADMIN: 'admin',
    MANAGER: 'manager',
    USER: 'user',
    VIEWER: 'viewer'
};

// تسميات الأدوار
export const ROLE_LABELS = {
    [USER_ROLES.ADMIN]: 'مدير النظام',
    [USER_ROLES.MANAGER]: 'مدير فرع',
    [USER_ROLES.USER]: 'مستخدم',
    [USER_ROLES.VIEWER]: 'مراقب'
};

// صلاحيات الأدوار
export const ROLE_PERMISSIONS = {
    [USER_ROLES.ADMIN]: [
        'create', 'read', 'update', 'delete',
        'manage_users', 'manage_settings', 'view_reports',
        'backup_restore', 'system_config'
    ],
    [USER_ROLES.MANAGER]: [
        'create', 'read', 'update', 'delete',
        'view_reports', 'manage_inventory'
    ],
    [USER_ROLES.USER]: [
        'create', 'read', 'update'
    ],
    [USER_ROLES.VIEWER]: [
        'read'
    ]
};

// أنواع الشهادات
export const CERTIFICATE_TYPES = {
    INSTALLATION: 'installation',
    MONITORING: 'monitoring',
    MAINTENANCE: 'maintenance',
    INSPECTION: 'inspection'
};

// تسميات أنواع الشهادات
export const CERTIFICATE_TYPE_LABELS = {
    [CERTIFICATE_TYPES.INSTALLATION]: 'شهادة تركيب',
    [CERTIFICATE_TYPES.MONITORING]: 'شهادة مراقبة دورية',
    [CERTIFICATE_TYPES.MAINTENANCE]: 'شهادة صيانة',
    [CERTIFICATE_TYPES.INSPECTION]: 'شهادة فحص'
};

// حالات الشهادات
export const CERTIFICATE_STATUS = {
    ACTIVE: 'active',
    EXPIRED: 'expired',
    PENDING: 'pending',
    CANCELLED: 'cancelled'
};

// تسميات حالات الشهادات
export const CERTIFICATE_STATUS_LABELS = {
    [CERTIFICATE_STATUS.ACTIVE]: 'نشطة',
    [CERTIFICATE_STATUS.EXPIRED]: 'منتهية الصلاحية',
    [CERTIFICATE_STATUS.PENDING]: 'في الانتظار',
    [CERTIFICATE_STATUS.CANCELLED]: 'ملغية'
};

// أنواع المركبات
export const VEHICLE_TYPES = {
    CAR: 'car',
    TRUCK: 'truck',
    MOTORCYCLE: 'motorcycle',
    BUS: 'bus',
    TAXI: 'taxi'
};

// تسميات أنواع المركبات
export const VEHICLE_TYPE_LABELS = {
    [VEHICLE_TYPES.CAR]: 'سيارة',
    [VEHICLE_TYPES.TRUCK]: 'شاحنة',
    [VEHICLE_TYPES.MOTORCYCLE]: 'دراجة نارية',
    [VEHICLE_TYPES.BUS]: 'حافلة',
    [VEHICLE_TYPES.TAXI]: 'تاكسي'
};

// أنواع الوقود
export const FUEL_TYPES = {
    GASOLINE_91: 'gasoline_91',
    GASOLINE_95: 'gasoline_95',
    DIESEL: 'diesel',
    LPG: 'lpg'
};

// تسميات أنواع الوقود
export const FUEL_TYPE_LABELS = {
    [FUEL_TYPES.GASOLINE_91]: 'بنزين 91',
    [FUEL_TYPES.GASOLINE_95]: 'بنزين 95',
    [FUEL_TYPES.DIESEL]: 'ديزل',
    [FUEL_TYPES.LPG]: 'غاز البترول المسال'
};

// أسعار الوقود (ريال سعودي)
export const FUEL_PRICES = {
    [FUEL_TYPES.GASOLINE_91]: 2.18,
    [FUEL_TYPES.GASOLINE_95]: 2.33,
    [FUEL_TYPES.DIESEL]: 0.68,
    [FUEL_TYPES.LPG]: 0.75
};

// حالات الطلبات
export const ORDER_STATUS = {
    PENDING: 'pending',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
};

// تسميات حالات الطلبات
export const ORDER_STATUS_LABELS = {
    [ORDER_STATUS.PENDING]: 'في الانتظار',
    [ORDER_STATUS.PROCESSING]: 'قيد المعالجة',
    [ORDER_STATUS.COMPLETED]: 'مكتمل',
    [ORDER_STATUS.CANCELLED]: 'ملغي'
};

// أنواع المعاملات المالية
export const TRANSACTION_TYPES = {
    SALE: 'sale',
    PURCHASE: 'purchase',
    REFUND: 'refund',
    PAYMENT: 'payment'
};

// تسميات أنواع المعاملات
export const TRANSACTION_TYPE_LABELS = {
    [TRANSACTION_TYPES.SALE]: 'مبيعات',
    [TRANSACTION_TYPES.PURCHASE]: 'مشتريات',
    [TRANSACTION_TYPES.REFUND]: 'استرداد',
    [TRANSACTION_TYPES.PAYMENT]: 'دفع'
};

// طرق الدفع
export const PAYMENT_METHODS = {
    CASH: 'cash',
    CARD: 'card',
    BANK_TRANSFER: 'bank_transfer',
    DIGITAL_WALLET: 'digital_wallet'
};

// تسميات طرق الدفع
export const PAYMENT_METHOD_LABELS = {
    [PAYMENT_METHODS.CASH]: 'نقدي',
    [PAYMENT_METHODS.CARD]: 'بطاقة',
    [PAYMENT_METHODS.BANK_TRANSFER]: 'تحويل بنكي',
    [PAYMENT_METHODS.DIGITAL_WALLET]: 'محفظة رقمية'
};

// أولويات الإشعارات
export const NOTIFICATION_PRIORITY = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent'
};

// تسميات أولويات الإشعارات
export const NOTIFICATION_PRIORITY_LABELS = {
    [NOTIFICATION_PRIORITY.LOW]: 'منخفضة',
    [NOTIFICATION_PRIORITY.MEDIUM]: 'متوسطة',
    [NOTIFICATION_PRIORITY.HIGH]: 'عالية',
    [NOTIFICATION_PRIORITY.URGENT]: 'عاجلة'
};

// أنواع الإشعارات
export const NOTIFICATION_TYPES = {
    INFO: 'info',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error'
};

// ألوان أنواع الإشعارات
export const NOTIFICATION_COLORS = {
    [NOTIFICATION_TYPES.INFO]: '#17a2b8',
    [NOTIFICATION_TYPES.SUCCESS]: '#28a745',
    [NOTIFICATION_TYPES.WARNING]: '#ffc107',
    [NOTIFICATION_TYPES.ERROR]: '#dc3545'
};

// إعدادات التطبيق الافتراضية
export const DEFAULT_SETTINGS = {
    // إعدادات عامة
    shopName: APP_INFO.NAME,
    language: 'ar',
    currency: 'SAR',
    timezone: 'Asia/Riyadh',
    
    // إعدادات العمل
    workingHours: {
        start: '08:00',
        end: '18:00',
        days: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday']
    },
    
    // إعدادات التذكيرات
    reminders: {
        certificateExpiry: 30, // أيام قبل انتهاء الصلاحية
        maintenanceDue: 7,     // أيام قبل موعد الصيانة
        paymentDue: 3          // أيام قبل موعد الدفع
    },
    
    // إعدادات النسخ الاحتياطية
    backup: {
        enabled: true,
        frequency: 'daily', // daily, weekly, monthly
        maxBackups: 30,
        autoCleanup: true
    },
    
    // إعدادات الأمان
    security: {
        sessionTimeout: 480, // دقائق (8 ساعات)
        maxLoginAttempts: 5,
        passwordMinLength: 6,
        requireStrongPassword: false
    },
    
    // إعدادات الواجهة
    ui: {
        theme: 'light', // light, dark, auto
        sidebarCollapsed: false,
        showWelcomeMessage: true,
        itemsPerPage: 25
    },
    
    // إعدادات التقارير
    reports: {
        defaultPeriod: 'month',
        includeCharts: true,
        autoGenerate: false
    }
};

// تنسيقات التاريخ
export const DATE_FORMATS = {
    SHORT: 'DD/MM/YYYY',
    LONG: 'DD MMMM YYYY',
    WITH_TIME: 'DD/MM/YYYY HH:mm',
    TIME_ONLY: 'HH:mm',
    ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ'
};

// أحجام الملفات
export const FILE_SIZES = {
    KB: 1024,
    MB: 1024 * 1024,
    GB: 1024 * 1024 * 1024
};

// أنواع الملفات المسموحة
export const ALLOWED_FILE_TYPES = {
    IMAGES: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
    DOCUMENTS: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
    SPREADSHEETS: ['xls', 'xlsx', 'csv'],
    ARCHIVES: ['zip', 'rar', '7z']
};

// حدود الملفات
export const FILE_LIMITS = {
    MAX_SIZE: 10 * FILE_SIZES.MB, // 10 ميجابايت
    MAX_FILES: 5 // عدد الملفات في الرفع الواحد
};

// رسائل الخطأ الشائعة
export const ERROR_MESSAGES = {
    NETWORK_ERROR: 'خطأ في الاتصال بالشبكة',
    UNAUTHORIZED: 'غير مخول للوصول',
    FORBIDDEN: 'ممنوع الوصول',
    NOT_FOUND: 'العنصر غير موجود',
    SERVER_ERROR: 'خطأ في الخادم',
    VALIDATION_ERROR: 'خطأ في التحقق من البيانات',
    FILE_TOO_LARGE: 'حجم الملف كبير جداً',
    INVALID_FILE_TYPE: 'نوع الملف غير مدعوم'
};

// رسائل النجاح الشائعة
export const SUCCESS_MESSAGES = {
    SAVED: 'تم الحفظ بنجاح',
    UPDATED: 'تم التحديث بنجاح',
    DELETED: 'تم الحذف بنجاح',
    CREATED: 'تم الإنشاء بنجاح',
    UPLOADED: 'تم الرفع بنجاح',
    EXPORTED: 'تم التصدير بنجاح',
    IMPORTED: 'تم الاستيراد بنجاح'
};

// مفاتيح التخزين المحلي
export const STORAGE_KEYS = {
    USER_SESSION: 'userSession',
    THEME: 'theme',
    LANGUAGE: 'language',
    SETTINGS: 'appSettings',
    CACHE: 'appCache'
};

// عناوين URL للواجهات
export const ROUTES = {
    LOGIN: '/auth/login',
    DASHBOARD: '/dashboard',
    CUSTOMERS: '/customers',
    VEHICLES: '/vehicles',
    CERTIFICATES: '/certificates',
    INVENTORY: '/inventory',
    REPORTS: '/reports',
    SETTINGS: '/settings'
};

// الولايات الجزائرية (58 ولاية)
export const ALGERIAN_STATES = {
    '01': 'أدرار',
    '02': 'الشلف',
    '03': 'الأغواط',
    '04': 'أم البواقي',
    '05': 'باتنة',
    '06': 'بجاية',
    '07': 'بسكرة',
    '08': 'بشار',
    '09': 'البليدة',
    '10': 'البويرة',
    '11': 'تمنراست',
    '12': 'تبسة',
    '13': 'تلمسان',
    '14': 'تيارت',
    '15': 'تيزي وزو',
    '16': 'الجزائر',
    '17': 'الجلفة',
    '18': 'جيجل',
    '19': 'سطيف',
    '20': 'سعيدة',
    '21': 'سكيكدة',
    '22': 'سيدي بلعباس',
    '23': 'عنابة',
    '24': 'قالمة',
    '25': 'قسنطينة',
    '26': 'المدية',
    '27': 'مستغانم',
    '28': 'المسيلة',
    '29': 'معسكر',
    '30': 'ورقلة',
    '31': 'وهران',
    '32': 'البيض',
    '33': 'إليزي',
    '34': 'برج بوعريريج',
    '35': 'بومرداس',
    '36': 'الطارف',
    '37': 'تندوف',
    '38': 'تيسمسيلت',
    '39': 'الوادي',
    '40': 'خنشلة',
    '41': 'سوق أهراس',
    '42': 'تيبازة',
    '43': 'ميلة',
    '44': 'عين الدفلى',
    '45': 'النعامة',
    '46': 'عين تموشنت',
    '47': 'غرداية',
    '48': 'غليزان',
    '49': 'تيميمون',
    '50': 'برج باجي مختار',
    '51': 'أولاد جلال',
    '52': 'بني عباس',
    '53': 'عين صالح',
    '54': 'عين قزام',
    '55': 'تقرت',
    '56': 'جانت',
    '57': 'المغير',
    '58': 'المنيعة'
};

// أنواع التراخيص
export const LICENSE_TYPES = {
    TRIAL: 'trial',
    LIFETIME: 'lifetime',
    MONTHLY: 'monthly',
    YEARLY: 'yearly'
};

// تسميات أنواع التراخيص
export const LICENSE_TYPE_LABELS = {
    [LICENSE_TYPES.TRIAL]: 'تجريبي (30 يوم)',
    [LICENSE_TYPES.LIFETIME]: 'مدى الحياة',
    [LICENSE_TYPES.MONTHLY]: 'شهري',
    [LICENSE_TYPES.YEARLY]: 'سنوي'
};

// حالات التراخيص
export const LICENSE_STATUS = {
    PENDING: 'pending',
    ACTIVE: 'active',
    EXPIRED: 'expired',
    SUSPENDED: 'suspended',
    REVOKED: 'revoked'
};

// تسميات حالات التراخيص
export const LICENSE_STATUS_LABELS = {
    [LICENSE_STATUS.PENDING]: 'في الانتظار',
    [LICENSE_STATUS.ACTIVE]: 'نشط',
    [LICENSE_STATUS.EXPIRED]: 'منتهي الصلاحية',
    [LICENSE_STATUS.SUSPENDED]: 'معلق',
    [LICENSE_STATUS.REVOKED]: 'ملغي'
};

// حالات طلبات التفعيل
export const ACTIVATION_REQUEST_STATUS = {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    PROCESSING: 'processing'
};

// تسميات حالات طلبات التفعيل
export const ACTIVATION_REQUEST_STATUS_LABELS = {
    [ACTIVATION_REQUEST_STATUS.PENDING]: 'في الانتظار',
    [ACTIVATION_REQUEST_STATUS.APPROVED]: 'موافق عليه',
    [ACTIVATION_REQUEST_STATUS.REJECTED]: 'مرفوض',
    [ACTIVATION_REQUEST_STATUS.PROCESSING]: 'قيد المعالجة'
};

// تصدير جميع الثوابت كمجموعة واحدة
export const CONSTANTS = {
    APP_INFO,
    USER_ROLES,
    ROLE_LABELS,
    ROLE_PERMISSIONS,
    CERTIFICATE_TYPES,
    CERTIFICATE_TYPE_LABELS,
    CERTIFICATE_STATUS,
    CERTIFICATE_STATUS_LABELS,
    VEHICLE_TYPES,
    VEHICLE_TYPE_LABELS,
    FUEL_TYPES,
    FUEL_TYPE_LABELS,
    FUEL_PRICES,
    ORDER_STATUS,
    ORDER_STATUS_LABELS,
    TRANSACTION_TYPES,
    TRANSACTION_TYPE_LABELS,
    PAYMENT_METHODS,
    PAYMENT_METHOD_LABELS,
    NOTIFICATION_PRIORITY,
    NOTIFICATION_PRIORITY_LABELS,
    NOTIFICATION_TYPES,
    NOTIFICATION_COLORS,
    DEFAULT_SETTINGS,
    DATE_FORMATS,
    FILE_SIZES,
    ALLOWED_FILE_TYPES,
    FILE_LIMITS,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    STORAGE_KEYS,
    ROUTES,
    ALGERIAN_STATES,
    LICENSE_TYPES,
    LICENSE_TYPE_LABELS,
    LICENSE_STATUS,
    LICENSE_STATUS_LABELS,
    ACTIVATION_REQUEST_STATUS,
    ACTIVATION_REQUEST_STATUS_LABELS
};

// للاستخدام في المتصفح
if (typeof window !== 'undefined') {
    window.CONSTANTS = CONSTANTS;
}
