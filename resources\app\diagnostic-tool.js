// أداة التشخيص التفاعلية
// Interactive Diagnostic Tool

let currentDiagnostics = null;
let isRunning = false;

// تشغيل التشخيص الشامل
async function runFullDiagnostics() {
    if (isRunning) return;
    
    isRunning = true;
    showLoading(true);
    hideResults();
    
    try {
        console.log('🔍 بدء التشخيص الشامل...');
        
        // إنشاء مثيل جديد من نظام التشخيص
        const diagnostics = new SystemDiagnostics();
        
        // تشغيل التشخيص مع تحديث التقدم
        updateProgress(10, 'فحص البيئة...');
        await sleep(500);
        
        updateProgress(30, 'فحص الملفات الأساسية...');
        await sleep(500);
        
        updateProgress(50, 'فحص نظام المصادقة...');
        await sleep(500);
        
        updateProgress(70, 'فحص نظام التراخيص...');
        await sleep(500);
        
        updateProgress(90, 'فحص قاعدة البيانات...');
        await sleep(500);
        
        // تشغيل التشخيص الفعلي
        const results = await diagnostics.runFullDiagnostics();
        
        updateProgress(100, 'اكتمل التشخيص!');
        await sleep(500);
        
        // حفظ النتائج وعرضها
        currentDiagnostics = results;
        displayResults(results);
        
        // عرض النتائج في Console أيضاً
        diagnostics.displayResults();
        
    } catch (error) {
        console.error('❌ خطأ في التشخيص:', error);
        showError('حدث خطأ أثناء التشخيص: ' + error.message);
    } finally {
        isRunning = false;
        showLoading(false);
    }
}

// فحص سريع
async function runQuickCheck() {
    if (isRunning) return;
    
    isRunning = true;
    showLoading(true);
    hideResults();
    
    try {
        updateProgress(20, 'فحص سريع للمكونات الأساسية...');
        
        const quickResults = {
            overall: 'good',
            tests: [],
            errors: [],
            warnings: [],
            recommendations: []
        };
        
        // فحص localStorage
        try {
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            quickResults.tests.push({
                name: 'localStorage',
                status: 'success',
                message: 'يعمل بشكل صحيح'
            });
        } catch (error) {
            quickResults.errors.push({
                component: 'localStorage',
                message: 'غير متوفر'
            });
        }
        
        updateProgress(50, 'فحص الجلسة الحالية...');
        
        // فحص الجلسة
        const session = localStorage.getItem('userSession');
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                if (sessionData.isValid) {
                    quickResults.tests.push({
                        name: 'الجلسة الحالية',
                        status: 'success',
                        message: `مستخدم نشط: ${sessionData.userType}`
                    });
                } else {
                    quickResults.warnings.push({
                        component: 'الجلسة الحالية',
                        message: 'جلسة غير صالحة'
                    });
                }
            } catch (error) {
                quickResults.errors.push({
                    component: 'الجلسة الحالية',
                    message: 'خطأ في قراءة الجلسة'
                });
            }
        } else {
            quickResults.warnings.push({
                component: 'الجلسة الحالية',
                message: 'لا توجد جلسة نشطة'
            });
        }
        
        updateProgress(80, 'فحص البيانات المحفوظة...');
        
        // فحص البيانات
        const licenses = localStorage.getItem('licenses');
        const requests = localStorage.getItem('activationRequests');
        
        if (licenses) {
            const licenseData = JSON.parse(licenses);
            quickResults.tests.push({
                name: 'التراخيص',
                status: 'success',
                message: `${licenseData.length} ترخيص محفوظ`
            });
        }
        
        if (requests) {
            const requestData = JSON.parse(requests);
            quickResults.tests.push({
                name: 'طلبات التفعيل',
                status: 'success',
                message: `${requestData.length} طلب محفوظ`
            });
        }
        
        updateProgress(100, 'اكتمل الفحص السريع!');
        await sleep(500);
        
        currentDiagnostics = quickResults;
        displayResults(quickResults);
        
    } catch (error) {
        console.error('❌ خطأ في الفحص السريع:', error);
        showError('حدث خطأ أثناء الفحص السريع: ' + error.message);
    } finally {
        isRunning = false;
        showLoading(false);
    }
}

// اختبار نظام تسجيل الدخول
async function testLoginSystem() {
    if (isRunning) return;
    
    isRunning = true;
    showLoading(true);
    hideResults();
    
    try {
        updateProgress(25, 'اختبار بيانات المدير...');
        
        const loginResults = {
            overall: 'good',
            tests: [],
            errors: [],
            warnings: [],
            recommendations: []
        };
        
        // اختبار بيانات المدير
        const adminTest = testAdminCredentials('admin', 'admin123');
        if (adminTest) {
            loginResults.tests.push({
                name: 'بيانات المدير',
                status: 'success',
                message: 'admin/admin123 صحيحة'
            });
        } else {
            loginResults.errors.push({
                component: 'بيانات المدير',
                message: 'بيانات غير صحيحة'
            });
        }
        
        updateProgress(50, 'اختبار عناصر تسجيل الدخول...');
        
        // فحص وجود عناصر تسجيل الدخول (محاكاة)
        const loginElements = ['adminLoginForm', 'username', 'password', 'licenseKey'];
        loginElements.forEach(elementId => {
            loginResults.tests.push({
                name: `عنصر ${elementId}`,
                status: 'success',
                message: 'متوقع وجوده في صفحة تسجيل الدخول'
            });
        });
        
        updateProgress(75, 'اختبار دوال المصادقة...');
        
        // فحص دوال المصادقة
        const authFunctions = ['handleAdminLogin', 'handleLicenseLogin'];
        authFunctions.forEach(funcName => {
            if (typeof window[funcName] === 'function') {
                loginResults.tests.push({
                    name: `دالة ${funcName}`,
                    status: 'success',
                    message: 'موجودة ومتاحة'
                });
            } else {
                loginResults.warnings.push({
                    component: `دالة ${funcName}`,
                    message: 'غير محملة في الصفحة الحالية'
                });
            }
        });
        
        updateProgress(100, 'اكتمل اختبار تسجيل الدخول!');
        await sleep(500);
        
        currentDiagnostics = loginResults;
        displayResults(loginResults);
        
    } catch (error) {
        console.error('❌ خطأ في اختبار تسجيل الدخول:', error);
        showError('حدث خطأ أثناء اختبار تسجيل الدخول: ' + error.message);
    } finally {
        isRunning = false;
        showLoading(false);
    }
}

// مسح جميع البيانات
async function clearAllData() {
    if (!confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }
    
    try {
        const keysToRemove = [
            'userSession',
            'licenses',
            'activationRequests',
            'registeredDevices',
            'licenseLogs',
            'adminSettings'
        ];
        
        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
        });
        
        alert('✅ تم مسح جميع البيانات بنجاح!');
        
        // إعادة تحميل الصفحة
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        
    } catch (error) {
        console.error('❌ خطأ في مسح البيانات:', error);
        alert('❌ حدث خطأ أثناء مسح البيانات: ' + error.message);
    }
}

// عرض النتائج
function displayResults(results) {
    const container = document.getElementById('resultsContainer');
    const grid = document.getElementById('resultsGrid');
    
    // تحديث الإحصائيات
    updateStats(results);
    
    // مسح النتائج السابقة
    grid.innerHTML = '';
    
    // عرض الاختبارات الناجحة
    if (results.tests && results.tests.length > 0) {
        const successCard = createResultCard('الاختبارات الناجحة', 'success', results.tests);
        grid.appendChild(successCard);
    }
    
    // عرض التحذيرات
    if (results.warnings && results.warnings.length > 0) {
        const warningCard = createResultCard('التحذيرات', 'warning', results.warnings);
        grid.appendChild(warningCard);
    }
    
    // عرض الأخطاء
    if (results.errors && results.errors.length > 0) {
        const errorCard = createResultCard('الأخطاء', 'error', results.errors);
        grid.appendChild(errorCard);
    }
    
    // عرض التوصيات
    if (results.recommendations && results.recommendations.length > 0) {
        const recommendationCard = createResultCard('التوصيات', 'info', results.recommendations);
        grid.appendChild(recommendationCard);
    }
    
    // إظهار النتائج
    container.style.display = 'block';
}

// تحديث الإحصائيات
function updateStats(results) {
    const successCount = results.tests ? results.tests.filter(t => t.status === 'success').length : 0;
    const warningCount = results.warnings ? results.warnings.length : 0;
    const errorCount = results.errors ? results.errors.length : 0;
    
    document.getElementById('successCount').textContent = successCount;
    document.getElementById('warningCount').textContent = warningCount;
    document.getElementById('errorCount').textContent = errorCount;
    
    // تحديث الحالة الإجمالية
    const statusEmoji = document.getElementById('statusEmoji');
    const statusText = document.getElementById('statusText');
    
    const overallStatus = results.overall || 'unknown';
    const statusInfo = getStatusInfo(overallStatus);
    
    statusEmoji.textContent = statusInfo.emoji;
    statusText.textContent = statusInfo.text;
}

// إنشاء بطاقة نتيجة
function createResultCard(title, type, items) {
    const card = document.createElement('div');
    card.className = 'result-card';
    
    const header = document.createElement('div');
    header.className = `card-header ${type}`;
    header.innerHTML = `<i class="fas fa-${getTypeIcon(type)}"></i> ${title} (${items.length})`;
    
    const body = document.createElement('div');
    body.className = 'card-body';
    
    items.forEach(item => {
        const testItem = document.createElement('div');
        testItem.className = 'test-item';
        
        const name = item.name || item.component || item.title || 'غير محدد';
        const message = item.message || item.description || 'لا توجد تفاصيل';
        const status = item.status || type;
        
        testItem.innerHTML = `
            <div>
                <div class="test-name">${name}</div>
                <div style="font-size: 0.8rem; color: #666;">${message}</div>
            </div>
            <div class="test-status ${status}">${getStatusText(status)}</div>
        `;
        
        body.appendChild(testItem);
    });
    
    card.appendChild(header);
    card.appendChild(body);
    
    return card;
}

// دوال مساعدة
function getStatusInfo(status) {
    const statusMap = {
        excellent: { emoji: '🟢', text: 'ممتاز' },
        good: { emoji: '🔵', text: 'جيد' },
        fair: { emoji: '🟡', text: 'مقبول' },
        poor: { emoji: '🟠', text: 'ضعيف' },
        critical: { emoji: '🔴', text: 'حرج' },
        unknown: { emoji: '⚪', text: 'غير محدد' }
    };
    return statusMap[status] || statusMap.unknown;
}

function getTypeIcon(type) {
    const iconMap = {
        success: 'check-circle',
        warning: 'exclamation-triangle',
        error: 'times-circle',
        info: 'info-circle'
    };
    return iconMap[type] || 'circle';
}

function getStatusText(status) {
    const textMap = {
        success: 'نجح',
        warning: 'تحذير',
        error: 'خطأ',
        info: 'معلومات'
    };
    return textMap[status] || status;
}

function testAdminCredentials(username, password) {
    return username === 'admin' && password === 'admin123';
}

function showLoading(show) {
    const container = document.getElementById('loadingContainer');
    container.style.display = show ? 'block' : 'none';
    
    if (!show) {
        updateProgress(0, '');
    }
}

function hideResults() {
    document.getElementById('resultsContainer').style.display = 'none';
}

function updateProgress(percent, text) {
    document.getElementById('progressFill').style.width = percent + '%';
    document.getElementById('currentTest').textContent = text;
}

function showError(message) {
    alert('❌ ' + message);
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// إجراءات سريعة
function openLoginPage() {
    window.open('src/auth/new-login.html', '_blank');
}

function openActivationPage() {
    window.open('src/auth/activation-request.html', '_blank');
}

function checkConsole() {
    alert('افتح Developer Tools (F12) وانتقل لتبويب Console لرؤية رسائل التشخيص المفصلة.');
}

function exportResults() {
    if (!currentDiagnostics) {
        alert('لا توجد نتائج للتصدير. قم بتشغيل التشخيص أولاً.');
        return;
    }
    
    const exportData = {
        ...currentDiagnostics,
        exportedAt: new Date().toISOString(),
        systemInfo: {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language
        }
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `diagnostic-results-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

// تشغيل فحص سريع عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 أداة التشخيص جاهزة للاستخدام');
    
    // تشغيل فحص سريع تلقائي بعد 2 ثانية
    setTimeout(() => {
        if (!isRunning) {
            runQuickCheck();
        }
    }, 2000);
});
