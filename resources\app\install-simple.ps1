# تثبيت بسيط لمؤسسة وقود المستقبل
Write-Host "تثبيت مؤسسة وقود المستقبل..." -ForegroundColor Yellow

# الحصول على مسار سطح المكتب
$DesktopPath = [Environment]::GetFolderPath("Desktop")
$CurrentPath = Get-Location

# إنشاء اختصار
$WshShell = New-Object -ComObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("$DesktopPath\مؤسسة وقود المستقبل.lnk")
$Shortcut.TargetPath = "$CurrentPath\run-app.bat"
$Shortcut.WorkingDirectory = $CurrentPath
$Shortcut.Description = "نظام إدارة مؤسسة وقود المستقبل"
$Shortcut.Save()

Write-Host "تم إنشاء الاختصار بنجاح!" -ForegroundColor Green
Write-Host "يمكنك الآن النقر المزدوج على الاختصار في سطح المكتب" -ForegroundColor White
