// نظام تسجيل الأخطاء الشامل
// Comprehensive Error Logging System

class ErrorLogger {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000;
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;
        this.supportInfo = {
            phone: '0696924176',
            whatsapp: '213696924176',
            email: '<EMAIL>'
        };
        this.initialized = false;
    }

    // تهيئة نظام تسجيل الأخطاء
    initialize() {
        if (this.initialized) return;

        try {
            // تحميل السجلات المحفوظة
            this.loadStoredLogs();
            
            // إعداد معالجات الأخطاء العامة
            this.setupGlobalErrorHandlers();
            
            // إعداد مراقبة الأداء
            this.setupPerformanceMonitoring();
            
            this.initialized = true;
            this.log('info', 'ErrorLogger', 'تم تهيئة نظام تسجيل الأخطاء بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام تسجيل الأخطاء:', error);
        }
    }

    // تحميل السجلات المحفوظة
    loadStoredLogs() {
        try {
            const stored = localStorage.getItem('errorLogs');
            if (stored) {
                this.logs = JSON.parse(stored);
                // الاحتفاظ بآخر 1000 سجل فقط
                if (this.logs.length > this.maxLogs) {
                    this.logs = this.logs.slice(-this.maxLogs);
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل السجلات:', error);
            this.logs = [];
        }
    }

    // حفظ السجلات
    saveLogs() {
        try {
            localStorage.setItem('errorLogs', JSON.stringify(this.logs));
        } catch (error) {
            console.error('خطأ في حفظ السجلات:', error);
        }
    }

    // إعداد معالجات الأخطاء العامة
    setupGlobalErrorHandlers() {
        // معالج الأخطاء العامة
        window.addEventListener('error', (event) => {
            this.logError('JavaScript Error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : null
            });
        });

        // معالج الأخطاء غير المعالجة في Promise
        window.addEventListener('unhandledrejection', (event) => {
            this.logError('Unhandled Promise Rejection', {
                reason: event.reason,
                promise: event.promise
            });
        });

        // معالج أخطاء الموارد
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError('Resource Loading Error', {
                    element: event.target.tagName,
                    source: event.target.src || event.target.href,
                    message: 'فشل في تحميل المورد'
                });
            }
        }, true);
    }

    // إعداد مراقبة الأداء
    setupPerformanceMonitoring() {
        // مراقبة أداء التحميل
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                    if (loadTime > 5000) { // أكثر من 5 ثوان
                        this.logWarning('Performance', `تحميل بطيء للصفحة: ${loadTime}ms`);
                    }
                }
            }, 1000);
        });

        // مراقبة استخدام الذاكرة (إذا كان متوفراً)
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576);
                
                if (usedMB > limitMB * 0.8) { // أكثر من 80% من الحد الأقصى
                    this.logWarning('Memory', `استخدام ذاكرة عالي: ${usedMB}MB من ${limitMB}MB`);
                }
            }, 60000); // فحص كل دقيقة
        }
    }

    // تسجيل خطأ
    logError(component, details) {
        this.log('error', component, details);
    }

    // تسجيل تحذير
    logWarning(component, message) {
        this.log('warning', component, message);
    }

    // تسجيل معلومات
    logInfo(component, message) {
        this.log('info', component, message);
    }

    // تسجيل عام
    log(level, component, details) {
        const logEntry = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            level: level,
            component: component,
            details: details,
            userAgent: navigator.userAgent,
            url: window.location.href,
            sessionInfo: this.getSessionInfo()
        };

        this.logs.push(logEntry);

        // الاحتفاظ بآخر 1000 سجل فقط
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }

        // حفظ السجلات
        this.saveLogs();

        // عرض في Console
        this.displayInConsole(logEntry);

        // إرسال تنبيه للأخطاء الحرجة
        if (level === 'error') {
            this.handleCriticalError(logEntry);
        }
    }

    // عرض في Console
    displayInConsole(logEntry) {
        const emoji = this.getLevelEmoji(logEntry.level);
        const message = `${emoji} [${logEntry.component}] ${JSON.stringify(logEntry.details)}`;
        
        switch (logEntry.level) {
            case 'error':
                console.error(message);
                break;
            case 'warning':
                console.warn(message);
                break;
            case 'info':
                console.info(message);
                break;
            default:
                console.log(message);
        }
    }

    // معالجة الأخطاء الحرجة
    handleCriticalError(logEntry) {
        // عد الأخطاء في آخر 5 دقائق
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        const recentErrors = this.logs.filter(log => 
            log.level === 'error' && 
            new Date(log.timestamp) > fiveMinutesAgo
        );

        // إذا كان هناك أكثر من 5 أخطاء في 5 دقائق
        if (recentErrors.length > 5) {
            this.showCriticalErrorAlert();
        }
    }

    // إظهار تنبيه خطأ حرج
    showCriticalErrorAlert() {
        const alertDiv = document.createElement('div');
        alertDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 300px;
            font-family: Arial, sans-serif;
            direction: rtl;
        `;

        alertDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تحذير: أخطاء متعددة</strong>
            </div>
            <p style="margin: 0.5rem 0; font-size: 0.9rem;">
                تم اكتشاف عدة أخطاء. يرجى التواصل مع الدعم الفني.
            </p>
            <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
                <a href="tel:${this.supportInfo.phone}" style="
                    background: rgba(255,255,255,0.2); color: white; padding: 0.5rem;
                    border-radius: 4px; text-decoration: none; font-size: 0.8rem;
                ">📞 اتصال</a>
                <a href="https://wa.me/${this.supportInfo.whatsapp}" target="_blank" style="
                    background: rgba(255,255,255,0.2); color: white; padding: 0.5rem;
                    border-radius: 4px; text-decoration: none; font-size: 0.8rem;
                ">💬 واتساب</a>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: rgba(255,255,255,0.2); color: white; border: none;
                    padding: 0.5rem; border-radius: 4px; cursor: pointer; font-size: 0.8rem;
                ">✕ إغلاق</button>
            </div>
        `;

        document.body.appendChild(alertDiv);

        // إزالة التنبيه بعد 30 ثانية
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 30000);
    }

    // الحصول على معلومات الجلسة
    getSessionInfo() {
        try {
            const session = localStorage.getItem('userSession');
            if (session) {
                const sessionData = JSON.parse(session);
                return {
                    userType: sessionData.userType,
                    username: sessionData.username,
                    loginTime: sessionData.loginTime
                };
            }
        } catch (error) {
            // تجاهل الأخطاء
        }
        return null;
    }

    // الحصول على رمز المستوى
    getLevelEmoji(level) {
        const emojis = {
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            debug: '🐛'
        };
        return emojis[level] || '📝';
    }

    // إنشاء معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // الحصول على جميع السجلات
    getAllLogs() {
        return [...this.logs];
    }

    // الحصول على السجلات حسب المستوى
    getLogsByLevel(level) {
        return this.logs.filter(log => log.level === level);
    }

    // الحصول على السجلات حسب المكون
    getLogsByComponent(component) {
        return this.logs.filter(log => log.component === component);
    }

    // الحصول على السجلات في فترة زمنية
    getLogsByTimeRange(startTime, endTime) {
        const start = new Date(startTime);
        const end = new Date(endTime);
        
        return this.logs.filter(log => {
            const logTime = new Date(log.timestamp);
            return logTime >= start && logTime <= end;
        });
    }

    // مسح السجلات
    clearLogs() {
        this.logs = [];
        this.saveLogs();
        console.log('🗑️ تم مسح جميع السجلات');
    }

    // تصدير السجلات
    exportLogs(format = 'json') {
        const exportData = {
            logs: this.logs,
            exportedAt: new Date().toISOString(),
            totalCount: this.logs.length,
            summary: this.generateSummary()
        };

        switch (format) {
            case 'json':
                return JSON.stringify(exportData, null, 2);
            case 'csv':
                return this.convertToCSV(this.logs);
            case 'txt':
                return this.convertToText(this.logs);
            default:
                throw new Error(`تنسيق غير مدعوم: ${format}`);
        }
    }

    // تحويل إلى CSV
    convertToCSV(logs) {
        if (logs.length === 0) return '';

        const headers = ['Timestamp', 'Level', 'Component', 'Details', 'URL'];
        const csvRows = [headers.join(',')];

        logs.forEach(log => {
            const row = [
                log.timestamp,
                log.level,
                log.component,
                `"${JSON.stringify(log.details).replace(/"/g, '""')}"`,
                log.url
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    }

    // تحويل إلى نص
    convertToText(logs) {
        return logs.map(log => 
            `[${log.timestamp}] ${log.level.toUpperCase()} - ${log.component}: ${JSON.stringify(log.details)}`
        ).join('\n');
    }

    // إنشاء ملخص
    generateSummary() {
        const summary = {
            total: this.logs.length,
            byLevel: {},
            byComponent: {},
            timeRange: {
                oldest: null,
                newest: null
            }
        };

        // تجميع حسب المستوى
        this.logs.forEach(log => {
            summary.byLevel[log.level] = (summary.byLevel[log.level] || 0) + 1;
            summary.byComponent[log.component] = (summary.byComponent[log.component] || 0) + 1;
        });

        // تحديد النطاق الزمني
        if (this.logs.length > 0) {
            summary.timeRange.oldest = this.logs[0].timestamp;
            summary.timeRange.newest = this.logs[this.logs.length - 1].timestamp;
        }

        return summary;
    }

    // إحصائيات سريعة
    getQuickStats() {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        const recentLogs = this.logs.filter(log => new Date(log.timestamp) > oneHourAgo);
        const dailyLogs = this.logs.filter(log => new Date(log.timestamp) > oneDayAgo);

        return {
            total: this.logs.length,
            lastHour: recentLogs.length,
            lastDay: dailyLogs.length,
            errors: this.logs.filter(log => log.level === 'error').length,
            warnings: this.logs.filter(log => log.level === 'warning').length
        };
    }
}

// إنشاء مثيل واحد للاستخدام العام
const errorLogger = new ErrorLogger();

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    errorLogger.initialize();
});

// تصدير للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ErrorLogger, errorLogger };
} else {
    window.ErrorLogger = ErrorLogger;
    window.errorLogger = errorLogger;
}
