const { app, BrowserWindow } = require('electron');

let mainWindow;

function createWindow() {
  console.log('إنشاء النافذة الرئيسية...');
  
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    // icon: 'assets/icon.png',
    show: true,
    autoHideMenuBar: true
  });

  // تحميل صفحة اختبار أساسية
  console.log('تحميل صفحة الاختبار...');
  mainWindow.loadFile('test-basic.html');

  // فتح أدوات المطور للتشخيص
  mainWindow.webContents.openDevTools();

  // معالجة الأخطاء
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('فشل في تحميل الصفحة:', errorDescription, 'URL:', validatedURL);
  });

  mainWindow.webContents.on('crashed', (event, killed) => {
    console.error('تعطل المحتوى:', { killed });
  });

  mainWindow.on('unresponsive', () => {
    console.error('النافذة لا تستجيب');
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// تشغيل التطبيق
app.whenReady().then(() => {
  console.log('التطبيق جاهز، إنشاء النافذة...');
  createWindow();
});

// إغلاق التطبيق
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

console.log('تم تحميل main.js المبسط');
