/* إعدادات عامة */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    /* ألوان الوضع الفاتح */
    --bg-color: #f8f9fa;
    --card-bg: #ffffff;
    --text-color: #2c3e50;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --sidebar-bg: #2c3e50;
    --sidebar-text: #ffffff;
    --header-bg: #ffffff;
    --header-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* الوضع المظلم */
body.dark-mode {
    --bg-color: #1a1a2e;
    --card-bg: #16213e;
    --text-color: #e6e6e6;
    --text-muted: #a0a0a0;
    --border-color: #444;
    --sidebar-bg: #0f3460;
    --sidebar-text: #ffffff;
    --header-bg: #16213e;
    --header-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    direction: rtl;
    overflow-x: hidden;
}

/* حاوي لوحة التحكم */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    transition: var(--transition);
    overflow-y: auto;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 700;
}

.logo i {
    font-size: 1.5rem;
    margin-left: 0.5rem;
    color: var(--warning-color);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--sidebar-text);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* التنقل في الشريط الجانبي */
.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    margin: 0.2rem 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition);
    border-right: 3px solid transparent;
}

.nav-item a:hover,
.nav-item.active a {
    background: rgba(255, 255, 255, 0.1);
    border-right-color: var(--warning-color);
}

.nav-item i {
    font-size: 1.1rem;
    margin-left: 0.8rem;
    width: 20px;
    text-align: center;
}

.badge {
    background: var(--accent-color);
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* فوتر الشريط الجانبي */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.admin-avatar {
    width: 40px;
    height: 40px;
    background: var(--warning-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.8rem;
}

.admin-details {
    display: flex;
    flex-direction: column;
}

.admin-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.admin-role {
    font-size: 0.8rem;
    opacity: 0.7;
}

.logout-btn {
    width: 100%;
    background: rgba(231, 76, 60, 0.2);
    color: var(--accent-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
    padding: 0.8rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logout-btn:hover {
    background: var(--accent-color);
    color: white;
}

.logout-btn i {
    margin-left: 0.5rem;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    transition: var(--transition);
}

.sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* الهيدر العلوي */
.top-header {
    background: var(--header-bg);
    padding: 1rem 2rem;
    box-shadow: var(--header-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    margin-left: 1rem;
    border-radius: 4px;
    transition: var(--transition);
    display: none;
}

.menu-toggle:hover {
    background: var(--border-color);
}

#pageTitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* إحصائيات الهيدر */
.header-stats {
    display: flex;
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.5rem 1rem;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* أزرار الهيدر */
.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

/* منطقة المحتوى */
.content-area {
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* هيدر الأقسام */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.section-header h2 {
    color: var(--text-color);
    font-size: 1.8rem;
    font-weight: 600;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* مجموعات الفلترة والبحث */
.filter-group select,
.search-group input {
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--card-bg);
    color: var(--text-color);
    font-size: 0.9rem;
}

.search-group {
    position: relative;
}

.search-group i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-group input {
    padding-right: 2.5rem;
    width: 250px;
}

/* شبكة الطلبات */
.requests-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.request-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    cursor: pointer;
}

.request-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.request-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.request-id {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.9rem;
}

.request-status {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.request-status.pending {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.request-status.approved {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
}

.request-status.rejected {
    background: rgba(220, 53, 69, 0.2);
    color: var(--accent-color);
}

.request-info {
    margin-bottom: 1rem;
}

.request-info h4 {
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.request-details {
    display: grid;
    gap: 0.3rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.request-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* الأزرار */
.btn {
    padding: 0.6rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #219a52;
}

.btn-danger {
    background: var(--accent-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-secondary {
    background: var(--text-muted);
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

/* جدول التراخيص */
.licenses-table-container {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.licenses-table {
    width: 100%;
    border-collapse: collapse;
}

.licenses-table th,
.licenses-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.licenses-table th {
    background: var(--light-color);
    color: var(--text-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.licenses-table td {
    color: var(--text-color);
    font-size: 0.9rem;
}

.licenses-table tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* شبكة الإحصائيات */
.analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.analytics-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--light-color);
}

.card-header h3 {
    color: var(--text-color);
    font-weight: 600;
}

.card-content {
    padding: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.stat-box {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.2rem;
    color: white;
}

.stat-icon.requests { background: var(--info-color); }
.stat-icon.licenses { background: var(--success-color); }
.stat-icon.expired { background: var(--accent-color); }

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
}

.stat-text {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* إعدادات النظام */
.settings-container {
    max-width: 600px;
}

.settings-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.settings-card h3 {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    color: var(--text-color);
    font-weight: 500;
}

.setting-item input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--card-bg);
    color: var(--text-color);
}

.settings-actions {
    display: flex;
    gap: 1rem;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 700px;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    color: var(--text-color);
    font-weight: 600;
}

.close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0.5rem;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* نماذج الإدخال */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--card-bg);
    color: var(--text-color);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .header-stats {
        display: none;
    }
    
    .section-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .requests-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
